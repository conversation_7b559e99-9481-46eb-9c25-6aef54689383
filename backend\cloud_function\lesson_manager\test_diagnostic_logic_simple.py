#!/usr/bin/env python3
"""
Simple test to validate the diagnostic progression logic without requiring full server startup.
This tests the specific logic changes made to fix the diagnostic_start_probe stuck issue.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_diagnostic_start_probe_logic():
    """Test the diagnostic_start_probe progression logic"""
    print("Testing diagnostic_start_probe progression logic...")
    
    # Simulate the context and variables that would be present in the main function
    context = {
        'diagnostic_start_probe_interaction_count': 0
    }
    
    lesson_phase_from_context = 'diagnostic_start_probe'
    current_probing_level_number_for_prompt = 5
    request_id = 'test_request'
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'First interaction - system message',
            'user_query': 'Start diagnostic assessment',
            'expected_phase': 'diagnostic_start_probe',
            'expected_count': 0
        },
        {
            'name': 'Student ready response',
            'user_query': "I'm ready",
            'expected_phase': 'diagnostic_probing_L5_ask_q1',
            'expected_count': 1
        },
        {
            'name': 'Second student response',
            'user_query': "Yes, let's start",
            'expected_phase': 'diagnostic_probing_L5_ask_q1',
            'expected_count': 2
        },
        {
            'name': 'Third student response - should trigger forced progression',
            'user_query': "I want to learn",
            'expected_phase': 'diagnostic_probing_L5_ask_q1',
            'expected_count': 3,
            'should_force': True
        }
    ]
    
    all_tests_passed = True
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\nTest {i}: {scenario['name']}")
        print(f"User query: '{scenario['user_query']}'")
        
        user_query = scenario['user_query']
        
        # Simulate the logic from the main function
        if lesson_phase_from_context == 'diagnostic_start_probe':
            # Check if this is the first interaction or system message
            if user_query.strip() in ['Start diagnostic assessment', 'Start diagnostic assessment...'] or '[System:' in user_query:
                # First interaction or system message - stay for introduction
                python_calculated_new_phase_for_block = lesson_phase_from_context
                print(f"[{request_id}] DIAGNOSTIC PROGRESSION: First/system interaction - staying in diagnostic_start_probe for introduction")
            else:
                # Any student response to diagnostic_start_probe should progress to Q1
                user_response = user_query.strip().lower()
                
                # Check for stuck diagnostic_start_probe and force progression
                diagnostic_start_probe_interaction_count = context.get('diagnostic_start_probe_interaction_count', 0)
                
                if (len(user_response) > 0 and 
                    not user_response.startswith('[system') and
                    user_response not in ['start diagnostic assessment', 'start diagnostic assessment...']):
                    
                    # Increment interaction counter for diagnostic_start_probe
                    diagnostic_start_probe_interaction_count += 1
                    context['diagnostic_start_probe_interaction_count'] = diagnostic_start_probe_interaction_count
                    
                    # Student has responded - calculate next phase
                    python_calculated_new_phase_for_block = f"diagnostic_probing_L{current_probing_level_number_for_prompt}_ask_q1"
                    
                    # Force progression after 3+ student interactions in diagnostic_start_probe
                    if diagnostic_start_probe_interaction_count >= 3:
                        print(f"[{request_id}] 🚨 DIAGNOSTIC START PROBE STUCK: {diagnostic_start_probe_interaction_count} interactions - FORCING progression to Q1")
                        context['diagnostic_start_probe_forced_progression'] = True
                        context['diagnostic_start_probe_forced_reason'] = f'Stuck after {diagnostic_start_probe_interaction_count} interactions'
                    else:
                        print(f"[{request_id}] DIAGNOSTIC PROGRESSION: Student responded '{user_response}' (interaction {diagnostic_start_probe_interaction_count}) - AI should transition to {python_calculated_new_phase_for_block}")
                    
                    # Reset question index to 0 for first question
                    context['current_question_index'] = 0
                    context['current_question_index_from_state'] = 0
                    
                    print(f"[{request_id}] NATURAL PROGRESSION: Trusting AI to handle phase transition with state update block")
                else:
                    # Stay in start_probe for system messages or empty responses
                    python_calculated_new_phase_for_block = lesson_phase_from_context
                    print(f"[{request_id}] DIAGNOSTIC PROGRESSION: System/empty response - staying in diagnostic_start_probe")
        
        # Validate results
        actual_phase = python_calculated_new_phase_for_block
        actual_count = context.get('diagnostic_start_probe_interaction_count', 0)
        forced_progression = context.get('diagnostic_start_probe_forced_progression', False)
        
        print(f"Expected phase: {scenario['expected_phase']}")
        print(f"Actual phase: {actual_phase}")
        print(f"Expected count: {scenario['expected_count']}")
        print(f"Actual count: {actual_count}")
        
        # Check results
        phase_correct = actual_phase == scenario['expected_phase']
        count_correct = actual_count == scenario['expected_count']
        force_correct = forced_progression == scenario.get('should_force', False)
        
        if phase_correct and count_correct and force_correct:
            print("✅ PASS")
        else:
            print("❌ FAIL")
            if not phase_correct:
                print(f"  Phase mismatch: expected {scenario['expected_phase']}, got {actual_phase}")
            if not count_correct:
                print(f"  Count mismatch: expected {scenario['expected_count']}, got {actual_count}")
            if not force_correct:
                print(f"  Force progression mismatch: expected {scenario.get('should_force', False)}, got {forced_progression}")
            all_tests_passed = False
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - Diagnostic progression logic is working correctly!")
        return True
    else:
        print("💥 SOME TESTS FAILED - Diagnostic progression logic needs fixes")
        return False

def main():
    """Main test function"""
    print("Diagnostic Start Probe Logic Test")
    print("="*50)
    
    success = test_diagnostic_start_probe_logic()
    
    if success:
        print("\n✅ Diagnostic progression fix validation PASSED")
        return True
    else:
        print("\n❌ Diagnostic progression fix validation FAILED")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
