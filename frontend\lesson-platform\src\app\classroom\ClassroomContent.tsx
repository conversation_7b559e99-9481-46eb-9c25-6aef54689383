'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation'; 
import TutorChat from '@/components/lesson-components/TutorChat';
import { useLessonTimer } from '@/hooks/useLessonTimer';
import type { ChatMessage } from '@/components/lesson-components/LessonChat';
import { useSession } from '@/hooks/useSessionSimple'; // Using simplified session hook
import useInteractionLogger from '@/hooks/use-interaction-logger';
import { useToast } from '@/app/providers/ClientToastWrapper';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/shadcn/card";
import { Button } from "@/components/shadcn/button";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import ErrorDisplay from "@/components/ui/ErrorDisplay";
import LessonHeader from "@/components/shadcn/LessonHeader";
import { formatGradeLevelForDisplay } from '@/lib/utils';
import ErrorBoundary from '@/components/lesson-components/ErrorBoundary';
import LevelAdjustmentHistory from '@/components/lesson-components/LevelAdjustmentHistory';
import { DiagnosticProgress } from '@/components/DiagnosticProgress';
import axios, { AxiosError } from 'axios'; // Import AxiosError

const AI_INTERACTION_ENDPOINT = '/api/enhance-content'; // Next.js API proxy for Flask's /api/enhance-content
const LESSON_PHASE_COMPLETED = "completed";
const LESSON_DURATION_MINUTES = 45; // Total lesson duration in minutes

// Fallback Component for ErrorBoundary
const ErrorBoundaryFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void; }) => (
    <div className="p-4">
        <Card className="border-destructive">
            <CardHeader><CardTitle className="text-destructive">Rendering Error</CardTitle></CardHeader>
            <CardContent>
                <p>An unexpected error occurred while rendering this part of the lesson.</p>
                <pre className="mt-2 p-2 bg-red-50 text-red-700 rounded text-xs overflow-auto">{error.message}\n{error.stack}</pre>
                <Button onClick={resetErrorBoundary} className="mt-4">Try to Recover</Button>
            </CardContent>
        </Card>
    </div>
);

// Props expected from the parent page component (e.g., app/classroom/page.tsx)
interface ClassroomContentProps {
    sessionIdFromUrlProp: string; // Session ID from URL parameter
    lessonRefProp: string;
    studentIdProp: string; 
    countryProp?: string;
    curriculumProp?: string;
    gradeProp?: string;
    levelProp?: string;
    subjectProp?: string;
}

const ClassroomContent = ({
    sessionIdFromUrlProp, // Session ID from URL
    lessonRefProp, studentIdProp,
    countryProp, curriculumProp, gradeProp, levelProp, subjectProp
}: ClassroomContentProps): React.ReactElement => {
    const router = useRouter();
    const logInteraction = useInteractionLogger();
    const { toast } = useToast();
    
    const { 
        user, // NextAuth user object
        isReady,   // Indicates if useSession hook has initialized
        getAuthHeaders,
        backendSessionId // THE Firestore-backed lesson session ID from context
    } = useSession();

    const chatBottomRef = useRef<HTMLDivElement>(null);
    const initialAiInteractionSentRef = useRef(false); // Tracks if the first system message has been sent
    const handleAiInteractionRef = useRef<Function | null>(null); // Ref to store handleAiInteraction
    
    const [currentLessonPhase, setCurrentLessonPhase] = useState<string | null>(null); 
    const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
    const [isAiLoading, setIsAiLoading] = useState(false); // For AI response loading
    const [isPageLoading, setIsPageLoading] = useState(true); // Overall page/initial setup loading
    const [uiError, setUiError] = useState<string | null>(null); // For displaying errors in the UI
    
    // Level tracking state for real-time adjustments
    const [currentTeachingLevel, setCurrentTeachingLevel] = useState<number | null>(null);
    const [levelAdjustmentHistory, setLevelAdjustmentHistory] = useState<Array<{
        timestamp: number;
        direction: 'up' | 'down';
        fromLevel: number;
        toLevel: number;
        confidence: number;
    }>>([]);
    
    // Diagnostic progress tracking state
    const [diagnosticProgress, setDiagnosticProgress] = useState({
        currentQuestionIndex: 0,
        totalQuestions: 5, // Diagnostic system uses 5 questions
        currentProbingLevel: 5, // Default starting level
        questionsCompleted: 0,
        isComplete: false
    });
    
    // Timer state
    const [lessonStartTime, setLessonStartTime] = useState<Date | null>(null);
    
    // Timer logic moved to custom hook
    const {
        timeRemaining,
        isTimerActive,
        setIsTimerActive,
        formatTime,
        startTimer,
        getTimerStatus
    } = useLessonTimer({
        lessonStartTime,
        currentLessonPhase,
        sessionIdFromUrlProp,
        backendSessionId: backendSessionId || undefined,
        lessonRef: lessonRefProp,
        onTimeUp: () => {
            // This will be called when time is up
            const sessionId = sessionIdFromUrlProp || backendSessionId;
            if (sessionId) {
                handleAiInteraction('[System: Time is up! Completing lesson...]', true, sessionId);
            }
        },
        onQuizTransition: () => {
            // This will be called when forced quiz transition is triggered at 37.5 minutes
            const sessionId = sessionIdFromUrlProp || backendSessionId;
            if (sessionId) {
                logInteraction('forced_quiz_transition_triggered', {
                    lessonRef: lessonRefProp,
                    sessionId: sessionId,
                    currentPhase: currentLessonPhase || 'teaching',
                    timeRemaining: timeRemaining,
                    uncoveredContent: 'Material not covered due to time constraints will be added to homework'
                });
                
                // Send system message to AI to trigger quiz and capture uncovered content as homework
                handleAiInteraction('[System: Time limit approaching - transition to quiz phase and capture any uncovered teaching material as homework assignments]', true, sessionId);
            }
        }
    });

    const [lessonDetails, setLessonDetails] = useState<{
        title: string;
        subject: string;
        grade: string;
        level: string;
        country: string;
        curriculum: string;
        lessonRef: string; 
    } | null>(null);

    // Chat and lesson state
    const [messages, setMessages] = useState<Array<{ role: 'user' | 'assistant'; content: string }>>([]);
    const [loading, setLoading] = useState(false);
    const [lessonEnded, setLessonEnded] = useState(false);

    // Derive core identifiers from props
    const lessonRef = lessonRefProp;
    const studentId = studentIdProp; // This should be the Firebase UID of the student

    // Handle lesson phase updates from server responses
    const handleLessonPhaseUpdates = useCallback((response: any) => {
        try {
            console.log('[handleLessonPhaseUpdates] ==> ENHANCED DEBUGGING - PROCESSING RESPONSE FROM BACKEND');
            console.log('[handleLessonPhaseUpdates] ==> FULL RAW RESPONSE:', JSON.stringify(response, null, 2));
            console.log('[handleLessonPhaseUpdates] Raw response keys:', Object.keys(response || {}));
            console.log('[handleLessonPhaseUpdates] Response.data keys:', Object.keys(response?.data || {}));
            
            // FORCE CONSOLE OUTPUT FOR REAL-TIME DEBUGGING
            console.warn('🔥 FRONTEND PHASE UPDATE DEBUG:', {
                responseType: typeof response,
                hasData: !!response?.data,
                dataKeys: response?.data ? Object.keys(response.data) : 'no data',
                currentFrontendPhase: currentLessonPhase
            });
            
            // CRITICAL FIX: Frontend defensive parsing to handle multiple backend field names
            // Check multiple possible field locations for state updates
            const serverStateUpdates = response?.data?.state_updates || 
                                     response?.data?.parsed_state || 
                                     response?.state_updates || 
                                     response?.parsed_state;
            let phaseFromServer = response?.data?.current_phase || response?.current_phase; // From the main response body
            let diagCompleteFromServer = response?.data?.diagnostic_complete;
            let assessedLevelFromServer = response?.data?.assessed_level;

            // ENHANCED: Check if this is a wrapped response (Flask returns {success: true, data: {...}})
            if (response?.success && response?.data) {
                console.log('[handleLessonPhaseUpdates] 🎯 DETECTED WRAPPED RESPONSE from Flask');
                const innerData = response.data;
                
                // CRITICAL FIX: Backend returns current_phase at top level of data, not just in state_updates
                // Priority order: state_updates.new_phase > data.current_phase > data.new_phase
                phaseFromServer = innerData.state_updates?.new_phase || 
                                innerData.current_phase || 
                                innerData.new_phase ||
                                phaseFromServer;
                                
                diagCompleteFromServer = innerData.diagnostic_complete ||
                                       innerData.diagnostic_completed_this_session ||
                                       innerData.state_updates?.diagnostic_completed_this_session ||
                                       diagCompleteFromServer;
                                       
                assessedLevelFromServer = innerData.assessed_level ||
                                        innerData.assigned_level_for_teaching ||
                                        innerData.state_updates?.assigned_level_for_teaching ||
                                        assessedLevelFromServer;
                
                console.log('[handleLessonPhaseUpdates] 🎯 EXTRACTED FROM WRAPPED RESPONSE:', {
                    phaseFromServer,
                    diagCompleteFromServer,
                    assessedLevelFromServer,
                    hasStateUpdates: !!innerData.state_updates,
                    innerDataKeys: Object.keys(innerData)
                });
                
                // ENHANCED: Force console output to show what we found
                console.warn('🔥 PHASE EXTRACTION DEBUG:', {
                    'innerData.current_phase': innerData.current_phase,
                    'innerData.new_phase': innerData.new_phase,
                    'innerData.state_updates?.new_phase': innerData.state_updates?.new_phase,
                    'final_phaseFromServer': phaseFromServer
                });
            }

            // Debug logging to track field locations
            console.log('[handleLessonPhaseUpdates] DEBUG: Checking state update fields:', {
                'response.data.state_updates': !!response?.data?.state_updates,
                'response.data.parsed_state': !!response?.data?.parsed_state,
                'response.state_updates': !!response?.state_updates,
                'response.parsed_state': !!response?.parsed_state,
                'final_serverStateUpdates': !!serverStateUpdates,
                'phaseFromServer': phaseFromServer,
                'currentFrontendPhase': currentLessonPhase
            });

            if (serverStateUpdates && typeof serverStateUpdates === 'object') {
                console.log('[handleLessonPhaseUpdates] Found state updates:', Object.keys(serverStateUpdates));
                console.log('[handleLessonPhaseUpdates] FULL state updates object:', JSON.stringify(serverStateUpdates, null, 2));
                
                if (serverStateUpdates.new_phase) {
                    phaseFromServer = serverStateUpdates.new_phase;
                    console.log(`[handleLessonPhaseUpdates] 🔄 PHASE TRANSITION DETECTED: ${phaseFromServer}`);
                    console.log(`[handleLessonPhaseUpdates] 🔄 Previous phase: ${currentLessonPhase}`);
                    console.log(`[handleLessonPhaseUpdates] 🔄 New phase: ${phaseFromServer}`);
                    
                    // FORCE IMMEDIATE CONSOLE ALERT
                    console.warn(`🚀 CRITICAL: PHASE CHANGING FROM ${currentLessonPhase} TO ${phaseFromServer}`);
                }
                if (serverStateUpdates.diagnostic_completed_this_session !== undefined) {
                    diagCompleteFromServer = serverStateUpdates.diagnostic_completed_this_session;
                    console.log(`[handleLessonPhaseUpdates] 📊 Diagnostic completion status: ${diagCompleteFromServer}`);
                }
                if (serverStateUpdates.assigned_level_for_teaching !== undefined) {
                    assessedLevelFromServer = serverStateUpdates.assigned_level_for_teaching;
                    console.log(`[handleLessonPhaseUpdates] 🎯 Teaching level assigned: ${assessedLevelFromServer}`);
                }
                
                // Extract diagnostic-specific information for UI display
                if (serverStateUpdates.current_probing_level_number) {
                    console.log(`[handleLessonPhaseUpdates] 📋 Current probing level: ${serverStateUpdates.current_probing_level_number}`);
                    setDiagnosticProgress(prev => ({
                        ...prev,
                        currentProbingLevel: serverStateUpdates.current_probing_level_number
                    }));
                }
                if (serverStateUpdates.current_question_index !== undefined) {
                    console.log(`[handleLessonPhaseUpdates] ❓ Current question index: ${serverStateUpdates.current_question_index}`);
                    console.log(`[handleLessonPhaseUpdates] ❓ Updating diagnostic progress with question index: ${serverStateUpdates.current_question_index}`);
                    setDiagnosticProgress(prev => {
                        const updated = {
                            ...prev,
                            currentQuestionIndex: serverStateUpdates.current_question_index,
                            questionsCompleted: serverStateUpdates.current_question_index // Update both for consistency
                        };
                        console.log(`[handleLessonPhaseUpdates] ❓ Diagnostic progress after question index update:`, updated);
                        return updated;
                    });
                }
                if (serverStateUpdates.diagnostic_questions_completed) {
                    console.log(`[handleLessonPhaseUpdates] ✅ Questions completed: ${serverStateUpdates.diagnostic_questions_completed}`);
                    console.log(`[handleLessonPhaseUpdates] ✅ Updating diagnostic progress with completed questions`);
                    setDiagnosticProgress(prev => {
                        const updated = {
                            ...prev,
                            questionsCompleted: serverStateUpdates.diagnostic_questions_completed
                        };
                        console.log(`[handleLessonPhaseUpdates] ✅ Diagnostic progress after completion update:`, updated);
                        return updated;
                    });
                }
                if (serverStateUpdates.diagnostic_completed_this_session) {
                    console.log(`[handleLessonPhaseUpdates] 🎉 Diagnostic phase completed!`);
                    console.log(`[handleLessonPhaseUpdates] 🎉 Marking diagnostic as complete in UI state`);
                    setDiagnosticProgress(prev => {
                        const updated = {
                            ...prev,
                            isComplete: true
                        };
                        console.log(`[handleLessonPhaseUpdates] 🎉 Final diagnostic progress state:`, updated);
                        return updated;
                    });
                }
            }
            
            if (phaseFromServer) {
                console.log('[handleLessonPhaseUpdates] 🎯 UPDATING LESSON PHASE TO:', phaseFromServer);
                console.log('[handleLessonPhaseUpdates] 🎯 Previous lesson phase was:', currentLessonPhase);

                // FORCE IMMEDIATE STATE UPDATE WITH DEBUGGING
                setCurrentLessonPhase(prevPhase => {
                    console.warn(`🔥 STATE UPDATE: PHASE CHANGING ${prevPhase} → ${phaseFromServer}`);
                    return phaseFromServer;
                });

                // CRITICAL FIX: Handle diagnostic_start_probe phase properly
                if (phaseFromServer === 'diagnostic_start_probe') {
                    console.log('[handleLessonPhaseUpdates] 🔍 DIAGNOSTIC START PROBE: Setting question index to 0');
                    setDiagnosticProgress(prev => ({
                        ...prev,
                        currentQuestionIndex: 0,
                        questionsCompleted: 0,
                        isComplete: false
                    }));
                }

                console.log('[handleLessonPhaseUpdates] 🎯 Phase update completed');

                // ADDITIONAL: Force a re-render by updating a dummy state
                setTimeout(() => {
                    console.warn(`🔥 POST-UPDATE CHECK: currentLessonPhase should now be ${phaseFromServer}`);
                }, 100);
            } else {
                console.warn('[handleLessonPhaseUpdates] ⚠️ NO PHASE UPDATE FOUND IN RESPONSE');
                console.warn('[handleLessonPhaseUpdates] ⚠️ Response structure might be different than expected');
            }
            
            if (diagCompleteFromServer !== undefined) {
                console.log('[handleLessonPhaseUpdates] Diagnostic complete status:', diagCompleteFromServer);
                // Potentially set a local state like setIsDiagnosticComplete(diagCompleteFromServer);
            }
            if (assessedLevelFromServer !== undefined) {
                console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);
                // Potentially set a local state like setTeachingLevel(assessedLevelFromServer);
            }

            if (phaseFromServer === LESSON_PHASE_COMPLETED) {
                console.log('Lesson completed successfully (from phase update)');
                setLessonEnded(true);
                // Potentially show download link for notes if response.data.notes_download_url exists
                if (response?.data?.notes_download_url) {
                    // Show a button or link
                }
            }
            // ... other phase-specific UI logic ...

            // COMPREHENSIVE SUMMARY LOGGING
            console.log('[handleLessonPhaseUpdates] ==> SUMMARY OF STATE UPDATES:');
            console.log('[handleLessonPhaseUpdates] Phase changed:', phaseFromServer ? `${currentLessonPhase} → ${phaseFromServer}` : 'No change');
            console.log('[handleLessonPhaseUpdates] Diagnostic complete:', diagCompleteFromServer);
            console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);
            console.log('[handleLessonPhaseUpdates] Current diagnostic progress:', diagnosticProgress);
            console.log('[handleLessonPhaseUpdates] ==> END OF PROCESSING');

        } catch (error) {
            console.error('Error handling lesson phase update:', error);
            console.error('Error details:', error);
            setUiError('Failed to synchronize lesson state. Please try again.');
        }
    }, [setCurrentLessonPhase, setLessonEnded, setUiError, currentLessonPhase, diagnosticProgress]);

    // Helper function to extract enhanced content from AI responses
    const extractEnhancedContent = (response: any): string => {
        if (response?.data?.enhanced_content) {
            return response.data.enhanced_content;
        }
        if (response?.enhanced_content) {
            return response.enhanced_content;
        }
        if (response?.data?.data?.enhanced_content) {
            return response.data.data.enhanced_content;
        }
        
        const findContent = (obj: any): string | null => {
            if (typeof obj === 'string') return obj;
            if (Array.isArray(obj)) {
                for (const item of obj) {
                    const found = findContent(item);
                    if (found) return found;
                }
            } else if (obj && typeof obj === 'object') {
                for (const key in obj) {
                    if (Object.prototype.hasOwnProperty.call(obj, key)) {
                        const found = findContent(obj[key]);
                        if (found) return found;
                    }
                }
            }
            return null;
        };
        
        return findContent(response) || "I'm sorry, I couldn't process that response. Please try again.";
    };

    // Helper function to get user-friendly error messages
    const getErrorMessage = (error: any): string => {
        if (axios.isAxiosError(error)) {
            const axiosError = error as AxiosError;
            if (axiosError.response) {
                // Server responded with a status code outside 2xx
                if (axiosError.response.status === 401) {
                    return "Your session has expired. Please log in again.";
                }
                if (axiosError.response.status === 403) {
                    return "You don't have permission to perform this action.";
                }
                if (axiosError.response.status === 404) {
                    return "The requested resource was not found.";
                }
                if (axiosError.response.status === 429) {
                    return "Too many requests. Please wait a moment and try again.";
                }
                if (axiosError.response.status >= 500) {
                    return "Our servers are experiencing issues. Please try again later.";
                }
                return (axiosError.response.data as any)?.message || axiosError.message;
            }
            if (axiosError.request) {
                // Request was made but no response received
                return "No response from server. Please check your internet connection.";
            }
        }
        
        // Handle other error types
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        return "An unknown error occurred. Please try again.";
    };



    // Define handleAiInteraction first to avoid initialization issues
    const handleAiInteraction = useCallback(async (messageContent: string, isSystemMessage = false, currentSessionIdForCall?: string): Promise<boolean> => {
        // Priority: 1. Explicitly provided ID 2. URL session ID 3. Context session ID
        const sessionIdToUse = currentSessionIdForCall || sessionIdFromUrlProp || backendSessionId; 
        
        const logContext = `[ClassroomContent handleAiInteraction] isSystem: ${isSystemMessage}, SessionToUse: ${sessionIdToUse}, LessonRef: ${lessonRef}, StudentId: ${studentId}`;
        console.log(logContext);
        
        // Input validation
        if (typeof sessionIdToUse !== 'string' || !sessionIdToUse.trim()) {
            const error = `Session ID is invalid ('${sessionIdToUse}') from context/prop.`;
            setUiError(error);
            console.error(logContext, "CRITICAL ERROR:", error);
            toast({ 
                title: "Session Error", 
                description: "A valid session ID is required to continue the lesson. Please try refreshing.", 
                variant: "destructive" 
            });
            setIsAiLoading(false);
            return false;
        }
        if (!lessonRef || !studentId) {
            const error = "Lesson Reference or Student ID prop missing.";
            setUiError(error);
            console.error(logContext, "CRITICAL ERROR:", error);
            toast({ 
                title: "Configuration Error", 
                description: error, 
                variant: "destructive"
            });
            setIsAiLoading(false);
            return false;
        }

        setIsAiLoading(true);
        setUiError(null);
        logInteraction(
            isSystemMessage ? 'system_message_ai' : 'user_message_ai', 
            { 
                lessonRef, 
                sessionId: sessionIdToUse, 
                message: messageContent.substring(0, 100) 
            }
        );

        // Add user message to chat history if not a system message
        if (!isSystemMessage) {
            const currentUserMessage: ChatMessage = { 
                role: 'user', 
                content: messageContent, 
                timestamp: new Date().toISOString() 
            };
            console.log(`[ClassroomContent] 👤 Adding user message to chat:`, {
                messagePreview: messageContent.substring(0, 50) + '...',
                messageLength: messageContent.length,
                currentPhase: currentLessonPhase,
                chatHistoryLength: chatHistory.length
            });
            setChatHistory(prev => [...prev, currentUserMessage]);
        }

        try {
            const authHeaders = getAuthHeaders(backendSessionId);
            if (!authHeaders['Authorization']) {
                throw new Error("Authentication token unavailable for AI interaction.");
            }

            const requestBody = {
                student_id: studentId,
                lesson_ref: lessonRef,
                content_to_enhance: messageContent,
                country: countryProp || 'Nigeria', 
                curriculum: curriculumProp || 'National Curriculum', 
                grade: gradeProp, 
                level: levelProp, 
                subject: subjectProp, 
                session_id: sessionIdToUse, 
                chat_history: isSystemMessage ? [] : chatHistory.slice(-8)
            };
            
            console.log("[ClassroomContent] AI Interaction Request:", {
                endpoint: AI_INTERACTION_ENDPOINT,
                headers: { 'Content-Type': 'application/json' },
                body: { ...requestBody, chat_history: `[${requestBody.chat_history.length} messages]` }
            });

            const MAX_RETRIES = 2;
            let lastError;
            
            for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
                try {
                    const axiosResponse = await axios.post(AI_INTERACTION_ENDPOINT, requestBody, {
                        headers: { ...authHeaders, 'Content-Type': 'application/json' },
                        timeout: 90000,
                        validateStatus: () => true // Always resolve the promise
                    });

                    if (axiosResponse.status >= 200 && axiosResponse.status < 300) {
                        let result;
                        try {
                            result = typeof axiosResponse.data === 'string' 
                                ? JSON.parse(axiosResponse.data) 
                                : axiosResponse.data;
                            
                            console.log('[ClassroomContent] Successfully parsed response:', result);
                            
                            // CRITICAL DEBUG: Show exact response structure for phase sync debugging
                            console.warn('🔥 FRONTEND API RESPONSE STRUCTURE DEBUG:');
                            console.warn('🔥 Response keys:', Object.keys(result || {}));
                            console.warn('🔥 Response.data keys:', Object.keys(result?.data || {}));
                            console.warn('🔥 Response.data.state_updates keys:', Object.keys(result?.data?.state_updates || {}));
                            console.warn('🔥 Current phase in response.data:', result?.data?.current_phase);
                            console.warn('🔥 New phase in state_updates:', result?.data?.state_updates?.new_phase);
                            console.warn('🔥 Full result structure (first 500 chars):', JSON.stringify(result, null, 2).substring(0, 500));
                            
                            const enhancedContent = extractEnhancedContent(result);
                            
                            // Check for level adjustment notifications
                            if (result?.state_updates?.level_adjustment_made) {
                                const adjustment = result.state_updates.level_adjustment_made;
                                console.log('[ClassroomContent] Level adjustment detected:', adjustment);
                                
                                // Update current teaching level
                                setCurrentTeachingLevel(adjustment.to_level);
                                
                                // Add to adjustment history
                                setLevelAdjustmentHistory(prev => [
                                    ...prev,
                                    {
                                        timestamp: adjustment.timestamp,
                                        direction: adjustment.direction,
                                        fromLevel: adjustment.from_level,
                                        toLevel: adjustment.to_level,
                                        confidence: adjustment.confidence_score
                                    }
                                ].slice(-10)); // Keep only last 10 adjustments
                                
                                // Show level adjustment notification
                                toast({
                                    title: `Teaching Level ${adjustment.direction === 'up' ? 'Increased' : 'Decreased'}`,
                                    description: `I've adjusted the lesson difficulty from Level ${adjustment.from_level} to Level ${adjustment.to_level} to better match your learning pace.`,
                                    variant: adjustment.direction === 'up' ? 'default' : 'destructive',
                                    duration: 8000,
                                });
                                
                                // Log the level adjustment
                                logInteraction('level_adjustment_notification', {
                                    lessonRef,
                                    sessionId: sessionIdToUse,
                                    direction: adjustment.direction,
                                    fromLevel: adjustment.from_level,
                                    toLevel: adjustment.to_level,
                                    confidence: adjustment.confidence_score,
                                    reasoning: adjustment.reasoning,
                                    timestamp: new Date().toISOString()
                                });
                            }
                            
                            // Check for initial or updated teaching level in state updates
                            if (result?.state_updates?.assigned_level_for_teaching && currentTeachingLevel === null) {
                                setCurrentTeachingLevel(result.state_updates.assigned_level_for_teaching);
                            }
                            
                            const aiMessage: ChatMessage = {
                                role: 'assistant',
                                content: enhancedContent,
                                timestamp: new Date().toISOString()
                            };
                            
                            console.log(`[ClassroomContent] 💬 Adding AI message to chat history:`, {
                                contentPreview: enhancedContent.substring(0, 100) + '...',
                                messageLength: enhancedContent.length,
                                currentChatLength: chatHistory.length,
                                hasStateUpdates: !!result?.state_updates,
                                hasPhaseUpdate: !!(result?.state_updates?.new_phase || result?.data?.current_phase)
                            });
                            
                            setChatHistory(prev => {
                                const newHistory = [...prev, aiMessage];
                                console.log(`[ClassroomContent] 📝 Chat history updated. Total messages: ${newHistory.length}`);
                                return newHistory;
                            });

                            handleLessonPhaseUpdates(result);

                            return true;
                        } catch (parseError: any) {
                            throw new Error(`Failed to parse server response: ${parseError.message}`);
                        }
                    }

                    if (axiosResponse.status === 401) {
                        throw new Error('Authentication failed. Please log in again.');
                    } else if (axiosResponse.status === 429) {
                        const retryAfter = (axiosResponse.headers as any)['retry-after'] || 5;
                        if (attempt < MAX_RETRIES) {
                            await new Promise(resolve => setTimeout(resolve, Number(retryAfter) * 1000));
                            continue;
                        }
                        throw new Error('Server is busy. Please try again later.');
                    }

                    const errorMessage = (axiosResponse.data as any)?.message || 
                                       axiosResponse.statusText || 
                                       `Request failed with status ${axiosResponse.status}`;
                    throw new Error(errorMessage);

                } catch (error) {
                    lastError = error;
                    if (attempt < MAX_RETRIES) {
                        const backoffTime = Math.pow(2, attempt) * 1000;
                        console.warn(`Attempt ${attempt + 1} failed, retrying in ${backoffTime}ms...`, error);
                        await new Promise(resolve => setTimeout(resolve, backoffTime));
                    }
                }
            }

            throw lastError || new Error('Request failed after multiple attempts');

            // This code is unreachable due to the throw statement above
            // Keeping it for reference but it won't be executed
            console.warn('This code should not be reachable - check for unreachable code');
            return false;
        } catch (error: any) {
            const errorMessage = getErrorMessage(error);
            console.error("[ClassroomContent] Error during AI interaction:", errorMessage, "\nFull error:", error);
            
            // Update UI with error state
            setUiError(errorMessage);
            
            // Add error message to chat for better UX
            const errorMessageObj: ChatMessage = {
                role: 'assistant',
                content: `I'm sorry, I encountered an error: ${errorMessage}`,
                timestamp: new Date().toISOString(),
                status: 'error'
            };
            setChatHistory(prev => [...prev, errorMessageObj]);
            
            // Show toast for non-timeout errors to avoid duplicate messages
            if (!error.message?.includes('timeout') && !error.message?.includes('Network Error')) {
                toast({
                    title: "AI Service Error",
                    description: errorMessage,
                    variant: "destructive",
                    duration: 10000
                });
            }
            
            // Log the error for debugging
            logInteraction('ai_interaction_error', {
                lessonRef,
                sessionId: sessionIdToUse,
                error: errorMessage,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
            
            return false;
        } finally {
            // Always ensure loading state is reset
            setIsAiLoading(false);
        }
    }, [
        sessionIdFromUrlProp,
        backendSessionId || undefined, 
        lessonRef, studentId, 
        countryProp, curriculumProp, gradeProp, levelProp, subjectProp, 
        getAuthHeaders, toast, logInteraction, chatHistory,
        timeRemaining // Add timeRemaining to dependencies
    ]); 


    useEffect(() => {
        const effectRunId = Date.now(); // For unique logging per run
        console.log(`[ClassroomContent Effect #${effectRunId}] Running. Deps state:`, 
            { sessionIdFromUrlProp, backendSessionId, lessonRef, studentId, isReady, initialAiSent: initialAiInteractionSentRef.current, chatHistoryLength: chatHistory.length, isPageLoading });

        if (initialAiInteractionSentRef.current) {
            console.log(`[ClassroomContent Effect #${effectRunId}] Initial AI interaction already attempted/sent. Current page loading: ${isPageLoading}.`);
            // If setup was done and page is still loading, ensure it stops.
            if (isPageLoading) setIsPageLoading(false);
            return;
        }

        if (!isReady) {
            console.warn(`[ClassroomContent Effect #${effectRunId}] Waiting for context (ready: ${isReady}).`);
            if (!isPageLoading) setIsPageLoading(true); // Keep loading screen if waiting for these
            return;
        }
        
        // Once user session and context are ready, validate critical props and session ID
        if (!lessonRef || !studentId || !(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {
            let missingInfo: string[] = [];
            if (!lessonRef) missingInfo.push("lessonRefProp");
            if (!studentId) missingInfo.push("studentIdProp");
            if (!(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {
                missingInfo.push(`valid sessionIdFromUrlProp (received: '${sessionIdFromUrlProp}', type: ${typeof sessionIdFromUrlProp})`);
            }
            
            const errorMessage = `Critical info missing for ClassroomContent init: ${missingInfo.join(', ')}. Please ensure the lesson was started correctly.`;
            console.error(`[ClassroomContent Effect #${effectRunId}] Prerequisite check failed:`, errorMessage, 
                { lessonRefFromProp: lessonRef, studentIdFromProp: studentId, contextSessionId: backendSessionId });
            setUiError(errorMessage);
            toast({ title: "Lesson Load Error", description: errorMessage, variant: "destructive", duration: 10000 });
            setIsPageLoading(false);
            initialAiInteractionSentRef.current = true; // Mark as "attempted" to prevent loops
            return;
        }

        // All prerequisites are met: lessonRef, studentId from props, and sessionIdFromUrlProp are valid.
        // The initialSetupTriggeredRef is primarily to ensure the initial AI message is sent only once.
        
        console.log(`[ClassroomContent Effect #${effectRunId}] All prerequisites met. Using sessionIdFromUrlProp: '${sessionIdFromUrlProp}', lessonRef: '${lessonRef}'`);
        
        // Ensure isPageLoading is true before we potentially make an async call or set lessonDetails
        if(!isPageLoading) setIsPageLoading(true);

        if (!lessonDetails) {
            console.log(`[ClassroomContent Effect #${effectRunId}] Populating lessonDetails.`);
            setLessonDetails({
                title: `Lesson: ${lessonRef}`,
                subject: subjectProp || 'N/A',
                grade: gradeProp || 'N/A',
                level: levelProp || 'N/A',
                country: countryProp || 'N/A',
                curriculum: curriculumProp || 'N/A',
                lessonRef: lessonRef,
            });
        }
        
        // Send initial system message if chat is empty and it hasn't been sent yet
        if (chatHistory.length === 0 && !initialAiInteractionSentRef.current) {
            initialAiInteractionSentRef.current = true; 
            console.log(`[ClassroomContent Effect #${effectRunId}] Triggering initial AI interaction (Diagnostic Start Message) with sessionIdFromUrlProp: ${sessionIdFromUrlProp}`);
            
            // CRITICAL FIX: Send diagnostic-specific message instead of generic system message
            handleAiInteraction(`Start diagnostic assessment`, true, sessionIdFromUrlProp)
                .then(success => {
                    if (success) {
                        console.log(`[ClassroomContent Effect #${effectRunId}] Initial system message AI interaction completed successfully.`);
                    } else {
                        console.warn(`[ClassroomContent Effect #${effectRunId}] Initial system message AI interaction reported failure.`);
                    }
                })
                .catch(err => { 
                    console.error(`[ClassroomContent Effect #${effectRunId}] Promise rejected from initial AI interaction:`, err.message);
                })
                .finally(() => {
                    setIsPageLoading(false); 
                });
        } else {
            // Initial message already sent or chat not empty, just ensure loading is false
            console.log(`[ClassroomContent Effect #${effectRunId}] Initial AI message condition not met (chatHistory: ${chatHistory.length}, initialSentRef: ${initialAiInteractionSentRef.current}). Setting page loading false.`);
            setIsPageLoading(false);
        }

    }, [
        // Key dependencies that trigger re-evaluation of initial setup:
        sessionIdFromUrlProp, // Add to dependencies
        backendSessionId || undefined, 
        lessonRef, // from prop
        studentId, // from prop
        isReady,
        // Other dependencies that, if they change, might necessitate re-evaluation or are used:
        subjectProp, gradeProp, levelProp, countryProp, curriculumProp, 
        chatHistory.length, // To check if initial message needed
        handleAiInteraction, // Memoized
        lessonDetails,       // If details need to be set
        isPageLoading,       // To manage the loading flag
        toast                // Stable
    ]);


    useEffect(() => {
        chatBottomRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [chatHistory]);

    // DEBUG: Monitor phase changes to ensure state updates are working
    useEffect(() => {
        console.warn('🎯 PHASE CHANGE DETECTED:', {
            previousPhase: 'tracked separately',
            currentPhase: currentLessonPhase,
            timestamp: new Date().toISOString()
        });
        
        // Force console alert for phase changes
        if (currentLessonPhase) {
            console.warn(`🚀 FRONTEND PHASE IS NOW: ${currentLessonPhase}`);
        }
    }, [currentLessonPhase]);

    // --- Render Logic ---

    if (isPageLoading && !initialAiInteractionSentRef.current && !uiError) { 
        console.log("[ClassroomContent Render] Showing loading spinner:", { isPageLoading, initialAiSent: initialAiInteractionSentRef.current, uiError });
        return <div className="flex justify-center items-center h-screen"><LoadingSpinner size="large" /><p className="ml-2">Initializing Classroom...</p></div>;
    }
    
    if (uiError) { 
        console.log("[ClassroomContent Render] Showing UI error display:", uiError);
        return (
            <div className="flex flex-col justify-center items-center h-screen p-4">
                <ErrorDisplay title="Lesson Error" message={uiError} />
                <Button onClick={() => router.push('/dashboard')} className="mt-4">Go to Dashboard</Button>
            </div>
        );
    }
    
    const currentSessionId = sessionIdFromUrlProp || backendSessionId;
    if (!currentSessionId || !lessonRef || !studentId || !lessonDetails) {
        const missingRenderData = { 
            sessionIdFromUrl: sessionIdFromUrlProp, 
            contextSessionId: backendSessionId, 
            propLessonRef: lessonRef, 
            propStudentId: studentId, 
            currentLessonDetails: lessonDetails 
        };
        console.error("[ClassroomContent Render] Critical data missing just before render. This indicates a logic flow issue.", missingRenderData);
        return (
            <div className="flex flex-col justify-center items-center h-screen p-4">
                <ErrorDisplay title="Content Load Error" message={"Essential lesson data is still missing after initialization attempts. Please try returning to the dashboard and starting the lesson again."} />
                <Button onClick={() => router.push('/dashboard')} className="mt-4">Go to Dashboard</Button>
            </div>
        );
    }


// All checks passed, render the main classroom UI
return (
    <ErrorBoundary fallback={<ErrorBoundaryFallback error={new Error('Component error')} resetErrorBoundary={() => window.location.reload()} />}>
        <div className="flex flex-col h-screen bg-background text-foreground">
            <LessonHeader
                lessonTitle={lessonDetails.title}
                subjectName={lessonDetails.subject}
                gradeLevel={formatGradeLevelForDisplay(lessonDetails.grade)}
                currentTeachingLevel={currentTeachingLevel}
                levelAdjustmentHistory={levelAdjustmentHistory}
                onEnd={() => {
                    if (backendSessionId && typeof backendSessionId === 'string' && lessonRefProp && studentIdProp) {
                        logInteraction('lesson_ended_by_user', { 
                            lessonRef: lessonRefProp, 
                            sessionId: backendSessionId,
                            timeElapsed: LESSON_DURATION_MINUTES * 60 - timeRemaining,
                            timeRemaining
                        });
                    }
                    router.push('/dashboard');
                }}
                connectionStatus={
                    isAiLoading ? <LoadingSpinner size="small" /> :
                    currentLessonPhase === LESSON_PHASE_COMPLETED ? (
                        <span className="text-green-500 text-xs font-semibold">
                            Lesson Complete! {formatTime(timeRemaining)} remaining
                        </span>
                    ) :
                    uiError ? (
                        <span className="text-red-500 text-xs font-semibold">Error</span>
                    ) : (
                        <div className="flex items-center space-x-3">
                            <div className="flex flex-col">
                                <span className="text-blue-500 text-xs font-semibold">
                                    {formatTime(timeRemaining)} remaining
                                </span>
                                {(() => {
                                    const timerStatus = getTimerStatus();
                                    if (timerStatus.isInQuizPhase) {
                                        return <span className="text-amber-600 text-xs">Quiz Phase</span>;
                                    } else if (timerStatus.timeUntilQuizTransition <= 300) { // 5 minutes
                                        return <span className="text-orange-500 text-xs">Quiz in {formatTime(timerStatus.timeUntilQuizTransition)}</span>;
                                    }
                                    return null;
                                })()}
                            </div>
                            <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden relative">
                                {/* Main progress bar */}
                                <div 
                                    className={`h-full ${
                                        timeRemaining > (LESSON_DURATION_MINUTES * 60 * 0.5) ? 'bg-green-500' : 
                                        timeRemaining > (LESSON_DURATION_MINUTES * 60 * 0.25) ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{
                                        width: `${Math.max(5, (timeRemaining / (LESSON_DURATION_MINUTES * 60)) * 100)}%`
                                    }}
                                />
                                {/* Quiz transition marker at 37.5 minutes */}
                                <div 
                                    className="absolute top-0 w-0.5 h-full bg-amber-500 z-10"
                                    style={{
                                        left: `${(37.5 / 45) * 100}%`
                                    }}
                                    title="Quiz transition point (37.5 min)"
                                />
                            </div>
                        </div>
                    )
                }
            />
            <div className="flex flex-1 overflow-hidden">
                <aside className="hidden md:block w-64 lg:w-72 xl:w-1/4 p-4 border-r overflow-y-auto bg-slate-50 dark:bg-slate-800">
                    <Card className="shadow-md mb-4">
                        <CardHeader>
                            <CardTitle className="text-lg text-slate-700 dark:text-slate-200">Lesson Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-1.5 text-sm text-slate-600 dark:text-slate-300">
                            <p><strong>Topic:</strong> {lessonDetails.title}</p>
                            <p><strong>Subject:</strong> {lessonDetails.subject}</p>
                            <p><strong>Grade:</strong> {lessonDetails.grade}</p>
                            <p><strong>Curriculum:</strong> {lessonDetails.curriculum}</p>
                            <hr className="my-2 border-slate-200 dark:border-slate-700"/>
                            <p><strong>Phase:</strong> <span className="font-medium text-blue-600 dark:text-blue-400">{currentLessonPhase || 'Initializing...'}</span></p>
                            <p><strong>Session ID:</strong> <span className="text-xs text-gray-500">{sessionIdFromUrlProp || backendSessionId}</span></p>
                        </CardContent>
                    </Card>

                    {/* Diagnostic Progress Indicator */}
                    <DiagnosticProgress
                        currentQuestionIndex={diagnosticProgress.currentQuestionIndex}
                        totalQuestions={diagnosticProgress.totalQuestions}
                        currentProbingLevel={diagnosticProgress.currentProbingLevel}
                        questionsCompleted={diagnosticProgress.questionsCompleted}
                        currentPhase={currentLessonPhase}
                        isComplete={diagnosticProgress.isComplete}
                        className="mb-4"
                    />

                    {/* Level Adjustment History */}
                    {levelAdjustmentHistory.length > 0 && (
                        <LevelAdjustmentHistory
                            adjustments={levelAdjustmentHistory}
                            currentLevel={currentTeachingLevel}
                            className="mb-4"
                        />
                    )}
                     {/* Future: Add objectives, key concepts, progress indicator here */}
                </aside>
                <main className="flex-1 flex flex-col bg-white dark:bg-slate-900">
                    <TutorChat
                        lessonRef={lessonRef}
                        studentId={studentId}
                        sessionId={sessionIdFromUrlProp || backendSessionId || ''} 
                        chatMessages={chatHistory}
                        onSendMessage={handleAiInteraction}
                        isProcessing={isAiLoading}
                        error={uiError} // Pass UI error to TutorChat if it needs to display it
                        getAuthHeaders={getAuthHeaders}
                        lessonTitle={lessonDetails?.title}
                        role="instructor" // or "tutor"
                    />
                    <div ref={chatBottomRef} />
                </main>
            </div>
        </div>
    </ErrorBoundary>
);
};

export default ClassroomContent;