/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/enhance-content/route";
exports.ids = ["app/api/enhance-content/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_enhance_content_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/enhance-content/route.ts */ \"(rsc)/./src/app/api/enhance-content/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/enhance-content/route\",\n        pathname: \"/api/enhance-content\",\n        filename: \"route\",\n        bundlePath: \"app/api/enhance-content/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\enhance-content\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_enhance_content_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZlbmhhbmNlLWNvbnRlbnQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmVuaGFuY2UtY29udGVudCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmVuaGFuY2UtY29udGVudCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNwYyU1Q09uZURyaXZlJTVDRGVza3RvcCU1Q0Rlc2t0b3AlNUNTb2x5bnRhX1dlYnNpdGUlNUNmcm9udGVuZCU1Q2xlc3Nvbi1wbGF0Zm9ybSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDcGMlNUNPbmVEcml2ZSU1Q0Rlc2t0b3AlNUNEZXNrdG9wJTVDU29seW50YV9XZWJzaXRlJTVDZnJvbnRlbmQlNUNsZXNzb24tcGxhdGZvcm0maXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQytFO0FBQzVKO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxlbmhhbmNlLWNvbnRlbnRcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2VuaGFuY2UtY29udGVudC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2VuaGFuY2UtY29udGVudFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvZW5oYW5jZS1jb250ZW50L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcZW5oYW5jZS1jb250ZW50XFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/enhance-content/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/enhance-content/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n\n\n // Assuming you have a logger utility\n // Assuming you have a config utility for backend URL\nconst FLASK_API_ENDPOINT = '/api/enhance-content'; // The specific endpoint on your Flask backend\nasync function POST(request) {\n    const logPrefix = '[API Proxy /api/enhance-content]';\n    const frontendRequestId = request.headers.get('x-request-id') || crypto.randomUUID(); // Use incoming or generate\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Request received.`);\n    let requestBodyFromClient;\n    try {\n        requestBodyFromClient = await request.json();\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Parsed body from client:`, JSON.stringify(requestBodyFromClient).substring(0, 500) + \"...\");\n    } catch (e) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Error parsing JSON body from client:`, e.message);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Invalid JSON payload.'\n        }, {\n            status: 400\n        });\n    }\n    const flaskBackendUrl = ((0,_lib_config__WEBPACK_IMPORTED_MODULE_2__.getBackendUrl)() || process.env.FLASK_BACKEND_URL || 'http://localhost:5000').replace(/\\/$/, \"\");\n    const fullFlaskUrl = `${flaskBackendUrl}${FLASK_API_ENDPOINT}`;\n    // Prepare headers to forward to Flask\n    // Crucially, forward the Authorization header for Flask's @require_auth\n    const authorizationHeader = request.headers.get('Authorization');\n    const studentIdHeader = request.headers.get('X-Student-Id'); // If you use this custom header\n    const headersToFlask = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n    };\n    if (authorizationHeader) {\n        headersToFlask['Authorization'] = authorizationHeader;\n    }\n    // Add any other headers you need to pass, like X-Student-Id if Flask uses it\n    // For example, if your Flask's @require_auth gets student ID from a custom header:\n    // if (requestBodyFromClient.student_id) { // Or get from JWT if preferred on Flask side\n    //     headersToFlask['X-Student-Id'] = requestBodyFromClient.student_id;\n    // }\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Forwarding request to Flask: ${fullFlaskUrl}`);\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.debug(`${logPrefix} [${frontendRequestId}] Body to Flask:`, requestBodyFromClient);\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.debug(`${logPrefix} [${frontendRequestId}] Headers to Flask:`, headersToFlask);\n    try {\n        const flaskResponse = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(fullFlaskUrl, requestBodyFromClient, {\n            headers: headersToFlask,\n            timeout: 90000\n        });\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Response from Flask. Status: ${flaskResponse.status}.`);\n        // Enhanced logging for debugging\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Type of flaskResponse.data: ${typeof flaskResponse.data}`);\n        try {\n            const dataString = JSON.stringify(flaskResponse.data);\n            const dataPreview = dataString.substring(0, 1000);\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] flaskResponse.data (preview): ${dataPreview}${dataString.length > 1000 ? '...' : ''}`);\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Response size: ${dataString.length} bytes`);\n        } catch (e) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Could not stringify flaskResponse.data. Raw:`, flaskResponse.data);\n        }\n        if (flaskResponse.status >= 200 && flaskResponse.status < 300 && flaskResponse.data) {\n            const responseKeys = typeof flaskResponse.data === 'object' && flaskResponse.data !== null ? Object.keys(flaskResponse.data).join(', ') : 'non-object response';\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Forwarding FULL Flask data to client. Response keys: ${responseKeys}`);\n            // Forward Flask's response directly to the client\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(flaskResponse.data, {\n                status: flaskResponse.status\n            });\n        } else {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Invalid response from Flask:`, {\n                status: flaskResponse.status,\n                hasData: !!flaskResponse.data,\n                dataType: typeof flaskResponse.data\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid response from backend',\n                status: flaskResponse.status\n            }, {\n                status: 502\n            });\n        }\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Error proxying to Flask:`, error.message);\n        if (axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].isAxiosError(error)) {\n            const axiosError = error; // Type assertion\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Axios error details: Code: ${axiosError.code}, Status: ${axiosError.response?.status}`);\n            // logger.error(`${logPrefix} [${frontendRequestId}] Axios error response data:`, axiosError.response?.data);\n            const statusCode = axiosError.response?.status || 502; // 502 Bad Gateway if backend error\n            const flaskErrorMessage = axiosError.response?.data?.message || axiosError.message || 'Backend communication error.';\n            const flaskRequestId = axiosError.response?.data?.request_id || null;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: `Backend communication error: ${flaskErrorMessage}`,\n                details: axiosError.response?.data || null,\n                backendRequestId: flaskRequestId\n            }, {\n                status: statusCode\n            });\n        }\n        // For non-Axios errors during proxying\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Internal proxy error while communicating with backend.'\n        }, {\n            status: 500\n        });\n    }\n}\n// Optional: Add an OPTIONS handler for CORS preflight if needed\n// This might be necessary if your global CORS config in next.config.js isn't sufficient\n// or if you have specific header requirements for this route.\nasync function OPTIONS(request) {\n    const logPrefix = '[API Proxy /api/enhance-content]';\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} OPTIONS request received.`);\n    const responseHeaders = new Headers();\n    // Match these with what your Flask backend's CORS setup allows for the actual request\n    responseHeaders.set('Access-Control-Allow-Origin', request.headers.get('Origin') || '*'); // Or your specific frontend origin\n    responseHeaders.set('Access-Control-Allow-Methods', 'POST, OPTIONS');\n    responseHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Student-Id, X-Request-Id'); // Add any custom headers\n    responseHeaders.set('Access-Control-Allow-Credentials', 'true');\n    responseHeaders.set('Access-Control-Max-Age', '86400'); // Cache preflight for 1 day\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers: responseHeaders\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/enhance-content/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/env.ts":
/*!********************!*\
  !*** ./src/env.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env),\n/* harmony export */   publicEnv: () => (/* binding */ publicEnv)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n// File: env.ts\n\n/**\r\n * Environment variable schema definition\r\n * Add all environment variables your application uses here\r\n */ const baseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'debug',\n        'info',\n        'warn',\n        'error'\n    ]).default('info'),\n    PRETTY_PRINT: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'true',\n        'false'\n    ]).default('false').transform((val)=>val === 'true'),\n    SERVICE_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('education-platform'),\n    // Public API URL\n    NEXT_PUBLIC_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    // Public Firebase Config (Client-side) - Renamed to match convention\n    NEXT_PUBLIC_FIREBASE_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_PROJECT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_APP_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // Feature flags (can be public)\n    NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'true',\n        'false'\n    ]).default('true').transform((val)=>val === 'true'),\n    NEXT_PUBLIC_ENABLE_ENROLLMENT_API: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'true',\n        'false'\n    ]).default('true').transform((val)=>val === 'true')\n});\n// Server-side specific schema, extending the base\nconst serverSchema = baseSchema.extend({\n    // Firebase Admin (Server-side) - Kept original names as they are not public\n    FIREBASE_PROJECT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    FIREBASE_CLIENT_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(),\n    FIREBASE_PRIVATE_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(10)\n});\n// Client-side schema only includes the base (public) variables\nconst clientSchema = baseSchema;\n/**\r\n * Parse and validate environment variables\r\n * This will throw an error if required environment variables are missing\r\n */ function createEnv() {\n    const isServer = typeof process !== 'undefined' && process.env;\n    const isBrowser = \"undefined\" !== 'undefined';\n    let envSource = {};\n    let schemaToUse;\n    if (isServer) {\n        console.log('[env.ts] Running on server, using process.env');\n        envSource = process.env;\n        schemaToUse = serverSchema;\n    } else if (isBrowser) {\n        // console.log('[env.ts] Running in browser');\n        // Construct the object for Zod validation using only available client-side variables\n        const browserEnv = {\n            NODE_ENV: \"development\",\n            // Add public vars from process.env (replaced by Next.js build)\n            NEXT_PUBLIC_API_URL: \"http://localhost:5000\",\n            NEXT_PUBLIC_FIREBASE_API_KEY: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n            NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: \"solynta-academy.firebaseapp.com\",\n            NEXT_PUBLIC_FIREBASE_PROJECT_ID: \"solynta-academy\",\n            NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: \"solynta-academy.firebasestorage.app\",\n            NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: \"914922463191\",\n            NEXT_PUBLIC_FIREBASE_APP_ID: \"1:914922463191:web:b6e9c737dba77a26643592\",\n            NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: \"G-ZVC7R06Y33\",\n            NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: process.env.NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION,\n            NEXT_PUBLIC_ENABLE_ENROLLMENT_API: process.env.NEXT_PUBLIC_ENABLE_ENROLLMENT_API\n        };\n        // Filter out undefined values before parsing.\n        // Zod handles missing keys better than explicit undefined for defaults.\n        envSource = Object.entries(browserEnv).reduce((acc, [key, value])=>{\n            if (value !== undefined) {\n                acc[key] = value;\n            }\n            return acc;\n        }, {});\n        // Use clientSchema (baseSchema) for validation\n        schemaToUse = clientSchema;\n    } else {\n        console.warn('[env.ts] Environment context unclear (neither server nor browser). Using empty source.');\n        // Fallback or throw error depending on requirements\n        return clientSchema.parse({}); // Parse against client schema with empty source (will use defaults)\n    }\n    // Log the source keys being parsed (optional debugging)\n    // console.log(`[env.ts] Parsing env source keys: ${Object.keys(envSource).join(', ')} using ${isServer ? 'server' : 'client'} schema`);\n    try {\n        // Use safeParse to avoid throwing immediately, allowing for better error reporting\n        const parsed = schemaToUse.safeParse(envSource);\n        if (!parsed.success) {\n            console.error('❌ Invalid environment variables (raw error):', parsed.error // Log the raw error object\n            );\n            console.error('❌ Invalid environment variables (formatted):', // Use format() for detailed error messages\n            parsed.error.format());\n            // In development/test, return partial data with defaults to allow app to run partially\n            if (envSource.NODE_ENV !== 'production') {\n                console.warn('[env.ts] Parsing failed, returning partial env with defaults for non-production.');\n                // Attempt to parse with partial schema - might still fail if types are wrong\n                const partialParsed = schemaToUse.partial().safeParse(envSource);\n                if (partialParsed.success) {\n                    return partialParsed.data;\n                } else {\n                    console.error('[env.ts] Partial parsing also failed:', partialParsed.error.format());\n                    // Return minimal defaults if even partial fails\n                    return clientSchema.parse({}); // Return base defaults\n                }\n            }\n            throw new Error('Invalid environment variables'); // Throw in production\n        }\n        // console.log('[env.ts] Environment variables parsed successfully.');\n        return parsed.data;\n    } catch (error) {\n        console.error('[env.ts] Critical error during environment variable parsing:', error);\n        // Decide how to handle critical failure, e.g., throw or return defaults\n        if (true) {\n            console.warn('[env.ts] Returning default environment due to critical parsing error.');\n            return clientSchema.parse({}); // Return base defaults in non-prod\n        }\n        throw new Error('Failed to parse environment variables.');\n    }\n}\n// Export validated environment\nconst env = createEnv();\n// Create safe versions for client-side use - ensure keys match the clientSchema/baseSchema\nconst publicEnv = {\n    NODE_ENV: env.NODE_ENV,\n    NEXT_PUBLIC_API_URL: env.NEXT_PUBLIC_API_URL,\n    NEXT_PUBLIC_FIREBASE_API_KEY: env.NEXT_PUBLIC_FIREBASE_API_KEY,\n    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n    NEXT_PUBLIC_FIREBASE_PROJECT_ID: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n    NEXT_PUBLIC_FIREBASE_APP_ID: env.NEXT_PUBLIC_FIREBASE_APP_ID,\n    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,\n    NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: env.NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION,\n    NEXT_PUBLIC_ENABLE_ENROLLMENT_API: env.NEXT_PUBLIC_ENABLE_ENROLLMENT_API\n};\n // Use clientSchema for public type\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/env.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAiInstructorUrl: () => (/* binding */ getAiInstructorUrl),\n/* harmony export */   getAiTutorUrl: () => (/* binding */ getAiTutorUrl),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   getEnvironment: () => (/* binding */ getEnvironment),\n/* harmony export */   getFrontendUrl: () => (/* binding */ getFrontendUrl),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   isTest: () => (/* binding */ isTest)\n/* harmony export */ });\n/**\r\n * Configuration utilities for the application\r\n */ /**\r\n * Get the backend URL based on the current environment\r\n * @returns The backend URL\r\n */ function getBackendUrl() {\n    // Use environment variable or default to empty string (same origin)\n    // Try multiple environment variable names for backwards compatibility\n    const backendUrl = \"http://localhost:5000\" || 0 || 0;\n    // For development, provide a fallback localhost URL if no backend URL is specified\n    if (!backendUrl && \"development\" === 'development') {\n        return 'http://localhost:5000';\n    }\n    // Validate URL format if one is provided\n    if (backendUrl && !backendUrl.startsWith('http')) {\n        console.warn('Backend URL should start with http:// or https://');\n    }\n    return backendUrl;\n}\n/**\r\n * Get the frontend URL based on the current environment\r\n * @returns The frontend URL\r\n */ function getFrontendUrl() {\n    return process.env.NEXT_PUBLIC_FRONTEND_URL || ( false ? 0 : '');\n}\n/**\r\n * Get the full URL for a specific API endpoint\r\n * @param endpoint The API endpoint path\r\n * @returns The complete API URL\r\n */ function getApiUrl(endpoint) {\n    const baseUrl = getBackendUrl();\n    // If there's no backend URL, use same-origin API endpoint\n    if (!baseUrl) {\n        // If endpoint doesn't start with a slash, add it\n        return endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    }\n    // Ensure there's a single slash between base URL and endpoint\n    const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    return `${normalizedBaseUrl}${normalizedEndpoint}`;\n}\n/**\r\n * Get the URL for AI instructor API\r\n * @returns The AI instructor API URL\r\n */ function getAiInstructorUrl() {\n    return getApiUrl('/ai-instructor');\n}\n/**\r\n * Get the URL for AI tutor API\r\n * @returns The AI tutor API URL\r\n */ function getAiTutorUrl() {\n    return getApiUrl('/ai-tutor');\n}\n/**\r\n * Get the current environment name\r\n * @returns The environment name (development, production, test)\r\n */ function getEnvironment() {\n    return \"development\" || 0;\n}\n/**\r\n * Check if the application is running in development mode\r\n * @returns True if in development mode\r\n */ function isDevelopment() {\n    return getEnvironment() === 'development';\n}\n/**\r\n * Check if the application is running in production mode\r\n * @returns True if in production mode\r\n */ function isProduction() {\n    return getEnvironment() === 'production';\n}\n/**\r\n * Check if the application is running in test mode\r\n * @returns True if in test mode\r\n */ function isTest() {\n    return getEnvironment() === 'test';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/env */ \"(rsc)/./src/env.ts\");\n// File: lib/logger.ts\n/**\r\n * Application logger that provides consistent logging across the application\r\n * Can be configured to output to different destinations based on environment\r\n */ // Import environment variables\n\n// Environment-aware log level\nconst LOG_LEVEL = _env__WEBPACK_IMPORTED_MODULE_0__.env.LOG_LEVEL || 'info';\n// Numeric log level priorities (higher = more severe)\nconst LOG_LEVEL_PRIORITY = {\n    debug: 0,\n    info: 1,\n    warn: 2,\n    error: 3\n};\n// Current environment\nconst NODE_ENV = _env__WEBPACK_IMPORTED_MODULE_0__.env.NODE_ENV || 'development';\n// Should we pretty print logs?\nconst PRETTY_PRINT = _env__WEBPACK_IMPORTED_MODULE_0__.env.PRETTY_PRINT === 'true' || NODE_ENV === 'development';\n/**\r\n * Determine if a log at the given level should be output\r\n * based on the configured minimum log level\r\n */ function shouldLog(level) {\n    return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[LOG_LEVEL];\n}\n/**\r\n * Format a log message based on environment and settings\r\n */ function formatLogMessage(level, message, meta) {\n    const timestamp = new Date().toISOString();\n    if (PRETTY_PRINT) {\n        // Pretty format for development or when enabled\n        const colorCode = {\n            debug: '\\x1b[34m',\n            info: '\\x1b[32m',\n            warn: '\\x1b[33m',\n            error: '\\x1b[31m' // Red\n        }[level];\n        const reset = '\\x1b[0m';\n        const metaStr = meta ? `\\n${JSON.stringify(meta, null, 2)}` : '';\n        return `${colorCode}[${timestamp}] [${level.toUpperCase()}]${reset} ${message}${metaStr}`;\n    } else {\n        // JSON format for production or when pretty print is disabled\n        return JSON.stringify({\n            timestamp,\n            level,\n            message,\n            ...meta,\n            service: _env__WEBPACK_IMPORTED_MODULE_0__.env.SERVICE_NAME || 'app'\n        });\n    }\n}\n/**\r\n * Log a message at the specified level\r\n */ function logMessage(level, message, meta) {\n    if (!shouldLog(level)) return;\n    const formattedMessage = formatLogMessage(level, message, meta);\n    switch(level){\n        case 'debug':\n            console.debug(formattedMessage);\n            break;\n        case 'info':\n            console.info(formattedMessage);\n            break;\n        case 'warn':\n            console.warn(formattedMessage);\n            break;\n        case 'error':\n            console.error(formattedMessage);\n            break;\n    }\n}\n/**\r\n * Logger interface that can be imported throughout the application\r\n */ const logger = {\n    debug: (message, meta)=>logMessage('debug', message, meta),\n    info: (message, meta)=>logMessage('info', message, meta),\n    warn: (message, meta)=>logMessage('warn', message, meta),\n    error: (message, meta)=>logMessage('error', message, meta),\n    // Create a child logger with context\n    child: (context)=>({\n            debug: (message, meta)=>logMessage('debug', message, {\n                    ...context,\n                    ...meta\n                }),\n            info: (message, meta)=>logMessage('info', message, {\n                    ...context,\n                    ...meta\n                }),\n            warn: (message, meta)=>logMessage('warn', message, {\n                    ...context,\n                    ...meta\n                }),\n            error: (message, meta)=>logMessage('error', message, {\n                    ...context,\n                    ...meta\n                })\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fenhance-content%2Froute&page=%2Fapi%2Fenhance-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fenhance-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();