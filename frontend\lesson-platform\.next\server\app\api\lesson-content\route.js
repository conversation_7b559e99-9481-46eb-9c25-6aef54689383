/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/lesson-content/route";
exports.ids = ["app/api/lesson-content/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flesson-content%2Froute&page=%2Fapi%2Flesson-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flesson-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flesson-content%2Froute&page=%2Fapi%2Flesson-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flesson-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_lesson_content_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/lesson-content/route.ts */ \"(rsc)/./src/app/api/lesson-content/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/lesson-content/route\",\n        pathname: \"/api/lesson-content\",\n        filename: \"route\",\n        bundlePath: \"app/api/lesson-content/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\lesson-content\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_lesson_content_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flesson-content%2Froute&page=%2Fapi%2Flesson-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flesson-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/lesson-content/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/lesson-content/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n// src/app/api/lesson-content/route.ts (Add detailed error logging)\n\n\n\n\n\nconst logPrefix = '[API /lesson-content]';\nasync function POST(request) {\n    const requestStartTime = Date.now();\n    const frontendRequestId = (0,crypto__WEBPACK_IMPORTED_MODULE_3__.randomUUID)();\n    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Request received.`);\n    let body;\n    let backendUrl;\n    let fullBackendUrl;\n    let backendResponse;\n    let backendJson;\n    try {\n        // 1. Parse Body\n        try {\n            body = await request.json().catch(()=>({}));\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Parsed Body: ${JSON.stringify(body)}`);\n        } catch (parseError) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Error parsing request body: ${parseError.message}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Invalid request body.',\n                sessionId: null\n            }, {\n                status: 400\n            });\n        } // 2. Extract Params & Validate\n        const incomingSessionId = body.sessionId || body.session_id || null;\n        const studentId = body.studentId || body.student_id;\n        const lessonRef = body.lessonRef || body.lesson_ref;\n        const subject = body.subject;\n        const country = body.country;\n        const curriculum = body.curriculum;\n        const grade = body.grade;\n        const level = body.level;\n        const currentPhase = body.current_phase; // CRITICAL FIX: Extract phase parameter\n        // Construct backendData with all necessary fields\n        const backendData = {\n            student_id: studentId,\n            lesson_ref: lessonRef,\n            subject: subject,\n            country: country,\n            curriculum: curriculum,\n            grade: grade,\n            level: level,\n            ...currentPhase && {\n                current_phase: currentPhase\n            } // CRITICAL FIX: Include phase if provided\n        };\n        if (!lessonRef || !studentId || !subject || !country || !curriculum || !grade || !level) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.warn(`${logPrefix} [${frontendRequestId}] Missing required fields in request. Received: studentId=${studentId}, lessonRef=${lessonRef}, subject=${subject}, country=${country}, curriculum=${curriculum}, grade=${grade}, level=${level}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing one or more required fields: studentId, lessonRef, subject, country, curriculum, grade, level.',\n                sessionId: null\n            }, {\n                status: 400\n            });\n        }\n        // 3. Get Backend URL\n        try {\n            backendUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_2__.getBackendUrl)() || ( true ? 'http://localhost:5000' : 0);\n            if (!backendUrl) throw new Error(\"Backend URL not configured.\");\n            backendUrl = backendUrl.endsWith('/') ? backendUrl.slice(0, -1) : backendUrl;\n            fullBackendUrl = `${backendUrl}/lesson-content`;\n        } catch (urlError) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Error getting backend URL: ${urlError.message}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Backend service URL configuration error.',\n                sessionId: null\n            }, {\n                status: 500\n            });\n        }\n        // 4. Backend Check & Fallback\n        // Check if we should use fallback content\n        const useFallback = process.env.USE_FALLBACK_CONTENT === 'true' || false;\n        if (useFallback) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Using fallback content as configured by environment variable.`);\n            const fallbackSessionId = `fallback-${(0,crypto__WEBPACK_IMPORTED_MODULE_3__.randomUUID)()}`;\n            const fallbackResponse = {\n                success: true,\n                message: 'Using fallback content (backend bypass mode)',\n                sessionId: fallbackSessionId,\n                lessonTitle: `${subject} - ${grade} - ${lessonRef}`,\n                backendRequestId: null\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(fallbackResponse, {\n                status: 200\n            });\n        }\n        // 5. Call Backend using Axios\n        const authorizationHeader = request.headers.get('Authorization');\n        const headers = {\n            'Content-Type': 'application/json',\n            ...studentId ? {\n                'X-Student-Id': studentId\n            } : {},\n            ...authorizationHeader ? {\n                'Authorization': authorizationHeader\n            } : {}\n        };\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Headers SENT TO BACKEND: ${JSON.stringify(headers)}`);\n        try {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Calling backend via AXIOS: ${fullBackendUrl}`);\n            const axiosResponse = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(fullBackendUrl, backendData, {\n                headers,\n                timeout: 90000 // 90 seconds timeout for potentially long AI operations\n            });\n            backendJson = axiosResponse.data;\n            backendResponse = {\n                ok: true,\n                status: axiosResponse.status\n            };\n        } catch (error) {\n            if (axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].isAxiosError(error)) {\n                _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Axios request to backend failed: ${error.message}`);\n                // Check for timeout\n                if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        message: 'Backend service timed out.',\n                        sessionId: null\n                    }, {\n                        status: 504\n                    });\n                }\n                // Handle other Axios errors with response data if available\n                const status = error.response?.status || 503;\n                const message = error.response?.data?.message || error.message || 'Axios request failed';\n                // If we have response data, use it for backendJson\n                if (error.response?.data) {\n                    backendJson = error.response.data;\n                    backendResponse = {\n                        ok: false,\n                        status: error.response.status\n                    };\n                    // Log the detailed error response for debugging\n                    _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Backend error response: ${JSON.stringify(error.response.data)}`);\n                    // For 500 errors, try to provide more helpful information or use fallback\n                    if (status === 500) {\n                        // Check if we should use fallback content for 500 errors\n                        const useFallbackFor500 = process.env.USE_FALLBACK_FOR_500_ERRORS === 'true' || process.env.USE_FALLBACK_CONTENT === 'true' || false;\n                        if (useFallbackFor500) {\n                            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Backend returned 500 error, using fallback content.`);\n                            const fallbackSessionId = `fallback-${(0,crypto__WEBPACK_IMPORTED_MODULE_3__.randomUUID)()}`;\n                            const fallbackResponse = {\n                                success: true,\n                                message: 'Using fallback content (backend error fallback)',\n                                sessionId: fallbackSessionId,\n                                lessonTitle: `${backendData.subject} - ${backendData.grade} - ${backendData.lesson_ref}`,\n                                backendRequestId: error.response.data?.request_id || null,\n                                fallback: true\n                            };\n                            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(fallbackResponse, {\n                                status: 200\n                            });\n                        }\n                        // Return a more descriptive error with the original message preserved\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            success: false,\n                            message: message || 'Backend server error',\n                            error: error.response.data,\n                            sessionId: null,\n                            backendRequestId: error.response.data?.request_id\n                        }, {\n                            status: 200\n                        }); // Return 200 from proxy with error details\n                    }\n                // Continue processing with the error response data for other status codes\n                } else {\n                    // No response data available\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: false,\n                        message,\n                        sessionId: null\n                    }, {\n                        status\n                    });\n                }\n            } else {\n                // Handle non-Axios errors\n                _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Non-Axios error: ${error.message}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: 'Unexpected proxy error.',\n                    sessionId: null\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // Skip steps 6 & 7 since axios already parsed the JSON\n        const backendRequestId = backendJson.request_id || null;\n        // 8. Check Backend Status/Success Flag\n        if (!backendResponse.ok || !backendJson.success) {\n            const errorMessage = backendJson.message || `Backend request failed with status ${backendResponse.status}`;\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Backend error. Status: ${backendResponse.status}. Message: ${errorMessage}. Backend ID: ${backendRequestId}.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: errorMessage,\n                sessionId: null,\n                backendRequestId\n            }, {\n                status: 200\n            }); // Return 200 from proxy\n        }\n        // 9. Extract Session ID\n        const responseData = backendJson.data;\n        const newSessionId = responseData?.session_id || null;\n        if (!newSessionId) {\n            _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] Backend success, but 'session_id' missing in 'data'! Backend ID: ${backendRequestId}. Body: ${responseBodyText?.substring(0, 500)}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Backend session ID missing.\",\n                sessionId: null,\n                backendRequestId\n            }, {\n                status: 200\n            });\n        }\n        // 10. Prepare and Send Success Response\n        const normalizedSuccessResponse = {\n            success: true,\n            message: backendJson.message || 'Session started successfully.',\n            sessionId: newSessionId,\n            lessonTitle: responseData?.lessonTitle || null,\n            backendRequestId: backendRequestId\n        };\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.info(`${logPrefix} [${frontendRequestId}] Sending normalized SUCCESS response. Session: ${newSessionId}.`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(normalizedSuccessResponse, {\n            status: 200\n        });\n    } catch (error) {\n        const duration = Date.now() - requestStartTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_1__.logger.error(`${logPrefix} [${frontendRequestId}] UNHANDLED error in API route (${duration}ms):`, error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'An unexpected server error occurred.',\n            sessionId: null\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/lesson-content/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/env.ts":
/*!********************!*\
  !*** ./src/env.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env),\n/* harmony export */   publicEnv: () => (/* binding */ publicEnv)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n// File: env.ts\n\n/**\r\n * Environment variable schema definition\r\n * Add all environment variables your application uses here\r\n */ const baseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'debug',\n        'info',\n        'warn',\n        'error'\n    ]).default('info'),\n    PRETTY_PRINT: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'true',\n        'false'\n    ]).default('false').transform((val)=>val === 'true'),\n    SERVICE_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('education-platform'),\n    // Public API URL\n    NEXT_PUBLIC_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    // Public Firebase Config (Client-side) - Renamed to match convention\n    NEXT_PUBLIC_FIREBASE_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_PROJECT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_APP_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // Feature flags (can be public)\n    NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'true',\n        'false'\n    ]).default('true').transform((val)=>val === 'true'),\n    NEXT_PUBLIC_ENABLE_ENROLLMENT_API: zod__WEBPACK_IMPORTED_MODULE_0__.z.enum([\n        'true',\n        'false'\n    ]).default('true').transform((val)=>val === 'true')\n});\n// Server-side specific schema, extending the base\nconst serverSchema = baseSchema.extend({\n    // Firebase Admin (Server-side) - Kept original names as they are not public\n    FIREBASE_PROJECT_ID: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    FIREBASE_CLIENT_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email(),\n    FIREBASE_PRIVATE_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(10)\n});\n// Client-side schema only includes the base (public) variables\nconst clientSchema = baseSchema;\n/**\r\n * Parse and validate environment variables\r\n * This will throw an error if required environment variables are missing\r\n */ function createEnv() {\n    const isServer = typeof process !== 'undefined' && process.env;\n    const isBrowser = \"undefined\" !== 'undefined';\n    let envSource = {};\n    let schemaToUse;\n    if (isServer) {\n        console.log('[env.ts] Running on server, using process.env');\n        envSource = process.env;\n        schemaToUse = serverSchema;\n    } else if (isBrowser) {\n        // console.log('[env.ts] Running in browser');\n        // Construct the object for Zod validation using only available client-side variables\n        const browserEnv = {\n            NODE_ENV: \"development\",\n            // Add public vars from process.env (replaced by Next.js build)\n            NEXT_PUBLIC_API_URL: \"http://localhost:5000\",\n            NEXT_PUBLIC_FIREBASE_API_KEY: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n            NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: \"solynta-academy.firebaseapp.com\",\n            NEXT_PUBLIC_FIREBASE_PROJECT_ID: \"solynta-academy\",\n            NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: \"solynta-academy.firebasestorage.app\",\n            NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: \"914922463191\",\n            NEXT_PUBLIC_FIREBASE_APP_ID: \"1:914922463191:web:b6e9c737dba77a26643592\",\n            NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: \"G-ZVC7R06Y33\",\n            NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: process.env.NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION,\n            NEXT_PUBLIC_ENABLE_ENROLLMENT_API: process.env.NEXT_PUBLIC_ENABLE_ENROLLMENT_API\n        };\n        // Filter out undefined values before parsing.\n        // Zod handles missing keys better than explicit undefined for defaults.\n        envSource = Object.entries(browserEnv).reduce((acc, [key, value])=>{\n            if (value !== undefined) {\n                acc[key] = value;\n            }\n            return acc;\n        }, {});\n        // Use clientSchema (baseSchema) for validation\n        schemaToUse = clientSchema;\n    } else {\n        console.warn('[env.ts] Environment context unclear (neither server nor browser). Using empty source.');\n        // Fallback or throw error depending on requirements\n        return clientSchema.parse({}); // Parse against client schema with empty source (will use defaults)\n    }\n    // Log the source keys being parsed (optional debugging)\n    // console.log(`[env.ts] Parsing env source keys: ${Object.keys(envSource).join(', ')} using ${isServer ? 'server' : 'client'} schema`);\n    try {\n        // Use safeParse to avoid throwing immediately, allowing for better error reporting\n        const parsed = schemaToUse.safeParse(envSource);\n        if (!parsed.success) {\n            console.error('❌ Invalid environment variables (raw error):', parsed.error // Log the raw error object\n            );\n            console.error('❌ Invalid environment variables (formatted):', // Use format() for detailed error messages\n            parsed.error.format());\n            // In development/test, return partial data with defaults to allow app to run partially\n            if (envSource.NODE_ENV !== 'production') {\n                console.warn('[env.ts] Parsing failed, returning partial env with defaults for non-production.');\n                // Attempt to parse with partial schema - might still fail if types are wrong\n                const partialParsed = schemaToUse.partial().safeParse(envSource);\n                if (partialParsed.success) {\n                    return partialParsed.data;\n                } else {\n                    console.error('[env.ts] Partial parsing also failed:', partialParsed.error.format());\n                    // Return minimal defaults if even partial fails\n                    return clientSchema.parse({}); // Return base defaults\n                }\n            }\n            throw new Error('Invalid environment variables'); // Throw in production\n        }\n        // console.log('[env.ts] Environment variables parsed successfully.');\n        return parsed.data;\n    } catch (error) {\n        console.error('[env.ts] Critical error during environment variable parsing:', error);\n        // Decide how to handle critical failure, e.g., throw or return defaults\n        if (true) {\n            console.warn('[env.ts] Returning default environment due to critical parsing error.');\n            return clientSchema.parse({}); // Return base defaults in non-prod\n        }\n        throw new Error('Failed to parse environment variables.');\n    }\n}\n// Export validated environment\nconst env = createEnv();\n// Create safe versions for client-side use - ensure keys match the clientSchema/baseSchema\nconst publicEnv = {\n    NODE_ENV: env.NODE_ENV,\n    NEXT_PUBLIC_API_URL: env.NEXT_PUBLIC_API_URL,\n    NEXT_PUBLIC_FIREBASE_API_KEY: env.NEXT_PUBLIC_FIREBASE_API_KEY,\n    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n    NEXT_PUBLIC_FIREBASE_PROJECT_ID: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n    NEXT_PUBLIC_FIREBASE_APP_ID: env.NEXT_PUBLIC_FIREBASE_APP_ID,\n    NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,\n    NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION: env.NEXT_PUBLIC_ENABLE_TIMETABLE_GENERATION,\n    NEXT_PUBLIC_ENABLE_ENROLLMENT_API: env.NEXT_PUBLIC_ENABLE_ENROLLMENT_API\n};\n // Use clientSchema for public type\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/env.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAiInstructorUrl: () => (/* binding */ getAiInstructorUrl),\n/* harmony export */   getAiTutorUrl: () => (/* binding */ getAiTutorUrl),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   getEnvironment: () => (/* binding */ getEnvironment),\n/* harmony export */   getFrontendUrl: () => (/* binding */ getFrontendUrl),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   isTest: () => (/* binding */ isTest)\n/* harmony export */ });\n/**\r\n * Configuration utilities for the application\r\n */ /**\r\n * Get the backend URL based on the current environment\r\n * @returns The backend URL\r\n */ function getBackendUrl() {\n    // Use environment variable or default to empty string (same origin)\n    // Try multiple environment variable names for backwards compatibility\n    const backendUrl = \"http://localhost:5000\" || 0 || 0;\n    // For development, provide a fallback localhost URL if no backend URL is specified\n    if (!backendUrl && \"development\" === 'development') {\n        return 'http://localhost:5000';\n    }\n    // Validate URL format if one is provided\n    if (backendUrl && !backendUrl.startsWith('http')) {\n        console.warn('Backend URL should start with http:// or https://');\n    }\n    return backendUrl;\n}\n/**\r\n * Get the frontend URL based on the current environment\r\n * @returns The frontend URL\r\n */ function getFrontendUrl() {\n    return process.env.NEXT_PUBLIC_FRONTEND_URL || ( false ? 0 : '');\n}\n/**\r\n * Get the full URL for a specific API endpoint\r\n * @param endpoint The API endpoint path\r\n * @returns The complete API URL\r\n */ function getApiUrl(endpoint) {\n    const baseUrl = getBackendUrl();\n    // If there's no backend URL, use same-origin API endpoint\n    if (!baseUrl) {\n        // If endpoint doesn't start with a slash, add it\n        return endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    }\n    // Ensure there's a single slash between base URL and endpoint\n    const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;\n    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;\n    return `${normalizedBaseUrl}${normalizedEndpoint}`;\n}\n/**\r\n * Get the URL for AI instructor API\r\n * @returns The AI instructor API URL\r\n */ function getAiInstructorUrl() {\n    return getApiUrl('/ai-instructor');\n}\n/**\r\n * Get the URL for AI tutor API\r\n * @returns The AI tutor API URL\r\n */ function getAiTutorUrl() {\n    return getApiUrl('/ai-tutor');\n}\n/**\r\n * Get the current environment name\r\n * @returns The environment name (development, production, test)\r\n */ function getEnvironment() {\n    return \"development\" || 0;\n}\n/**\r\n * Check if the application is running in development mode\r\n * @returns True if in development mode\r\n */ function isDevelopment() {\n    return getEnvironment() === 'development';\n}\n/**\r\n * Check if the application is running in production mode\r\n * @returns True if in production mode\r\n */ function isProduction() {\n    return getEnvironment() === 'production';\n}\n/**\r\n * Check if the application is running in test mode\r\n * @returns True if in test mode\r\n */ function isTest() {\n    return getEnvironment() === 'test';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/env */ \"(rsc)/./src/env.ts\");\n// File: lib/logger.ts\n/**\r\n * Application logger that provides consistent logging across the application\r\n * Can be configured to output to different destinations based on environment\r\n */ // Import environment variables\n\n// Environment-aware log level\nconst LOG_LEVEL = _env__WEBPACK_IMPORTED_MODULE_0__.env.LOG_LEVEL || 'info';\n// Numeric log level priorities (higher = more severe)\nconst LOG_LEVEL_PRIORITY = {\n    debug: 0,\n    info: 1,\n    warn: 2,\n    error: 3\n};\n// Current environment\nconst NODE_ENV = _env__WEBPACK_IMPORTED_MODULE_0__.env.NODE_ENV || 'development';\n// Should we pretty print logs?\nconst PRETTY_PRINT = _env__WEBPACK_IMPORTED_MODULE_0__.env.PRETTY_PRINT === 'true' || NODE_ENV === 'development';\n/**\r\n * Determine if a log at the given level should be output\r\n * based on the configured minimum log level\r\n */ function shouldLog(level) {\n    return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[LOG_LEVEL];\n}\n/**\r\n * Format a log message based on environment and settings\r\n */ function formatLogMessage(level, message, meta) {\n    const timestamp = new Date().toISOString();\n    if (PRETTY_PRINT) {\n        // Pretty format for development or when enabled\n        const colorCode = {\n            debug: '\\x1b[34m',\n            info: '\\x1b[32m',\n            warn: '\\x1b[33m',\n            error: '\\x1b[31m' // Red\n        }[level];\n        const reset = '\\x1b[0m';\n        const metaStr = meta ? `\\n${JSON.stringify(meta, null, 2)}` : '';\n        return `${colorCode}[${timestamp}] [${level.toUpperCase()}]${reset} ${message}${metaStr}`;\n    } else {\n        // JSON format for production or when pretty print is disabled\n        return JSON.stringify({\n            timestamp,\n            level,\n            message,\n            ...meta,\n            service: _env__WEBPACK_IMPORTED_MODULE_0__.env.SERVICE_NAME || 'app'\n        });\n    }\n}\n/**\r\n * Log a message at the specified level\r\n */ function logMessage(level, message, meta) {\n    if (!shouldLog(level)) return;\n    const formattedMessage = formatLogMessage(level, message, meta);\n    switch(level){\n        case 'debug':\n            console.debug(formattedMessage);\n            break;\n        case 'info':\n            console.info(formattedMessage);\n            break;\n        case 'warn':\n            console.warn(formattedMessage);\n            break;\n        case 'error':\n            console.error(formattedMessage);\n            break;\n    }\n}\n/**\r\n * Logger interface that can be imported throughout the application\r\n */ const logger = {\n    debug: (message, meta)=>logMessage('debug', message, meta),\n    info: (message, meta)=>logMessage('info', message, meta),\n    warn: (message, meta)=>logMessage('warn', message, meta),\n    error: (message, meta)=>logMessage('error', message, meta),\n    // Create a child logger with context\n    child: (context)=>({\n            debug: (message, meta)=>logMessage('debug', message, {\n                    ...context,\n                    ...meta\n                }),\n            info: (message, meta)=>logMessage('info', message, {\n                    ...context,\n                    ...meta\n                }),\n            warn: (message, meta)=>logMessage('warn', message, {\n                    ...context,\n                    ...meta\n                }),\n            error: (message, meta)=>logMessage('error', message, {\n                    ...context,\n                    ...meta\n                })\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Flesson-content%2Froute&page=%2Fapi%2Flesson-content%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Flesson-content%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();