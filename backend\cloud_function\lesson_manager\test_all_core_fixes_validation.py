#!/usr/bin/env python3
"""
Comprehensive test to validate all core fixes:
1. AI state update block generation
2. Diagnostic completion logic
3. 8-phase sequence enforcement
4. Performance optimization
"""

import sys
import os
import time
import json
import requests
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_diagnostic_progression_with_fixes():
    """Test the complete diagnostic progression with all fixes applied"""
    print("🧪 TESTING COMPLETE DIAGNOSTIC PROGRESSION WITH ALL FIXES")
    print("=" * 80)
    
    # Test configuration
    base_url = "http://localhost:5000"
    test_session_id = f"test_all_fixes_{int(time.time())}"
    
    # Test student credentials
    student_credentials = {
        "student_id": "andrea_ugono_33305",
        "lesson_id": "P5-ENT-046"
    }
    
    # Test diagnostic sequence
    diagnostic_questions = [
        "I want to learn about profit and loss",  # diagnostic_start_probe
        "A business sells items to make money",   # Q1
        "Profit is when you earn more than you spend",  # Q2
        "Loss happens when expenses are higher than income",  # Q3
        "Revenue minus expenses equals profit",   # Q4
        "Businesses need to track their money carefully"  # Q5
    ]
    
    results = {
        "test_start_time": datetime.now().isoformat(),
        "phases_tested": [],
        "state_blocks_found": 0,
        "state_blocks_missing": 0,
        "response_times": [],
        "ai_quality_scores": [],
        "diagnostic_completion": False,
        "teaching_transition": False,
        "errors": []
    }
    
    print(f"🎯 Test Session: {test_session_id}")
    print(f"👤 Student: {student_credentials['student_id']}")
    print(f"📚 Lesson: {student_credentials['lesson_id']}")
    print()
    
    for i, question in enumerate(diagnostic_questions, 1):
        print(f"📝 Question {i}/6: Testing diagnostic progression")
        print(f"   Query: '{question}'")
        
        # Record start time
        start_time = time.time()
        
        try:
            # Make API request
            response = requests.post(
                f"{base_url}/enhance_lesson_content",
                json={
                    "session_id": test_session_id,
                    "query": question,
                    **student_credentials
                },
                timeout=30
            )
            
            # Record response time
            response_time = time.time() - start_time
            results["response_times"].append(response_time)
            
            print(f"   ⏱️ Response time: {response_time:.2f}s")
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract phase information
                current_phase = data.get('current_lesson_phase', 'unknown')
                results["phases_tested"].append(current_phase)
                
                print(f"   📍 Phase: {current_phase}")
                
                # Check for state update blocks
                ai_response = data.get('ai_response', '')
                if 'AI_STATE_UPDATE_BLOCK_START' in ai_response:
                    results["state_blocks_found"] += 1
                    print(f"   ✅ State update block found")
                else:
                    results["state_blocks_missing"] += 1
                    print(f"   ❌ State update block missing")
                
                # Calculate AI quality score (simple heuristic)
                ai_quality = min(100, len(ai_response) / 5)  # Simple quality metric
                results["ai_quality_scores"].append(ai_quality)
                
                print(f"   🤖 AI Quality: {ai_quality:.1f}%")
                
                # Check for diagnostic completion
                if 'teaching' in current_phase:
                    results["diagnostic_completion"] = True
                    results["teaching_transition"] = True
                    print(f"   🎉 Diagnostic completed! Transitioned to teaching")
                
                # Check for specific diagnostic phases
                if 'diagnostic' in current_phase:
                    if 'ask_q' in current_phase:
                        q_num = current_phase.split('ask_q')[-1] if 'ask_q' in current_phase else '?'
                        print(f"   🔍 Diagnostic question phase: Q{q_num}")
                    elif 'eval_q' in current_phase:
                        q_num = current_phase.split('eval_q')[1].split('_')[0] if 'eval_q' in current_phase else '?'
                        print(f"   📊 Diagnostic evaluation phase: Q{q_num}")
                    elif 'decide_level' in current_phase:
                        print(f"   🎯 Diagnostic completion phase: deciding level")
                
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                results["errors"].append(error_msg)
                print(f"   ❌ Error: {error_msg}")
                
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            results["errors"].append(error_msg)
            print(f"   💥 Exception: {e}")
        
        print()
        
        # Stop if we've reached teaching phase
        if results["teaching_transition"]:
            print("🎉 Successfully transitioned to teaching phase!")
            break
    
    # Calculate final metrics
    avg_response_time = sum(results["response_times"]) / len(results["response_times"]) if results["response_times"] else 0
    avg_ai_quality = sum(results["ai_quality_scores"]) / len(results["ai_quality_scores"]) if results["ai_quality_scores"] else 0
    state_block_percentage = (results["state_blocks_found"] / (results["state_blocks_found"] + results["state_blocks_missing"])) * 100 if (results["state_blocks_found"] + results["state_blocks_missing"]) > 0 else 0
    
    results.update({
        "test_end_time": datetime.now().isoformat(),
        "avg_response_time": avg_response_time,
        "avg_ai_quality": avg_ai_quality,
        "state_block_percentage": state_block_percentage,
        "total_phases": len(set(results["phases_tested"])),
        "unique_phases": list(set(results["phases_tested"]))
    })
    
    # Print final results
    print("=" * 80)
    print("📊 FINAL TEST RESULTS")
    print("=" * 80)
    print(f"⏱️ Average Response Time: {avg_response_time:.2f}s (Target: <2s)")
    print(f"🤖 Average AI Quality: {avg_ai_quality:.1f}% (Target: >70%)")
    print(f"🔄 State Block Generation: {state_block_percentage:.1f}% (Target: 100%)")
    print(f"📍 Total Phases Tested: {results['total_phases']}")
    print(f"🎯 Diagnostic Completion: {'✅ YES' if results['diagnostic_completion'] else '❌ NO'}")
    print(f"🏫 Teaching Transition: {'✅ YES' if results['teaching_transition'] else '❌ NO'}")
    print(f"❌ Errors: {len(results['errors'])}")
    
    if results["errors"]:
        print("\n🚨 ERRORS ENCOUNTERED:")
        for error in results["errors"]:
            print(f"   • {error}")
    
    print(f"\n📋 Phases Progression:")
    for i, phase in enumerate(results["phases_tested"], 1):
        print(f"   {i}. {phase}")
    
    # Determine overall success
    success_criteria = {
        "avg_response_time_under_2s": avg_response_time < 2.0,
        "avg_ai_quality_over_70": avg_ai_quality > 70.0,
        "state_blocks_generated": state_block_percentage > 80.0,  # Allow some tolerance
        "diagnostic_completion": results["diagnostic_completion"],
        "no_critical_errors": len(results["errors"]) == 0
    }
    
    all_passed = all(success_criteria.values())
    
    print(f"\n🎯 SUCCESS CRITERIA:")
    for criterion, passed in success_criteria.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   • {criterion}: {status}")
    
    print(f"\n🏆 OVERALL RESULT: {'✅ ALL FIXES WORKING' if all_passed else '❌ SOME ISSUES REMAIN'}")
    
    # Save detailed results
    results_file = f"all_fixes_test_results_{int(time.time())}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed results saved: {results_file}")
    
    return all_passed, results

def main():
    """Main test function"""
    print("COMPREHENSIVE CORE FIXES VALIDATION TEST")
    print("=" * 80)
    print("Testing all implemented fixes:")
    print("1. ✅ AI state update block generation enforcement")
    print("2. ✅ Diagnostic completion logic fixes")
    print("3. ✅ 8-phase sequence enforcement")
    print("4. ✅ Performance optimization")
    print("=" * 80)
    
    try:
        success, results = test_diagnostic_progression_with_fixes()
        
        if success:
            print("\n🎉 ALL CORE FIXES VALIDATED SUCCESSFULLY!")
            print("✅ System is ready for production deployment")
            return 0
        else:
            print("\n⚠️ SOME ISSUES STILL NEED ATTENTION")
            print("❌ Review test results and address remaining issues")
            return 1
            
    except Exception as e:
        print(f"\n💥 TEST EXECUTION FAILED: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
