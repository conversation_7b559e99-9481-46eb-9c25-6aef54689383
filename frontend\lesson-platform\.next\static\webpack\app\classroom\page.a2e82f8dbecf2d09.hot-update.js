"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/classroom/page",{

/***/ "(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx":
/*!************************************************!*\
  !*** ./src/app/classroom/ClassroomContent.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_lesson_components_TutorChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/lesson-components/TutorChat */ \"(app-pages-browser)/./src/components/lesson-components/TutorChat.tsx\");\n/* harmony import */ var _hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useLessonTimer */ \"(app-pages-browser)/./src/hooks/useLessonTimer.ts\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(app-pages-browser)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-interaction-logger */ \"(app-pages-browser)/./src/hooks/use-interaction-logger.ts\");\n/* harmony import */ var _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/shadcn/card */ \"(app-pages-browser)/./src/components/shadcn/card.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/ErrorDisplay */ \"(app-pages-browser)/./src/components/ui/ErrorDisplay.tsx\");\n/* harmony import */ var _components_shadcn_LessonHeader__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/shadcn/LessonHeader */ \"(app-pages-browser)/./src/components/shadcn/LessonHeader.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_lesson_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/lesson-components/ErrorBoundary */ \"(app-pages-browser)/./src/components/lesson-components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_lesson_components_LevelAdjustmentHistory__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/lesson-components/LevelAdjustmentHistory */ \"(app-pages-browser)/./src/components/lesson-components/LevelAdjustmentHistory.tsx\");\n/* harmony import */ var _components_DiagnosticProgress__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/DiagnosticProgress */ \"(app-pages-browser)/./src/components/DiagnosticProgress.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n // Using simplified session hook\n\n\n\n\n\n\n\n\n\n\n\n // Import AxiosError\nconst AI_INTERACTION_ENDPOINT = '/api/enhance-content'; // Next.js API proxy for Flask's /api/enhance-content\nconst LESSON_PHASE_COMPLETED = \"completed\";\nconst LESSON_DURATION_MINUTES = 45; // Total lesson duration in minutes\n// Fallback Component for ErrorBoundary\nconst ErrorBoundaryFallback = (param)=>{\n    let { error, resetErrorBoundary } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n            className: \"border-destructive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                        className: \"text-destructive\",\n                        children: \"Rendering Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"An unexpected error occurred while rendering this part of the lesson.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 p-2 bg-red-50 text-red-700 rounded text-xs overflow-auto\",\n                            children: [\n                                error.message,\n                                \"\\\\n\",\n                                error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                            onClick: resetErrorBoundary,\n                            className: \"mt-4\",\n                            children: \"Try to Recover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ErrorBoundaryFallback;\nconst ClassroomContent = (param)=>{\n    let { sessionIdFromUrlProp, lessonRefProp, studentIdProp, countryProp, curriculumProp, gradeProp, levelProp, subjectProp } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const logInteraction = (0,_hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { toast } = (0,_app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const { user, isReady, getAuthHeaders, backendSessionId// THE Firestore-backed lesson session ID from context\n     } = (0,_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const chatBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const initialAiInteractionSentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false); // Tracks if the first system message has been sent\n    const handleAiInteractionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Ref to store handleAiInteraction\n    const [currentLessonPhase, setCurrentLessonPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isAiLoading, setIsAiLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // For AI response loading\n    const [isPageLoading, setIsPageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Overall page/initial setup loading\n    const [uiError, setUiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // For displaying errors in the UI\n    // Level tracking state for real-time adjustments\n    const [currentTeachingLevel, setCurrentTeachingLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [levelAdjustmentHistory, setLevelAdjustmentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Diagnostic progress tracking state\n    const [diagnosticProgress, setDiagnosticProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentQuestionIndex: 0,\n        totalQuestions: 5,\n        currentProbingLevel: 5,\n        questionsCompleted: 0,\n        isComplete: false\n    });\n    // Timer state\n    const [lessonStartTime, setLessonStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Timer logic moved to custom hook\n    const { timeRemaining, isTimerActive, setIsTimerActive, formatTime, startTimer, getTimerStatus } = (0,_hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__.useLessonTimer)({\n        lessonStartTime,\n        currentLessonPhase,\n        sessionIdFromUrlProp,\n        backendSessionId: backendSessionId || undefined,\n        lessonRef: lessonRefProp,\n        onTimeUp: {\n            \"ClassroomContent.useLessonTimer\": ()=>{\n                // This will be called when time is up\n                const sessionId = sessionIdFromUrlProp || backendSessionId;\n                if (sessionId) {\n                    handleAiInteraction('[System: Time is up! Completing lesson...]', true, sessionId);\n                }\n            }\n        }[\"ClassroomContent.useLessonTimer\"],\n        onQuizTransition: {\n            \"ClassroomContent.useLessonTimer\": ()=>{\n                // This will be called when forced quiz transition is triggered at 37.5 minutes\n                const sessionId = sessionIdFromUrlProp || backendSessionId;\n                if (sessionId) {\n                    logInteraction('forced_quiz_transition_triggered', {\n                        lessonRef: lessonRefProp,\n                        sessionId: sessionId,\n                        currentPhase: currentLessonPhase || 'teaching',\n                        timeRemaining: timeRemaining,\n                        uncoveredContent: 'Material not covered due to time constraints will be added to homework'\n                    });\n                    // Send system message to AI to trigger quiz and capture uncovered content as homework\n                    handleAiInteraction('[System: Time limit approaching - transition to quiz phase and capture any uncovered teaching material as homework assignments]', true, sessionId);\n                }\n            }\n        }[\"ClassroomContent.useLessonTimer\"]\n    });\n    const [lessonDetails, setLessonDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Chat and lesson state\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lessonEnded, setLessonEnded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Derive core identifiers from props\n    const lessonRef = lessonRefProp;\n    const studentId = studentIdProp; // This should be the Firebase UID of the student\n    // Handle lesson phase updates from server responses\n    const handleLessonPhaseUpdates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (response)=>{\n            try {\n                var _response_data, _response_data1, _response_data2, _response_data3, _response_data4, _response_data5, _response_data6;\n                console.log('[handleLessonPhaseUpdates] ==> ENHANCED DEBUGGING - PROCESSING RESPONSE FROM BACKEND');\n                console.log('[handleLessonPhaseUpdates] ==> FULL RAW RESPONSE:', JSON.stringify(response, null, 2));\n                console.log('[handleLessonPhaseUpdates] Raw response keys:', Object.keys(response || {}));\n                console.log('[handleLessonPhaseUpdates] Response.data keys:', Object.keys((response === null || response === void 0 ? void 0 : response.data) || {}));\n                // FORCE CONSOLE OUTPUT FOR REAL-TIME DEBUGGING\n                console.warn('🔥 FRONTEND PHASE UPDATE DEBUG:', {\n                    responseType: typeof response,\n                    hasData: !!(response === null || response === void 0 ? void 0 : response.data),\n                    dataKeys: (response === null || response === void 0 ? void 0 : response.data) ? Object.keys(response.data) : 'no data',\n                    currentFrontendPhase: currentLessonPhase\n                });\n                // CRITICAL FIX: Frontend defensive parsing to handle multiple backend field names\n                // Check multiple possible field locations for state updates\n                const serverStateUpdates = (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.state_updates) || (response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.parsed_state) || (response === null || response === void 0 ? void 0 : response.state_updates) || (response === null || response === void 0 ? void 0 : response.parsed_state);\n                let phaseFromServer = (response === null || response === void 0 ? void 0 : (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.current_phase) || (response === null || response === void 0 ? void 0 : response.current_phase); // From the main response body\n                let diagCompleteFromServer = response === null || response === void 0 ? void 0 : (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.diagnostic_complete;\n                let assessedLevelFromServer = response === null || response === void 0 ? void 0 : (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.assessed_level;\n                // ENHANCED: Check if this is a wrapped response (Flask returns {success: true, data: {...}})\n                if ((response === null || response === void 0 ? void 0 : response.success) && (response === null || response === void 0 ? void 0 : response.data)) {\n                    var _innerData_state_updates, _innerData_state_updates1, _innerData_state_updates2, _innerData_state_updates3;\n                    console.log('[handleLessonPhaseUpdates] 🎯 DETECTED WRAPPED RESPONSE from Flask');\n                    const innerData = response.data;\n                    // CRITICAL FIX: Backend returns current_phase at top level of data, not just in state_updates\n                    // Priority order: state_updates.new_phase > data.current_phase > data.new_phase\n                    phaseFromServer = ((_innerData_state_updates = innerData.state_updates) === null || _innerData_state_updates === void 0 ? void 0 : _innerData_state_updates.new_phase) || innerData.current_phase || innerData.new_phase || phaseFromServer;\n                    diagCompleteFromServer = innerData.diagnostic_complete || innerData.diagnostic_completed_this_session || ((_innerData_state_updates1 = innerData.state_updates) === null || _innerData_state_updates1 === void 0 ? void 0 : _innerData_state_updates1.diagnostic_completed_this_session) || diagCompleteFromServer;\n                    assessedLevelFromServer = innerData.assessed_level || innerData.assigned_level_for_teaching || ((_innerData_state_updates2 = innerData.state_updates) === null || _innerData_state_updates2 === void 0 ? void 0 : _innerData_state_updates2.assigned_level_for_teaching) || assessedLevelFromServer;\n                    console.log('[handleLessonPhaseUpdates] 🎯 EXTRACTED FROM WRAPPED RESPONSE:', {\n                        phaseFromServer,\n                        diagCompleteFromServer,\n                        assessedLevelFromServer,\n                        hasStateUpdates: !!innerData.state_updates,\n                        innerDataKeys: Object.keys(innerData)\n                    });\n                    // ENHANCED: Force console output to show what we found\n                    console.warn('🔥 PHASE EXTRACTION DEBUG:', {\n                        'innerData.current_phase': innerData.current_phase,\n                        'innerData.new_phase': innerData.new_phase,\n                        'innerData.state_updates?.new_phase': (_innerData_state_updates3 = innerData.state_updates) === null || _innerData_state_updates3 === void 0 ? void 0 : _innerData_state_updates3.new_phase,\n                        'final_phaseFromServer': phaseFromServer\n                    });\n                }\n                // Debug logging to track field locations\n                console.log('[handleLessonPhaseUpdates] DEBUG: Checking state update fields:', {\n                    'response.data.state_updates': !!(response === null || response === void 0 ? void 0 : (_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : _response_data5.state_updates),\n                    'response.data.parsed_state': !!(response === null || response === void 0 ? void 0 : (_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : _response_data6.parsed_state),\n                    'response.state_updates': !!(response === null || response === void 0 ? void 0 : response.state_updates),\n                    'response.parsed_state': !!(response === null || response === void 0 ? void 0 : response.parsed_state),\n                    'final_serverStateUpdates': !!serverStateUpdates,\n                    'phaseFromServer': phaseFromServer,\n                    'currentFrontendPhase': currentLessonPhase\n                });\n                if (serverStateUpdates && typeof serverStateUpdates === 'object') {\n                    console.log('[handleLessonPhaseUpdates] Found state updates:', Object.keys(serverStateUpdates));\n                    console.log('[handleLessonPhaseUpdates] FULL state updates object:', JSON.stringify(serverStateUpdates, null, 2));\n                    if (serverStateUpdates.new_phase) {\n                        phaseFromServer = serverStateUpdates.new_phase;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 PHASE TRANSITION DETECTED: \".concat(phaseFromServer));\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 Previous phase: \".concat(currentLessonPhase));\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 New phase: \".concat(phaseFromServer));\n                        // FORCE IMMEDIATE CONSOLE ALERT\n                        console.warn(\"\\uD83D\\uDE80 CRITICAL: PHASE CHANGING FROM \".concat(currentLessonPhase, \" TO \").concat(phaseFromServer));\n                    }\n                    if (serverStateUpdates.diagnostic_completed_this_session !== undefined) {\n                        diagCompleteFromServer = serverStateUpdates.diagnostic_completed_this_session;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDCCA Diagnostic completion status: \".concat(diagCompleteFromServer));\n                    }\n                    if (serverStateUpdates.assigned_level_for_teaching !== undefined) {\n                        assessedLevelFromServer = serverStateUpdates.assigned_level_for_teaching;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDFAF Teaching level assigned: \".concat(assessedLevelFromServer));\n                    }\n                    // Extract diagnostic-specific information for UI display\n                    if (serverStateUpdates.current_probing_level_number) {\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDCCB Current probing level: \".concat(serverStateUpdates.current_probing_level_number));\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>({\n                                    ...prev,\n                                    currentProbingLevel: serverStateUpdates.current_probing_level_number\n                                })\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.current_question_index !== undefined) {\n                        console.log(\"[handleLessonPhaseUpdates] ❓ Current question index: \".concat(serverStateUpdates.current_question_index));\n                        console.log(\"[handleLessonPhaseUpdates] ❓ Updating diagnostic progress with question index: \".concat(serverStateUpdates.current_question_index));\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    currentQuestionIndex: serverStateUpdates.current_question_index,\n                                    questionsCompleted: serverStateUpdates.current_question_index // Update both for consistency\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] ❓ Diagnostic progress after question index update:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.diagnostic_questions_completed) {\n                        console.log(\"[handleLessonPhaseUpdates] ✅ Questions completed: \".concat(serverStateUpdates.diagnostic_questions_completed));\n                        console.log(\"[handleLessonPhaseUpdates] ✅ Updating diagnostic progress with completed questions\");\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    questionsCompleted: serverStateUpdates.diagnostic_questions_completed\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] ✅ Diagnostic progress after completion update:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.diagnostic_completed_this_session) {\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Diagnostic phase completed!\");\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Marking diagnostic as complete in UI state\");\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    isComplete: true\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Final diagnostic progress state:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                }\n                if (phaseFromServer) {\n                    console.log('[handleLessonPhaseUpdates] 🎯 UPDATING LESSON PHASE TO:', phaseFromServer);\n                    console.log('[handleLessonPhaseUpdates] 🎯 Previous lesson phase was:', currentLessonPhase);\n                    // FORCE IMMEDIATE STATE UPDATE WITH DEBUGGING\n                    setCurrentLessonPhase({\n                        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prevPhase)=>{\n                            console.warn(\"\\uD83D\\uDD25 STATE UPDATE: PHASE CHANGING \".concat(prevPhase, \" → \").concat(phaseFromServer));\n                            return phaseFromServer;\n                        }\n                    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    console.log('[handleLessonPhaseUpdates] 🎯 Phase update completed');\n                    // ADDITIONAL: Force a re-render by updating a dummy state\n                    setTimeout({\n                        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": ()=>{\n                            console.warn(\"\\uD83D\\uDD25 POST-UPDATE CHECK: currentLessonPhase should now be \".concat(phaseFromServer));\n                        }\n                    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"], 100);\n                } else {\n                    console.warn('[handleLessonPhaseUpdates] ⚠️ NO PHASE UPDATE FOUND IN RESPONSE');\n                    console.warn('[handleLessonPhaseUpdates] ⚠️ Response structure might be different than expected');\n                }\n                if (diagCompleteFromServer !== undefined) {\n                    console.log('[handleLessonPhaseUpdates] Diagnostic complete status:', diagCompleteFromServer);\n                // Potentially set a local state like setIsDiagnosticComplete(diagCompleteFromServer);\n                }\n                if (assessedLevelFromServer !== undefined) {\n                    console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);\n                // Potentially set a local state like setTeachingLevel(assessedLevelFromServer);\n                }\n                if (phaseFromServer === LESSON_PHASE_COMPLETED) {\n                    var _response_data7;\n                    console.log('Lesson completed successfully (from phase update)');\n                    setLessonEnded(true);\n                    // Potentially show download link for notes if response.data.notes_download_url exists\n                    if (response === null || response === void 0 ? void 0 : (_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : _response_data7.notes_download_url) {\n                    // Show a button or link\n                    }\n                }\n                // ... other phase-specific UI logic ...\n                // COMPREHENSIVE SUMMARY LOGGING\n                console.log('[handleLessonPhaseUpdates] ==> SUMMARY OF STATE UPDATES:');\n                console.log('[handleLessonPhaseUpdates] Phase changed:', phaseFromServer ? \"\".concat(currentLessonPhase, \" → \").concat(phaseFromServer) : 'No change');\n                console.log('[handleLessonPhaseUpdates] Diagnostic complete:', diagCompleteFromServer);\n                console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);\n                console.log('[handleLessonPhaseUpdates] Current diagnostic progress:', diagnosticProgress);\n                console.log('[handleLessonPhaseUpdates] ==> END OF PROCESSING');\n            } catch (error) {\n                console.error('Error handling lesson phase update:', error);\n                console.error('Error details:', error);\n                setUiError('Failed to synchronize lesson state. Please try again.');\n            }\n        }\n    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"], [\n        setCurrentLessonPhase,\n        setLessonEnded,\n        setUiError,\n        currentLessonPhase,\n        diagnosticProgress\n    ]);\n    // Helper function to extract enhanced content from AI responses\n    const extractEnhancedContent = (response)=>{\n        var _response_data, _response_data_data, _response_data1;\n        if (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.enhanced_content) {\n            return response.data.enhanced_content;\n        }\n        if (response === null || response === void 0 ? void 0 : response.enhanced_content) {\n            return response.enhanced_content;\n        }\n        if (response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.enhanced_content) {\n            return response.data.data.enhanced_content;\n        }\n        const findContent = (obj)=>{\n            if (typeof obj === 'string') return obj;\n            if (Array.isArray(obj)) {\n                for (const item of obj){\n                    const found = findContent(item);\n                    if (found) return found;\n                }\n            } else if (obj && typeof obj === 'object') {\n                for(const key in obj){\n                    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                        const found = findContent(obj[key]);\n                        if (found) return found;\n                    }\n                }\n            }\n            return null;\n        };\n        return findContent(response) || \"I'm sorry, I couldn't process that response. Please try again.\";\n    };\n    // Helper function to get user-friendly error messages\n    const getErrorMessage = (error)=>{\n        if (axios__WEBPACK_IMPORTED_MODULE_17__[\"default\"].isAxiosError(error)) {\n            const axiosError = error;\n            if (axiosError.response) {\n                var _axiosError_response_data;\n                // Server responded with a status code outside 2xx\n                if (axiosError.response.status === 401) {\n                    return \"Your session has expired. Please log in again.\";\n                }\n                if (axiosError.response.status === 403) {\n                    return \"You don't have permission to perform this action.\";\n                }\n                if (axiosError.response.status === 404) {\n                    return \"The requested resource was not found.\";\n                }\n                if (axiosError.response.status === 429) {\n                    return \"Too many requests. Please wait a moment and try again.\";\n                }\n                if (axiosError.response.status >= 500) {\n                    return \"Our servers are experiencing issues. Please try again later.\";\n                }\n                return ((_axiosError_response_data = axiosError.response.data) === null || _axiosError_response_data === void 0 ? void 0 : _axiosError_response_data.message) || axiosError.message;\n            }\n            if (axiosError.request) {\n                // Request was made but no response received\n                return \"No response from server. Please check your internet connection.\";\n            }\n        }\n        // Handle other error types\n        if (error instanceof Error) {\n            return error.message;\n        }\n        if (typeof error === 'string') {\n            return error;\n        }\n        return \"An unknown error occurred. Please try again.\";\n    };\n    // Define handleAiInteraction first to avoid initialization issues\n    const handleAiInteraction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ClassroomContent.useCallback[handleAiInteraction]\": async function(messageContent) {\n            let isSystemMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, currentSessionIdForCall = arguments.length > 2 ? arguments[2] : void 0;\n            // Priority: 1. Explicitly provided ID 2. URL session ID 3. Context session ID\n            const sessionIdToUse = currentSessionIdForCall || sessionIdFromUrlProp || backendSessionId;\n            const logContext = \"[ClassroomContent handleAiInteraction] isSystem: \".concat(isSystemMessage, \", SessionToUse: \").concat(sessionIdToUse, \", LessonRef: \").concat(lessonRef, \", StudentId: \").concat(studentId);\n            console.log(logContext);\n            // Input validation\n            if (typeof sessionIdToUse !== 'string' || !sessionIdToUse.trim()) {\n                const error = \"Session ID is invalid ('\".concat(sessionIdToUse, \"') from context/prop.\");\n                setUiError(error);\n                console.error(logContext, \"CRITICAL ERROR:\", error);\n                toast({\n                    title: \"Session Error\",\n                    description: \"A valid session ID is required to continue the lesson. Please try refreshing.\",\n                    variant: \"destructive\"\n                });\n                setIsAiLoading(false);\n                return false;\n            }\n            if (!lessonRef || !studentId) {\n                const error = \"Lesson Reference or Student ID prop missing.\";\n                setUiError(error);\n                console.error(logContext, \"CRITICAL ERROR:\", error);\n                toast({\n                    title: \"Configuration Error\",\n                    description: error,\n                    variant: \"destructive\"\n                });\n                setIsAiLoading(false);\n                return false;\n            }\n            setIsAiLoading(true);\n            setUiError(null);\n            logInteraction(isSystemMessage ? 'system_message_ai' : 'user_message_ai', {\n                lessonRef,\n                sessionId: sessionIdToUse,\n                message: messageContent.substring(0, 100)\n            });\n            // Add user message to chat history if not a system message\n            if (!isSystemMessage) {\n                const currentUserMessage = {\n                    role: 'user',\n                    content: messageContent,\n                    timestamp: new Date().toISOString()\n                };\n                console.log(\"[ClassroomContent] \\uD83D\\uDC64 Adding user message to chat:\", {\n                    messagePreview: messageContent.substring(0, 50) + '...',\n                    messageLength: messageContent.length,\n                    currentPhase: currentLessonPhase,\n                    chatHistoryLength: chatHistory.length\n                });\n                setChatHistory({\n                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                            ...prev,\n                            currentUserMessage\n                        ]\n                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n            }\n            try {\n                const authHeaders = getAuthHeaders(backendSessionId);\n                if (!authHeaders['Authorization']) {\n                    throw new Error(\"Authentication token unavailable for AI interaction.\");\n                }\n                const requestBody = {\n                    student_id: studentId,\n                    lesson_ref: lessonRef,\n                    content_to_enhance: messageContent,\n                    country: countryProp || 'Nigeria',\n                    curriculum: curriculumProp || 'National Curriculum',\n                    grade: gradeProp,\n                    level: levelProp,\n                    subject: subjectProp,\n                    session_id: sessionIdToUse,\n                    chat_history: isSystemMessage ? [] : chatHistory.slice(-8)\n                };\n                console.log(\"[ClassroomContent] AI Interaction Request:\", {\n                    endpoint: AI_INTERACTION_ENDPOINT,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: {\n                        ...requestBody,\n                        chat_history: \"[\".concat(requestBody.chat_history.length, \" messages]\")\n                    }\n                });\n                const MAX_RETRIES = 2;\n                let lastError;\n                for(let attempt = 0; attempt <= MAX_RETRIES; attempt++){\n                    try {\n                        var _axiosResponse_data;\n                        const axiosResponse = await axios__WEBPACK_IMPORTED_MODULE_17__[\"default\"].post(AI_INTERACTION_ENDPOINT, requestBody, {\n                            headers: {\n                                ...authHeaders,\n                                'Content-Type': 'application/json'\n                            },\n                            timeout: 90000,\n                            validateStatus: {\n                                \"ClassroomContent.useCallback[handleAiInteraction]\": ()=>true // Always resolve the promise\n                            }[\"ClassroomContent.useCallback[handleAiInteraction]\"]\n                        });\n                        if (axiosResponse.status >= 200 && axiosResponse.status < 300) {\n                            let result;\n                            try {\n                                var _result_data, _result_data1, _result_data_state_updates, _result_data2, _result_state_updates, _result_state_updates1, _result_state_updates2, _result_data3;\n                                result = typeof axiosResponse.data === 'string' ? JSON.parse(axiosResponse.data) : axiosResponse.data;\n                                console.log('[ClassroomContent] Successfully parsed response:', result);\n                                // CRITICAL DEBUG: Show exact response structure for phase sync debugging\n                                console.warn('🔥 FRONTEND API RESPONSE STRUCTURE DEBUG:');\n                                console.warn('🔥 Response keys:', Object.keys(result || {}));\n                                console.warn('🔥 Response.data keys:', Object.keys((result === null || result === void 0 ? void 0 : result.data) || {}));\n                                console.warn('🔥 Response.data.state_updates keys:', Object.keys((result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.state_updates) || {}));\n                                console.warn('🔥 Current phase in response.data:', result === null || result === void 0 ? void 0 : (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.current_phase);\n                                console.warn('🔥 New phase in state_updates:', result === null || result === void 0 ? void 0 : (_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : (_result_data_state_updates = _result_data2.state_updates) === null || _result_data_state_updates === void 0 ? void 0 : _result_data_state_updates.new_phase);\n                                console.warn('🔥 Full result structure (first 500 chars):', JSON.stringify(result, null, 2).substring(0, 500));\n                                const enhancedContent = extractEnhancedContent(result);\n                                // Check for level adjustment notifications\n                                if (result === null || result === void 0 ? void 0 : (_result_state_updates = result.state_updates) === null || _result_state_updates === void 0 ? void 0 : _result_state_updates.level_adjustment_made) {\n                                    const adjustment = result.state_updates.level_adjustment_made;\n                                    console.log('[ClassroomContent] Level adjustment detected:', adjustment);\n                                    // Update current teaching level\n                                    setCurrentTeachingLevel(adjustment.to_level);\n                                    // Add to adjustment history\n                                    setLevelAdjustmentHistory({\n                                        \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                                                ...prev,\n                                                {\n                                                    timestamp: adjustment.timestamp,\n                                                    direction: adjustment.direction,\n                                                    fromLevel: adjustment.from_level,\n                                                    toLevel: adjustment.to_level,\n                                                    confidence: adjustment.confidence_score\n                                                }\n                                            ].slice(-10)\n                                    }[\"ClassroomContent.useCallback[handleAiInteraction]\"]); // Keep only last 10 adjustments\n                                    // Show level adjustment notification\n                                    toast({\n                                        title: \"Teaching Level \".concat(adjustment.direction === 'up' ? 'Increased' : 'Decreased'),\n                                        description: \"I've adjusted the lesson difficulty from Level \".concat(adjustment.from_level, \" to Level \").concat(adjustment.to_level, \" to better match your learning pace.\"),\n                                        variant: adjustment.direction === 'up' ? 'default' : 'destructive',\n                                        duration: 8000\n                                    });\n                                    // Log the level adjustment\n                                    logInteraction('level_adjustment_notification', {\n                                        lessonRef,\n                                        sessionId: sessionIdToUse,\n                                        direction: adjustment.direction,\n                                        fromLevel: adjustment.from_level,\n                                        toLevel: adjustment.to_level,\n                                        confidence: adjustment.confidence_score,\n                                        reasoning: adjustment.reasoning,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                }\n                                // Check for initial or updated teaching level in state updates\n                                if ((result === null || result === void 0 ? void 0 : (_result_state_updates1 = result.state_updates) === null || _result_state_updates1 === void 0 ? void 0 : _result_state_updates1.assigned_level_for_teaching) && currentTeachingLevel === null) {\n                                    setCurrentTeachingLevel(result.state_updates.assigned_level_for_teaching);\n                                }\n                                const aiMessage = {\n                                    role: 'assistant',\n                                    content: enhancedContent,\n                                    timestamp: new Date().toISOString()\n                                };\n                                console.log(\"[ClassroomContent] \\uD83D\\uDCAC Adding AI message to chat history:\", {\n                                    contentPreview: enhancedContent.substring(0, 100) + '...',\n                                    messageLength: enhancedContent.length,\n                                    currentChatLength: chatHistory.length,\n                                    hasStateUpdates: !!(result === null || result === void 0 ? void 0 : result.state_updates),\n                                    hasPhaseUpdate: !!((result === null || result === void 0 ? void 0 : (_result_state_updates2 = result.state_updates) === null || _result_state_updates2 === void 0 ? void 0 : _result_state_updates2.new_phase) || (result === null || result === void 0 ? void 0 : (_result_data3 = result.data) === null || _result_data3 === void 0 ? void 0 : _result_data3.current_phase))\n                                });\n                                setChatHistory({\n                                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>{\n                                        const newHistory = [\n                                            ...prev,\n                                            aiMessage\n                                        ];\n                                        console.log(\"[ClassroomContent] \\uD83D\\uDCDD Chat history updated. Total messages: \".concat(newHistory.length));\n                                        return newHistory;\n                                    }\n                                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                                handleLessonPhaseUpdates(result);\n                                return true;\n                            } catch (parseError) {\n                                throw new Error(\"Failed to parse server response: \".concat(parseError.message));\n                            }\n                        }\n                        if (axiosResponse.status === 401) {\n                            throw new Error('Authentication failed. Please log in again.');\n                        } else if (axiosResponse.status === 429) {\n                            const retryAfter = axiosResponse.headers['retry-after'] || 5;\n                            if (attempt < MAX_RETRIES) {\n                                await new Promise({\n                                    \"ClassroomContent.useCallback[handleAiInteraction]\": (resolve)=>setTimeout(resolve, Number(retryAfter) * 1000)\n                                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                                continue;\n                            }\n                            throw new Error('Server is busy. Please try again later.');\n                        }\n                        const errorMessage = ((_axiosResponse_data = axiosResponse.data) === null || _axiosResponse_data === void 0 ? void 0 : _axiosResponse_data.message) || axiosResponse.statusText || \"Request failed with status \".concat(axiosResponse.status);\n                        throw new Error(errorMessage);\n                    } catch (error) {\n                        lastError = error;\n                        if (attempt < MAX_RETRIES) {\n                            const backoffTime = Math.pow(2, attempt) * 1000;\n                            console.warn(\"Attempt \".concat(attempt + 1, \" failed, retrying in \").concat(backoffTime, \"ms...\"), error);\n                            await new Promise({\n                                \"ClassroomContent.useCallback[handleAiInteraction]\": (resolve)=>setTimeout(resolve, backoffTime)\n                            }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                        }\n                    }\n                }\n                throw lastError || new Error('Request failed after multiple attempts');\n                // This code is unreachable due to the throw statement above\n                // Keeping it for reference but it won't be executed\n                console.warn('This code should not be reachable - check for unreachable code');\n                return false;\n            } catch (error) {\n                var _error_message, _error_message1;\n                const errorMessage = getErrorMessage(error);\n                console.error(\"[ClassroomContent] Error during AI interaction:\", errorMessage, \"\\nFull error:\", error);\n                // Update UI with error state\n                setUiError(errorMessage);\n                // Add error message to chat for better UX\n                const errorMessageObj = {\n                    role: 'assistant',\n                    content: \"I'm sorry, I encountered an error: \".concat(errorMessage),\n                    timestamp: new Date().toISOString(),\n                    status: 'error'\n                };\n                setChatHistory({\n                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                            ...prev,\n                            errorMessageObj\n                        ]\n                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                // Show toast for non-timeout errors to avoid duplicate messages\n                if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && !((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Network Error'))) {\n                    toast({\n                        title: \"AI Service Error\",\n                        description: errorMessage,\n                        variant: \"destructive\",\n                        duration: 10000\n                    });\n                }\n                // Log the error for debugging\n                logInteraction('ai_interaction_error', {\n                    lessonRef,\n                    sessionId: sessionIdToUse,\n                    error: errorMessage,\n                    stack: error.stack,\n                    timestamp: new Date().toISOString()\n                });\n                return false;\n            } finally{\n                // Always ensure loading state is reset\n                setIsAiLoading(false);\n            }\n        }\n    }[\"ClassroomContent.useCallback[handleAiInteraction]\"], [\n        sessionIdFromUrlProp,\n        backendSessionId || undefined,\n        lessonRef,\n        studentId,\n        countryProp,\n        curriculumProp,\n        gradeProp,\n        levelProp,\n        subjectProp,\n        getAuthHeaders,\n        toast,\n        logInteraction,\n        chatHistory,\n        timeRemaining // Add timeRemaining to dependencies\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            const effectRunId = Date.now(); // For unique logging per run\n            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Running. Deps state:\"), {\n                sessionIdFromUrlProp,\n                backendSessionId,\n                lessonRef,\n                studentId,\n                isReady,\n                initialAiSent: initialAiInteractionSentRef.current,\n                chatHistoryLength: chatHistory.length,\n                isPageLoading\n            });\n            if (initialAiInteractionSentRef.current) {\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial AI interaction already attempted/sent. Current page loading: \").concat(isPageLoading, \".\"));\n                // If setup was done and page is still loading, ensure it stops.\n                if (isPageLoading) setIsPageLoading(false);\n                return;\n            }\n            if (!isReady) {\n                console.warn(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Waiting for context (ready: \").concat(isReady, \").\"));\n                if (!isPageLoading) setIsPageLoading(true); // Keep loading screen if waiting for these\n                return;\n            }\n            // Once user session and context are ready, validate critical props and session ID\n            if (!lessonRef || !studentId || !(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {\n                let missingInfo = [];\n                if (!lessonRef) missingInfo.push(\"lessonRefProp\");\n                if (!studentId) missingInfo.push(\"studentIdProp\");\n                if (!(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {\n                    missingInfo.push(\"valid sessionIdFromUrlProp (received: '\".concat(sessionIdFromUrlProp, \"', type: \").concat(typeof sessionIdFromUrlProp, \")\"));\n                }\n                const errorMessage = \"Critical info missing for ClassroomContent init: \".concat(missingInfo.join(', '), \". Please ensure the lesson was started correctly.\");\n                console.error(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Prerequisite check failed:\"), errorMessage, {\n                    lessonRefFromProp: lessonRef,\n                    studentIdFromProp: studentId,\n                    contextSessionId: backendSessionId\n                });\n                setUiError(errorMessage);\n                toast({\n                    title: \"Lesson Load Error\",\n                    description: errorMessage,\n                    variant: \"destructive\",\n                    duration: 10000\n                });\n                setIsPageLoading(false);\n                initialAiInteractionSentRef.current = true; // Mark as \"attempted\" to prevent loops\n                return;\n            }\n            // All prerequisites are met: lessonRef, studentId from props, and sessionIdFromUrlProp are valid.\n            // The initialSetupTriggeredRef is primarily to ensure the initial AI message is sent only once.\n            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] All prerequisites met. Using sessionIdFromUrlProp: '\").concat(sessionIdFromUrlProp, \"', lessonRef: '\").concat(lessonRef, \"'\"));\n            // Ensure isPageLoading is true before we potentially make an async call or set lessonDetails\n            if (!isPageLoading) setIsPageLoading(true);\n            if (!lessonDetails) {\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Populating lessonDetails.\"));\n                setLessonDetails({\n                    title: \"Lesson: \".concat(lessonRef),\n                    subject: subjectProp || 'N/A',\n                    grade: gradeProp || 'N/A',\n                    level: levelProp || 'N/A',\n                    country: countryProp || 'N/A',\n                    curriculum: curriculumProp || 'N/A',\n                    lessonRef: lessonRef\n                });\n            }\n            // Send initial system message if chat is empty and it hasn't been sent yet\n            if (chatHistory.length === 0 && !initialAiInteractionSentRef.current) {\n                initialAiInteractionSentRef.current = true;\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Triggering initial AI interaction (Diagnostic Start Message) with sessionIdFromUrlProp: \").concat(sessionIdFromUrlProp));\n                // CRITICAL FIX: Send diagnostic-specific message instead of generic system message\n                handleAiInteraction(\"Start diagnostic assessment\", true, sessionIdFromUrlProp).then({\n                    \"ClassroomContent.useEffect\": (success)=>{\n                        if (success) {\n                            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial system message AI interaction completed successfully.\"));\n                        } else {\n                            console.warn(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial system message AI interaction reported failure.\"));\n                        }\n                    }\n                }[\"ClassroomContent.useEffect\"]).catch({\n                    \"ClassroomContent.useEffect\": (err)=>{\n                        console.error(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Promise rejected from initial AI interaction:\"), err.message);\n                    }\n                }[\"ClassroomContent.useEffect\"]).finally({\n                    \"ClassroomContent.useEffect\": ()=>{\n                        setIsPageLoading(false);\n                    }\n                }[\"ClassroomContent.useEffect\"]);\n            } else {\n                // Initial message already sent or chat not empty, just ensure loading is false\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial AI message condition not met (chatHistory: \").concat(chatHistory.length, \", initialSentRef: \").concat(initialAiInteractionSentRef.current, \"). Setting page loading false.\"));\n                setIsPageLoading(false);\n            }\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        // Key dependencies that trigger re-evaluation of initial setup:\n        sessionIdFromUrlProp,\n        backendSessionId || undefined,\n        lessonRef,\n        studentId,\n        isReady,\n        // Other dependencies that, if they change, might necessitate re-evaluation or are used:\n        subjectProp,\n        gradeProp,\n        levelProp,\n        countryProp,\n        curriculumProp,\n        chatHistory.length,\n        handleAiInteraction,\n        lessonDetails,\n        isPageLoading,\n        toast // Stable\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            var _chatBottomRef_current;\n            (_chatBottomRef_current = chatBottomRef.current) === null || _chatBottomRef_current === void 0 ? void 0 : _chatBottomRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        chatHistory\n    ]);\n    // DEBUG: Monitor phase changes to ensure state updates are working\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            console.warn('🎯 PHASE CHANGE DETECTED:', {\n                previousPhase: 'tracked separately',\n                currentPhase: currentLessonPhase,\n                timestamp: new Date().toISOString()\n            });\n            // Force console alert for phase changes\n            if (currentLessonPhase) {\n                console.warn(\"\\uD83D\\uDE80 FRONTEND PHASE IS NOW: \".concat(currentLessonPhase));\n            }\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        currentLessonPhase\n    ]);\n    // --- Render Logic ---\n    if (isPageLoading && !initialAiInteractionSentRef.current && !uiError) {\n        console.log(\"[ClassroomContent Render] Showing loading spinner:\", {\n            isPageLoading,\n            initialAiSent: initialAiInteractionSentRef.current,\n            uiError\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"large\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 75\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-2\",\n                    children: \"Initializing Classroom...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 106\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 828,\n            columnNumber: 16\n        }, undefined);\n    }\n    if (uiError) {\n        console.log(\"[ClassroomContent Render] Showing UI error display:\", uiError);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-center items-center h-screen p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    title: \"Lesson Error\",\n                    message: uiError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 835,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    onClick: ()=>router.push('/dashboard'),\n                    className: \"mt-4\",\n                    children: \"Go to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 836,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 834,\n            columnNumber: 13\n        }, undefined);\n    }\n    const currentSessionId = sessionIdFromUrlProp || backendSessionId;\n    if (!currentSessionId || !lessonRef || !studentId || !lessonDetails) {\n        const missingRenderData = {\n            sessionIdFromUrl: sessionIdFromUrlProp,\n            contextSessionId: backendSessionId,\n            propLessonRef: lessonRef,\n            propStudentId: studentId,\n            currentLessonDetails: lessonDetails\n        };\n        console.error(\"[ClassroomContent Render] Critical data missing just before render. This indicates a logic flow issue.\", missingRenderData);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-center items-center h-screen p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    title: \"Content Load Error\",\n                    message: \"Essential lesson data is still missing after initialization attempts. Please try returning to the dashboard and starting the lesson again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 853,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    onClick: ()=>router.push('/dashboard'),\n                    className: \"mt-4\",\n                    children: \"Go to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 854,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 852,\n            columnNumber: 13\n        }, undefined);\n    }\n    // All checks passed, render the main classroom UI\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundaryFallback, {\n            error: new Error('Component error'),\n            resetErrorBoundary: ()=>window.location.reload()\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 862,\n            columnNumber: 30\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_LessonHeader__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    lessonTitle: lessonDetails.title,\n                    subjectName: lessonDetails.subject,\n                    gradeLevel: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.formatGradeLevelForDisplay)(lessonDetails.grade),\n                    currentTeachingLevel: currentTeachingLevel,\n                    levelAdjustmentHistory: levelAdjustmentHistory,\n                    onEnd: ()=>{\n                        if (backendSessionId && typeof backendSessionId === 'string' && lessonRefProp && studentIdProp) {\n                            logInteraction('lesson_ended_by_user', {\n                                lessonRef: lessonRefProp,\n                                sessionId: backendSessionId,\n                                timeElapsed: LESSON_DURATION_MINUTES * 60 - timeRemaining,\n                                timeRemaining\n                            });\n                        }\n                        router.push('/dashboard');\n                    },\n                    connectionStatus: isAiLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 882,\n                        columnNumber: 35\n                    }, void 0) : currentLessonPhase === LESSON_PHASE_COMPLETED ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-green-500 text-xs font-semibold\",\n                        children: [\n                            \"Lesson Complete! \",\n                            formatTime(timeRemaining),\n                            \" remaining\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 884,\n                        columnNumber: 25\n                    }, void 0) : uiError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 text-xs font-semibold\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 889,\n                        columnNumber: 25\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-500 text-xs font-semibold\",\n                                        children: [\n                                            formatTime(timeRemaining),\n                                            \" remaining\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    (()=>{\n                                        const timerStatus = getTimerStatus();\n                                        if (timerStatus.isInQuizPhase) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-600 text-xs\",\n                                                children: \"Quiz Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 48\n                                            }, void 0);\n                                        } else if (timerStatus.timeUntilQuizTransition <= 300) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-500 text-xs\",\n                                                children: [\n                                                    \"Quiz in \",\n                                                    formatTime(timerStatus.timeUntilQuizTransition)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 48\n                                            }, void 0);\n                                        }\n                                        return null;\n                                    })()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 29\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-2 bg-gray-200 rounded-full overflow-hidden relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full \".concat(timeRemaining > LESSON_DURATION_MINUTES * 60 * 0.5 ? 'bg-green-500' : timeRemaining > LESSON_DURATION_MINUTES * 60 * 0.25 ? 'bg-yellow-500' : 'bg-red-500'),\n                                        style: {\n                                            width: \"\".concat(Math.max(5, timeRemaining / (LESSON_DURATION_MINUTES * 60) * 100), \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 w-0.5 h-full bg-amber-500 z-10\",\n                                        style: {\n                                            left: \"\".concat(37.5 / 45 * 100, \"%\")\n                                        },\n                                        title: \"Quiz transition point (37.5 min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 33\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                lineNumber: 906,\n                                columnNumber: 29\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 891,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"hidden md:block w-64 lg:w-72 xl:w-1/4 p-4 border-r overflow-y-auto bg-slate-50 dark:bg-slate-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"shadow-md mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                className: \"text-lg text-slate-700 dark:text-slate-200\",\n                                                children: \"Lesson Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                            className: \"space-y-1.5 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Topic:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Subject:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.subject\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Grade:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.grade\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Curriculum:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.curriculum\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-slate-200 dark:border-slate-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 941,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Phase:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-blue-600 dark:text-blue-400\",\n                                                            children: currentLessonPhase || 'Initializing...'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 56\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Session ID:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 943,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: sessionIdFromUrlProp || backendSessionId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 943,\n                                                            columnNumber: 61\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiagnosticProgress__WEBPACK_IMPORTED_MODULE_16__.DiagnosticProgress, {\n                                    currentQuestionIndex: diagnosticProgress.currentQuestionIndex,\n                                    totalQuestions: diagnosticProgress.totalQuestions,\n                                    currentProbingLevel: diagnosticProgress.currentProbingLevel,\n                                    questionsCompleted: diagnosticProgress.questionsCompleted,\n                                    currentPhase: currentLessonPhase,\n                                    isComplete: diagnosticProgress.isComplete,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 21\n                                }, undefined),\n                                levelAdjustmentHistory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_LevelAdjustmentHistory__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    adjustments: levelAdjustmentHistory,\n                                    currentLevel: currentTeachingLevel,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 flex flex-col bg-white dark:bg-slate-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_TutorChat__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    lessonRef: lessonRef,\n                                    studentId: studentId,\n                                    sessionId: sessionIdFromUrlProp || backendSessionId || '',\n                                    chatMessages: chatHistory,\n                                    onSendMessage: handleAiInteraction,\n                                    isProcessing: isAiLoading,\n                                    error: uiError,\n                                    getAuthHeaders: getAuthHeaders,\n                                    lessonTitle: lessonDetails === null || lessonDetails === void 0 ? void 0 : lessonDetails.title,\n                                    role: \"instructor\" // or \"tutor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatBottomRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 968,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 930,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 863,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n        lineNumber: 862,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassroomContent, \"iSxiD6RNBz22xxxTSTnUfdo3bEA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession,\n        _hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__.useLessonTimer\n    ];\n});\n_c1 = ClassroomContent;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClassroomContent);\nvar _c, _c1;\n$RefreshReg$(_c, \"ErrorBoundaryFallback\");\n$RefreshReg$(_c1, \"ClassroomContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx\n"));

/***/ })

});