================================================================================
COMPREHENSIVE END-TO-END LESSON SYSTEM TEST REPORT
================================================================================
Student: <PERSON> (andrea_ugono_33305)
Subject: Computing, Primary 5
Test Start: 2025-06-25T14:11:02.144472
Total Interactions: 13

📊 PERFORMANCE METRICS
----------------------------------------
Average Response Time: 0.93s (Target: <2s)
Average AI Quality: 65.5% (Target: >70%)
Max Response Time: 1.84s
Min Response Time: 0.00s

🔄 PHASE TRANSITIONS
----------------------------------------
Interaction 1: diagnostic_start_probe → diagnostic_probing_L5_ask_q1 (✅ FORWARD)
Interaction 2: diagnostic_probing_L5_ask_q1 → diagnostic_probing_L5_eval_q1_ask_q2 (✅ FORWARD)
Interaction 3: diagnostic_probing_L5_eval_q1_ask_q2 → diagnostic_probing_L5_eval_q2_ask_q3 (✅ FORWARD)
Interaction 4: diagnostic_probing_L5_eval_q2_ask_q3 → diagnostic_probing_L5_eval_q3_ask_q4 (✅ FORWARD)
Interaction 5: diagnostic_probing_L5_eval_q3_ask_q4 → diagnostic_probing_L5_eval_q4_ask_q5 (✅ FORWARD)
Interaction 6: diagnostic_probing_L5_eval_q4_ask_q5 → diagnostic_probing_L5_eval_q5_decide_level (✅ FORWARD)
Interaction 7: teaching_start_level_6 → teaching (✅ FORWARD)
Interaction 8: teaching → teaching_practice (❌ BACKWARD)
Interaction 9: teaching_practice → quiz_initiate (✅ FORWARD)
Interaction 13: quiz_results → conclusion_summary (✅ FORWARD)

🔍 INTERACTION-BY-INTERACTION ANALYSIS
----------------------------------------
Interaction 1 ✅
  Phase: diagnostic_start_probe
  Input: Hi! I'm ready to start learning about computers....
  Response: Hi Andrea! I'm excited to help you learn all about Basic Computer Concepts today. We'll be exploring...
  Time: 1.81s, Quality: 70.0%

Interaction 2 ✅
  Phase: diagnostic_probing_L5_ask_q1
  Input: A computer is a machine that helps us work and play games. I...
  Response: That's a good start, Andrea! You've mentioned that a computer is a machine for work and games, with ...
  Time: 0.955s, Quality: 64.0%

Interaction 3 ✅
  Phase: diagnostic_probing_L5_eval_q1_ask_q2
  Input: Software is like programs that make the computer do things. ...
  Response: That's a great way to think about it, Andrea! Software is indeed the set of instructions (programs) ...
  Time: 1.025s, Quality: 62.0%

Interaction 4 ✅
  Phase: diagnostic_probing_L5_eval_q2_ask_q3
  Input: Input devices help us put information into the computer, lik...
  Response: That's a super clear explanation of input and output devices, Andrea! You've got the core idea exact...
  Time: 0.962s, Quality: 65.0%

Interaction 5 ✅
  Phase: diagnostic_probing_L5_eval_q3_ask_q4
  Input: The CPU is like the brain of the computer that processes inf...
  Response: That's a fantastic way to describe the CPU, Andrea! You're exactly right, it's the "brain" that hand...
  Time: 0.929s, Quality: 64.0%

Interaction 6 ✅
  Phase: diagnostic_probing_L5_eval_q4_ask_q5
  Input: Memory stores information temporarily while the computer is ...
  Response: Hello Andrea! Great job on completing the diagnostic questions about Basic Computer Concepts. It's c...
  Time: 1.044s, Quality: 82.0%

Interaction 7 ✅
  Phase: teaching_start_level_6
  Input: That makes sense! Can you tell me more about how computers p...
  Response: That's a great question, Andrea! Understanding how computers process information is a key part of le...
  Time: 1.837s, Quality: 74.0%

Interaction 8 ✅
  Phase: teaching
  Input: I understand now. What happens when we save a file?...
  Response: That's a fantastic question, Andrea! Understanding how files are saved is a key part of using comput...
  Time: 1.382s, Quality: 69.0%

Interaction 9 ✅
  Phase: teaching_practice
  Input: This is interesting! How do different input devices work?...
  Response: That's a fantastic question, Andrea! It’s great you’re thinking about how computers receive informat...
  Time: 1.183s, Quality: 78.0%

Interaction 10 ✅
  Phase: quiz_questions
  Input: The CPU processes the instructions...
  Response: I apologize, I'm having trouble accessing the quiz questions. Let's try restarting the quiz....
  Time: 0.004s, Quality: 50.0%

Interaction 11 ✅
  Phase: quiz_questions
  Input: RAM is temporary memory...
  Response: I apologize, I'm having trouble accessing the quiz questions. Let's try restarting the quiz....
  Time: 0.004s, Quality: 50.0%

Interaction 12 ✅
  Phase: quiz_questions
  Input: The hard drive stores files permanently...
  Response: I apologize, I'm having trouble accessing the quiz questions. Let's try restarting the quiz....
  Time: 0.003s, Quality: 50.0%

Interaction 13 ✅
  Phase: quiz_results
  Input: How did I do on the quiz?...
  Response: You did a fantastic job on the quiz, Andrea! You showed a great understanding of how computers can u...
  Time: 0.978s, Quality: 73.0%

✅ SUCCESS CRITERIA EVALUATION
----------------------------------------
complete_9_phases: ✅ PASS
zero_backward_transitions: ❌ FAIL
target_response_times: ✅ PASS
target_ai_quality: ❌ FAIL
authentic_ai_content: ✅ PASS

❌ ERRORS DETECTED
----------------------------------------
• Backward transition detected: teaching → teaching_practice
• Interaction 14 failed: not enough values to unpack (expected 3, got 2)

🎯 FINAL ASSESSMENT
----------------------------------------
❌ SOME SUCCESS CRITERIA NOT MET
⚠️  System requires additional fixes before deployment