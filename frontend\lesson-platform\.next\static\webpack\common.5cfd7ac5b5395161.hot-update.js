"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("common",{

/***/ "(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx":
/*!**************************************************!*\
  !*** ./src/app/providers/ClientToastWrapper.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToastWrapper: () => (/* binding */ ClientToastWrapper),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientToastWrapper,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ToastContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\nfunction ClientToastWrapper(param) {\n    let { children } = param;\n    _s();\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const toast = (props)=>{\n        const id = props.id || Math.random().toString(36).substr(2, 9);\n        // Add to toasts array\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    props\n                }\n            ]);\n        // Auto-dismiss after duration\n        const duration = props.duration || 5000;\n        setTimeout(()=>{\n            dismiss(id);\n        }, duration);\n        return {\n            id,\n            dismiss: ()=>dismiss(id)\n        };\n    };\n    const dismiss = (toastId)=>{\n        if (toastId) {\n            setToasts((prev)=>prev.filter((toast)=>toast.id !== toastId));\n        } else {\n            setToasts([]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toast,\n            dismiss\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map((param)=>{\n                    let { id, props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n              bg-white border rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px] relative\\n              \".concat(props.variant === 'destructive' ? 'border-red-200 bg-red-50' : '', \"\\n              \").concat(props.variant === 'success' ? 'border-green-200 bg-green-50' : '', \"\\n              \").concat(props.variant === 'warning' ? 'border-yellow-200 bg-yellow-50' : '', \"\\n            \"),\n                        children: [\n                            props.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold mb-1\",\n                                children: props.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 29\n                            }, this),\n                            props.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: props.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 35\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>dismiss(id),\n                                className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-600 w-6 h-6 flex items-center justify-center\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientToastWrapper, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\n_c = ClientToastWrapper;\n// Export the hook that components expect\nconst useToast = ()=>{\n    _s1();\n    const context = react__WEBPACK_IMPORTED_MODULE_1___default().useContext(ToastContext);\n    if (!context) {\n        console.error(\"useToast hook called outside of a ClientToastWrapper. Ensure ClientToastWrapper is placed correctly in your component tree (e.g., in client-providers.tsx).\");\n        throw new Error('useToast must be used within a ClientToastWrapper');\n    }\n    return context;\n};\n_s1(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ClientToastWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/authService.ts":
/*!********************************!*\
  !*** ./src/lib/authService.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_SESSION_KEY: () => (/* binding */ CURRENT_SESSION_KEY),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   findUserByUserId: () => (/* binding */ findUserByUserId),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getFreshAuthHeaders: () => (/* binding */ getFreshAuthHeaders),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserSession: () => (/* binding */ getUserSession),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   setupAuthListener: () => (/* binding */ setupAuthListener),\n/* harmony export */   setupAuthStateListener: () => (/* binding */ setupAuthStateListener),\n/* harmony export */   signInWithEmailAndPassword: () => (/* binding */ signInWithEmailAndPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   syncAuthState: () => (/* binding */ syncAuthState)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n// lib/authService.ts\n/* __next_internal_client_entry_do_not_use__ CURRENT_SESSION_KEY,saveUserSession,getUserSession,clearAuthData,signInWithEmailAndPassword,signOut,setupAuthStateListener,setupAuthListener,getAuthHeaders,getFreshAuthHeaders,refreshAuthToken,findUserByUserId,getUserRole,syncAuthState auto */ \n\n\n// Constants\nconst SESSION_KEY = 'user_session';\nconst CURRENT_SESSION_KEY = 'current_session'; // Export this constant\nconst TOKEN_KEY = 'token';\n/**\r\n * Save user session to localStorage with consistent keys\r\n */ const saveUserSession = (session)=>{\n    if (!session || !session.uid) return;\n    try {\n        // Add timestamp before saving\n        const sessionToSave = {\n            ...session,\n            tokenTimestamp: Date.now()\n        };\n        // Save the full session object with timestamp\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionToSave));\n        // CURRENT_SESSION_KEY should be set explicitly elsewhere when the *backend* session ID is known.\n        // Do not automatically set it to the Firebase UID here.\n        // localStorage.setItem(CURRENT_SESSION_KEY, session.uid); // Removed this line\n        localStorage.setItem(TOKEN_KEY, session.token); // Keep saving the token\n        console.log('Session object saved for UID:', session.uid);\n    } catch (error) {\n        console.error('Error saving user session:', error);\n    }\n};\n/**\r\n * Get the current user session from localStorage\r\n */ const getUserSession = ()=>{\n    try {\n        const sessionStr = localStorage.getItem(SESSION_KEY);\n        if (!sessionStr) return null;\n        return JSON.parse(sessionStr);\n    } catch (error) {\n        console.error('Failed to parse user session:', error);\n        return null;\n    }\n};\n/**\r\n * Clear all auth-related data from localStorage\r\n */ const clearAuthData = ()=>{\n    try {\n        localStorage.removeItem(SESSION_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n        localStorage.removeItem('authMethod');\n        localStorage.removeItem('viewing_as_child');\n        localStorage.removeItem('parent_id');\n        localStorage.removeItem('parent_name');\n        localStorage.removeItem('user_name');\n        localStorage.removeItem('parentEnrollmentMessage');\n    } catch (error) {\n        console.error('Error clearing auth data:', error);\n    }\n};\n/**\r\n * Sign in with email and password\r\n */ const signInWithEmailAndPassword = async (email, password)=>{\n    // Clear any previous auth state first\n    await signOut();\n    console.log(\"Attempting email/password sign-in\");\n    try {\n        var _auth_currentUser;\n        // Use Firebase's email/password auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        const user = userCredential.user;\n        // Get fresh token with custom claims\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const tokenResult = await user.getIdTokenResult(true);\n        const token = await ((_auth_currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.getIdToken(true, additionalClaims));\n        // Get user details from Firestore\n        const userDetails = await getUserDetailsFromFirestore(user);\n        // Create session\n        const userSession = {\n            uid: user.uid,\n            email: user.email,\n            name: user.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || null,\n            token: token,\n            role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n        };\n        // Save session\n        saveUserSession(userSession);\n        console.log(\"Authentication successful\");\n        return userSession;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Sign out the current user\r\n */ const signOut = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        clearAuthData();\n        console.log(\"User signed out\");\n    } catch (error) {\n        console.error(\"Sign out error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Set up a listener for auth state changes\r\n */ const setupAuthStateListener = (callback)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n};\n/**\r\n * Setup auth listener used by the session provider\r\n * This matches the signature expected by useSession\r\n */ const setupAuthListener = (setSession, setError, setIsLoading)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, async (user)=>{\n        console.log(\"Auth state changed:\", user ? \"User \".concat(user.uid) : \"No user\");\n        if (!user) {\n            setSession(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            var _auth_currentUser;\n            // Get fresh token for signed-in user\n            // Get custom claims including student_id if viewing as parent\n            const additionalClaims = {\n                student_id: localStorage.getItem('viewing_as_child') || undefined\n            };\n            const tokenResult = await user.getIdTokenResult(true);\n            const tokenString = await ((_auth_currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.getIdToken(true, additionalClaims));\n            if (!tokenString) {\n                throw new Error('Failed to get authentication token');\n            }\n            // Get user details from Firestore\n            const userDetails = await getUserDetailsFromFirestore(user);\n            // Create session object with token string\n            const userSession = {\n                uid: user.uid,\n                email: user.email || '',\n                name: user.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || '',\n                token: tokenString,\n                tokenResult,\n                role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n            };\n            // Set session and store backend session ID if available\n            setSession(userSession);\n            // If this is a new login response with sessionId, store it\n            const responseSessionId = user.sessionId;\n            if (responseSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, responseSessionId);\n            }\n        } catch (error) {\n            console.error(\"Error getting auth token:\", error);\n            setError(\"Failed to authenticate session\");\n            setSession(null);\n        } finally{\n            setIsLoading(false);\n        }\n    });\n};\n/**\r\n * Get auth headers for API requests\r\n * Accepts backendSessionId from context to avoid localStorage race conditions.\r\n */ const getAuthHeaders = (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    let currentToken = null;\n    let currentUid = null;\n    if (currentUser) {\n        var _currentUser_stsTokenManager;\n        currentUid = currentUser.uid;\n        // Attempt to get token from Firebase auth state first\n        currentToken = ((_currentUser_stsTokenManager = currentUser.stsTokenManager) === null || _currentUser_stsTokenManager === void 0 ? void 0 : _currentUser_stsTokenManager.accessToken) || null;\n    }\n    const storedSession = getUserSession();\n    // Prefer the ID passed from context, fall back to localStorage only if necessary (e.g., during initial load before context is ready)\n    const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n    // Use the effective backend session ID for the Session-ID header\n    if (effectiveBackendSessionId) {\n        headers['Session-ID'] = effectiveBackendSessionId;\n    } else {\n        // Fallback to UID only if backend session ID isn't available anywhere\n        const effectiveUid = currentUid || (storedSession === null || storedSession === void 0 ? void 0 : storedSession.uid);\n        if (effectiveUid) {\n            console.warn(\"Using UID (\".concat(effectiveUid, \") as Session-ID header fallback. Backend session ID not found in context or localStorage ('\").concat(CURRENT_SESSION_KEY, \"').\"));\n            headers['Session-ID'] = effectiveUid; // Still might be wrong, but it's the last resort\n        } else {\n            console.error(\"Cannot set Session-ID header: No backend session ID or user UID found.\");\n        }\n    }\n    // Prefer token from context's stored session if Firebase token is missing\n    const effectiveToken = currentToken || (storedSession === null || storedSession === void 0 ? void 0 : storedSession.token);\n    if (effectiveToken) {\n        headers['Authorization'] = \"Bearer \".concat(effectiveToken);\n    } else {\n        console.warn(\"Authorization token not found in Firebase state or stored session. This may cause authentication errors.\");\n        // Instead of completely failing, let's try to get token from localStorage as last resort\n        const fallbackToken = localStorage.getItem('token');\n        if (fallbackToken) {\n            console.warn(\"Using fallback token from localStorage\");\n            headers['Authorization'] = \"Bearer \".concat(fallbackToken);\n        } else {\n            console.error(\"No authentication token available from any source.\");\n        }\n    }\n    // Get role from stored session if available\n    const effectiveRole = (storedSession === null || storedSession === void 0 ? void 0 : storedSession.role) || 'student'; // Default role if not found\n    headers['X-User-Role'] = effectiveRole;\n    // CRITICAL FIX: Add testing mode header when no valid authentication is available\n    // This allows backend to generate console logs for lesson interactions during development\n    if (!effectiveToken || effectiveToken === 'undefined' || effectiveToken === 'null') {\n        console.warn(\"No valid authentication token found - enabling testing mode for backend logging\");\n        headers['X-Testing-Mode'] = 'true';\n    }\n    return headers;\n};\n/**\r\n * Get fresh auth headers with token refresh\r\n * Accepts backendSessionId from context.\r\n */ const getFreshAuthHeaders = async (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    if (currentUser) {\n        try {\n            const token = await currentUser.getIdToken(true); // Force refresh\n            headers['Authorization'] = \"Bearer \".concat(token);\n            // Use the effective backend session ID for the Session-ID header\n            const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n            if (effectiveBackendSessionId) {\n                headers['Session-ID'] = effectiveBackendSessionId;\n            } else {\n                // Fallback to UID only if backend session ID isn't available anywhere\n                console.warn(\"Using UID (\".concat(currentUser.uid, \") as Session-ID header fallback during fresh token request. Backend session ID not found.\"));\n                headers['Session-ID'] = currentUser.uid; // Last resort\n            }\n            const storedSession = getUserSession();\n            headers['X-User-Role'] = (storedSession === null || storedSession === void 0 ? void 0 : storedSession.role) || 'student'; // Default role\n        } catch (error) {\n            console.error(\"Error getting fresh token:\", error);\n            // Fallback to non-fresh headers if refresh fails\n            return getAuthHeaders(backendSessionIdFromContext);\n        }\n    } else {\n        // If no current user, return standard (likely unauthenticated) headers with testing mode\n        const fallbackHeaders = getAuthHeaders(backendSessionIdFromContext);\n        fallbackHeaders['X-Testing-Mode'] = 'true';\n        console.warn(\"No current user found - enabling testing mode for backend logging\");\n        return fallbackHeaders;\n    }\n    return headers;\n};\n/**\r\n * Refresh the auth token\r\n */ const refreshAuthToken = async ()=>{\n    try {\n        const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n        if (!currentUser) {\n            console.error(\"No current user found for token refresh\");\n            return null;\n        }\n        // Force refresh the token\n        const newToken = await currentUser.getIdToken(true);\n        // Update the stored session with new token\n        const storedSession = getUserSession();\n        if (storedSession) {\n            const updatedSession = {\n                ...storedSession,\n                token: newToken\n            };\n            saveUserSession(updatedSession);\n        }\n        return newToken;\n    } catch (error) {\n        console.error(\"Failed to refresh authentication token:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user details from Firestore\r\n */ async function getUserDetailsFromFirestore(user) {\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', user.uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                name: data.name,\n                role: data.role,\n                children: data.children || [],\n                parents: data.parents || []\n            };\n        }\n        // Fallback to check parents collection if not found in users\n        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'parents', user.uid);\n        const parentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(parentRef);\n        if (parentDoc.exists()) {\n            const data = parentDoc.data();\n            return {\n                name: data.name,\n                role: 'parent',\n                children: data.children || []\n            };\n        }\n        // Fallback to check students collection\n        const studentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'students', user.uid);\n        const studentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(studentRef);\n        if (studentDoc.exists()) {\n            const data = studentDoc.data();\n            return {\n                name: data.name,\n                role: 'student',\n                parents: data.parents || []\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching user details:\", error);\n        return null;\n    }\n}\n/**\r\n * Find user by userId (for child accounts)\r\n */ const findUserByUserId = async (userId)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, 'users');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (querySnapshot.empty) {\n            return null;\n        }\n        return querySnapshot.docs[0].data().email;\n    } catch (error) {\n        console.error(\"Error finding user by userId:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user role from Firestore\r\n */ const getUserRole = async (uid)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists() && userDoc.data().role) {\n            return userDoc.data().role;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user role:\", error);\n        return null;\n    }\n};\n/**\r\n * Sync Firebase auth state with local storage\r\n * This is crucial to fix the state mismatch issues\r\n */ const syncAuthState = async ()=>{\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    const storedSession = getUserSession();\n    // Case 1: Firebase has user but localStorage doesn't\n    if (currentUser && (!storedSession || storedSession.uid !== currentUser.uid)) {\n        console.log(\"Syncing: Firebase has user but localStorage doesn't match\");\n        const token = await currentUser.getIdToken(true);\n        const userDetails = await getUserDetailsFromFirestore(currentUser);\n        const userSession = {\n            uid: currentUser.uid,\n            email: currentUser.email,\n            name: currentUser.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || null,\n            token: token,\n            role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n        };\n        saveUserSession(userSession);\n        return userSession;\n    }\n    // Case 2: Firebase has no user but localStorage does\n    if (!currentUser && storedSession) {\n        console.log(\"Syncing: Firebase has no user but localStorage does\");\n        clearAuthData();\n        return null;\n    }\n    // Case 3: Both have matching user, check if token needs refresh\n    if (currentUser && storedSession && currentUser.uid === storedSession.uid) {\n        console.log(\"Syncing: Both have matching user\");\n        // Token is older than 30 minutes, refresh it\n        const tokenDate = new Date(storedSession.tokenTimestamp || 0);\n        const now = new Date();\n        const diffMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);\n        if (diffMinutes > 30) {\n            console.log(\"Token is older than 30 minutes, refreshing\");\n            await refreshAuthToken();\n        }\n    }\n    return storedSession;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/authService.ts\n"));

/***/ })

});