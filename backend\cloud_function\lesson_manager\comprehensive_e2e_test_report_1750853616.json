{"test_metadata": {"test_name": "Comprehensive End-to-End Lesson System Test", "student_id": "andrea_ugono_33305", "student_collection": "testing", "session_id": "e2e_test_1750853535", "started_at": "2025-06-25T13:12:15.091066", "completed_at": "2025-06-25T13:13:36.187359", "test_duration_minutes": 1.3516048833333334}, "success_criteria": {}, "performance_metrics": {"total_requests": 14, "avg_response_time": 4.572495409420559, "max_response_time": 7.191232681274414, "min_response_time": 3.489748239517212, "requests_under_2s": 0, "performance_threshold_met": false}, "ai_quality_analysis": {"total_assessments": 14, "avg_ai_quality": 61.089285714285715, "max_ai_quality": 68.5, "min_ai_quality": 54.50000000000001, "scores_above_70": 0, "quality_threshold_met": false}, "phase_coverage_analysis": {"expected_phases": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phases_completed": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phases_tracked": ["diagnostic_start_probe"], "coverage_percentage": 100.0, "missing_phases": [], "coverage_threshold_met": true}, "diagnostic_accuracy_validation": {"questions_asked": 5, "level_adjustments": [], "scoring_accuracy": false, "final_level": 1}, "state_update_compliance": {"total_blocks_found": 0, "blocks_found_in": [], "compliance_rate": 0.0}, "bug_analysis": {"total_bugs": 23, "bugs_by_severity": {"high": 14, "medium": 9}, "bugs_by_type": {"ai_instruction_compliance": 5, "performance": 9, "ai_quality": 9}, "critical_bugs": [], "high_priority_bugs": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 57.8% below 70.0% threshold in diagnostic"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.0% below 70.0% threshold in teaching_start"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 68.5% below 70.0% threshold in teaching"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_initiate"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_questions"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_results"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.5% below 70.0% threshold in conclusion_summary"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 54.5% below 70.0% threshold in final_assessment_pending"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 62.7% below 70.0% threshold in completed"}], "all_bugs": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "performance", "severity": "medium", "description": "Response time 3.49s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 57.8% below 70.0% threshold in diagnostic"}, {"type": "performance", "severity": "medium", "description": "Response time 3.62s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.0% below 70.0% threshold in teaching_start"}, {"type": "performance", "severity": "medium", "description": "Response time 4.19s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 68.5% below 70.0% threshold in teaching"}, {"type": "performance", "severity": "medium", "description": "Response time 6.87s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_initiate"}, {"type": "performance", "severity": "medium", "description": "Response time 4.06s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_questions"}, {"type": "performance", "severity": "medium", "description": "Response time 3.90s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_results"}, {"type": "performance", "severity": "medium", "description": "Response time 4.78s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.5% below 70.0% threshold in conclusion_summary"}, {"type": "performance", "severity": "medium", "description": "Response time 3.76s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 54.5% below 70.0% threshold in final_assessment_pending"}, {"type": "performance", "severity": "medium", "description": "Response time 4.37s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 62.7% below 70.0% threshold in completed"}]}, "errors_encountered": [], "raw_test_data": {"started_at": "2025-06-25T13:12:15.091066", "firebase_auth": true, "phase_coverage": 100.0, "phases_completed": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phase_transitions": ["diagnostic_start_probe"], "ai_quality_scores": [64.75, 61.0, 64.75, 61.0, 59.75, 57.75, 63.0, 68.5, 57.99999999999999, 57.99999999999999, 57.99999999999999, 63.5, 54.50000000000001, 62.74999999999999], "response_times": [7.191232681274414, 4.622148275375366, 4.845719814300537, 4.437338590621948, 3.8857967853546143, 3.489748239517212, 3.6167969703674316, 4.187292098999023, 6.868582487106323, 4.056262731552124, 3.898857831954956, 4.7801783084869385, 3.763683795928955, 4.371297121047974], "diagnostic_completion": false, "diagnostic_scores": [], "state_update_blocks": [], "session_persistence": false, "errors": [], "bugs_found": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "performance", "severity": "medium", "description": "Response time 3.49s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 57.8% below 70.0% threshold in diagnostic"}, {"type": "performance", "severity": "medium", "description": "Response time 3.62s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.0% below 70.0% threshold in teaching_start"}, {"type": "performance", "severity": "medium", "description": "Response time 4.19s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 68.5% below 70.0% threshold in teaching"}, {"type": "performance", "severity": "medium", "description": "Response time 6.87s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_initiate"}, {"type": "performance", "severity": "medium", "description": "Response time 4.06s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_questions"}, {"type": "performance", "severity": "medium", "description": "Response time 3.90s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 58.0% below 70.0% threshold in quiz_results"}, {"type": "performance", "severity": "medium", "description": "Response time 4.78s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.5% below 70.0% threshold in conclusion_summary"}, {"type": "performance", "severity": "medium", "description": "Response time 3.76s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 54.5% below 70.0% threshold in final_assessment_pending"}, {"type": "performance", "severity": "medium", "description": "Response time 4.37s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 62.7% below 70.0% threshold in completed"}], "success_criteria_met": {"phase_coverage_100": true, "diagnostic_completion": false, "avg_response_time_under_2s": false, "avg_ai_quality_over_70": false, "no_critical_bugs": true}, "performance_metrics": {}, "ai_quality_analysis": {}, "diagnostic_accuracy": {"questions_asked": 5, "level_adjustments": [], "scoring_accuracy": false, "final_level": 1}, "completed_at": "2025-06-25T13:13:36.187359"}, "recommendations": [{"priority": "HIGH", "category": "Performance", "issue": "Average response time 4.57s exceeds 2.0s threshold", "recommendation": "Optimize API response times through caching, database indexing, or code optimization"}, {"priority": "HIGH", "category": "AI Quality", "issue": "Average AI quality 61.1% below 70% threshold", "recommendation": "Improve AI instructor prompts, add personalization, and enhance educational effectiveness"}, {"priority": "MEDIUM", "category": "AI Compliance", "issue": "Missing mandatory AI state update blocks detected", "recommendation": "Ensure AI instructor generates proper state update blocks in required format"}]}