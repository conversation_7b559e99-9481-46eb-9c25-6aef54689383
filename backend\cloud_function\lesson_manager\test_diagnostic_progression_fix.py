#!/usr/bin/env python3
"""
Test script to validate the diagnostic progression fix for diagnostic_start_probe phase.
This script tests the specific issue where AI instructor gets stuck in diagnostic_start_probe
instead of progressing to diagnostic questions.
"""

import requests
import json
import time
import sys
from datetime import datetime

# Test configuration
BASE_URL = "http://127.0.0.1:5000"
STUDENT_ID = "andrea_ugono_33305"
STUDENT_COLLECTION = "testing"
STUDENT_NAME = "Andrea"

def log_test_step(message, level="INFO"):
    """Log test progress with timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def test_diagnostic_progression():
    """Test the diagnostic progression fix"""
    log_test_step("Starting diagnostic progression test", "TEST")
    
    # Create a new session
    session_id = f"test_diagnostic_fix_{int(time.time())}"
    log_test_step(f"Created session: {session_id}")
    
    # Test data for lesson interaction
    test_data = {
        "student_name": STUDENT_NAME,
        "student_response": "",
        "conversation_context": {
            "session_id": session_id,
            "user_id": STUDENT_ID,
            "student_collection": STUDENT_COLLECTION,
            "lesson_module": "P5-AI-002",
            "lesson_phase": "diagnostic_start_probe"
        },
        "action": "start_lesson"
    }
    
    try:
        # Step 1: Start the lesson
        log_test_step("Step 1: Starting lesson...")
        response = requests.post(f"{BASE_URL}/lesson/enhance", json=test_data, timeout=30)
        
        if response.status_code != 200:
            log_test_step(f"Failed to start lesson: {response.status_code} - {response.text}", "ERROR")
            return False
            
        result = response.json()
        log_test_step(f"Lesson started successfully. Phase: {result.get('state', {}).get('lesson_phase', 'unknown')}")
        
        # Step 2: Simulate student responses to test progression
        interactions = [
            "I'm ready",
            "Yes, let's start",
            "I want to learn about AI",
            "Tell me more"  # This should trigger forced progression after 3+ interactions
        ]
        
        current_phase = result.get('state', {}).get('lesson_phase', 'diagnostic_start_probe')
        
        for i, student_response in enumerate(interactions, 1):
            log_test_step(f"Step {i+1}: Student responds: '{student_response}'")
            
            # Update test data with student response and current context
            test_data.update({
                "student_response": student_response,
                "conversation_context": result.get('state', {}),
                "action": "continue_lesson"
            })
            
            # Send student response
            response = requests.post(f"{BASE_URL}/lesson/enhance", json=test_data, timeout=30)
            
            if response.status_code != 200:
                log_test_step(f"Failed at interaction {i}: {response.status_code} - {response.text}", "ERROR")
                continue
                
            result = response.json()
            new_phase = result.get('state', {}).get('lesson_phase', 'unknown')
            ai_response = result.get('response', '')
            
            log_test_step(f"AI Response: {ai_response[:100]}...")
            log_test_step(f"Phase transition: {current_phase} → {new_phase}")
            
            # Check if we've progressed out of diagnostic_start_probe
            if new_phase != 'diagnostic_start_probe':
                log_test_step(f"✅ SUCCESS: Progressed out of diagnostic_start_probe to {new_phase}", "SUCCESS")
                
                # Validate that we moved to the correct next phase
                if 'diagnostic_probing' in new_phase and 'ask_q1' in new_phase:
                    log_test_step("✅ SUCCESS: Correctly transitioned to first diagnostic question phase", "SUCCESS")
                    return True
                else:
                    log_test_step(f"⚠️ WARNING: Unexpected phase transition to {new_phase}", "WARNING")
                    return True  # Still consider it a success since we progressed
            
            current_phase = new_phase
            
            # Check for forced progression indicators
            state = result.get('state', {})
            if state.get('diagnostic_start_probe_forced_progression'):
                log_test_step(f"✅ FORCED PROGRESSION TRIGGERED: After {state.get('diagnostic_start_probe_interaction_count', 0)} interactions", "SUCCESS")
            
            time.sleep(1)  # Brief pause between interactions
        
        # If we're still stuck after all interactions
        if current_phase == 'diagnostic_start_probe':
            log_test_step("❌ FAILURE: Still stuck in diagnostic_start_probe after all interactions", "ERROR")
            return False
            
        log_test_step("✅ Test completed successfully", "SUCCESS")
        return True
        
    except Exception as e:
        log_test_step(f"Test failed with exception: {str(e)}", "ERROR")
        return False

def main():
    """Main test function"""
    log_test_step("Diagnostic Progression Fix Test", "HEADER")
    log_test_step("=" * 50, "HEADER")
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            log_test_step("✅ Server is running")
        else:
            log_test_step("❌ Server health check failed", "ERROR")
            return False
    except Exception as e:
        log_test_step(f"❌ Cannot connect to server: {e}", "ERROR")
        return False
    
    # Run the test
    success = test_diagnostic_progression()
    
    log_test_step("=" * 50, "HEADER")
    if success:
        log_test_step("🎉 DIAGNOSTIC PROGRESSION FIX TEST PASSED", "SUCCESS")
        return True
    else:
        log_test_step("💥 DIAGNOSTIC PROGRESSION FIX TEST FAILED", "ERROR")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
