"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/framer-motion";
exports.ids = ["vendor-chunks/framer-motion"];
exports.modules = {

/***/ "(ssr)/./node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionConfig: () => (/* binding */ MotionConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(ssr)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _render_dom_utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../render/dom/utils/filter-props.mjs */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* __next_internal_client_entry_do_not_use__ MotionConfig auto */ \n\n\n\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */ function MotionConfig({ children, isValidProp, ...config }) {\n    isValidProp && (0,_render_dom_utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_2__.loadExternalIsValidProp)(isValidProp);\n    /**\n     * Inherit props from any parent MotionConfig components\n     */ config = {\n        ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n        ...config\n    };\n    /**\n     * Don't allow isStatic to change between renders as it affects how many hooks\n     * motion components fire.\n     */ config.isStatic = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant)({\n        \"MotionConfig.useConstant\": ()=>config.isStatic\n    }[\"MotionConfig.useConstant\"]);\n    /**\n     * Creating a new config context object will re-render every `motion` component\n     * every time it renders. So we only want to create a new one sparingly.\n     */ const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"MotionConfig.useMemo[context]\": ()=>config\n    }[\"MotionConfig.useMemo[context]\"], [\n        JSON.stringify(config.transition),\n        config.transformPagePoint,\n        config.reducedMotion\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext.Provider, {\n        value: context,\n        children: children\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionConfigContext: () => (/* binding */ MotionConfigContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ MotionConfigContext auto */ \n/**\n * @public\n */ const MotionConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    transformPagePoint: (p)=>p,\n    isStatic: false,\n    reducedMotion: \"never\"\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2NvbnRleHQvTW90aW9uQ29uZmlnQ29udGV4dC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7eUVBQ3NDO0FBRXRDOztDQUVDLEdBQ0QsTUFBTUMsb0NBQXNCRCxvREFBYUEsQ0FBQztJQUN0Q0Usb0JBQW9CLENBQUNDLElBQU1BO0lBQzNCQyxVQUFVO0lBQ1ZDLGVBQWU7QUFDbkI7QUFFK0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXGNvbnRleHRcXE1vdGlvbkNvbmZpZ0NvbnRleHQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBAcHVibGljXG4gKi9cbmNvbnN0IE1vdGlvbkNvbmZpZ0NvbnRleHQgPSBjcmVhdGVDb250ZXh0KHtcbiAgICB0cmFuc2Zvcm1QYWdlUG9pbnQ6IChwKSA9PiBwLFxuICAgIGlzU3RhdGljOiBmYWxzZSxcbiAgICByZWR1Y2VkTW90aW9uOiBcIm5ldmVyXCIsXG59KTtcblxuZXhwb3J0IHsgTW90aW9uQ29uZmlnQ29udGV4dCB9O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJNb3Rpb25Db25maWdDb250ZXh0IiwidHJhbnNmb3JtUGFnZVBvaW50IiwicCIsImlzU3RhdGljIiwicmVkdWNlZE1vdGlvbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidMotionProp: () => (/* binding */ isValidMotionProp)\n/* harmony export */ });\n/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterProps: () => (/* binding */ filterProps),\n/* harmony export */   loadExternalIsValidProp: () => (/* binding */ loadExternalIsValidProp)\n/* harmony export */ });\n/* harmony import */ var _motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../motion/utils/valid-prop.mjs */ \"(ssr)/./node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs\");\n\n\nlet shouldForward = (key) => !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key);\nfunction loadExternalIsValidProp(isValidProp) {\n    if (!isValidProp)\n        return;\n    // Explicitly filter our events\n    shouldForward = (key) => key.startsWith(\"on\") ? !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n    /**\n     * We attempt to import this package but require won't be defined in esm environments, in that case\n     * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n     * in favour of explicit injection.\n     */\n    loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n}\ncatch {\n    // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n    const filteredProps = {};\n    for (const key in props) {\n        /**\n         * values is considered a valid prop by Emotion, so if it's present\n         * this will be rendered out to the DOM unless explicitly filtered.\n         *\n         * We check the type as it could be used with the `feColorMatrix`\n         * element, which we support.\n         */\n        if (key === \"values\" && typeof props.values === \"object\")\n            continue;\n        if (shouldForward(key) ||\n            (forwardMotionProps === true && (0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key)) ||\n            (!isDom && !(0,_motion_utils_valid_prop_mjs__WEBPACK_IMPORTED_MODULE_0__.isValidMotionProp)(key)) ||\n            // If trying to use native HTML drag events, forward drag listeners\n            (props[\"draggable\"] &&\n                key.startsWith(\"onDrag\"))) {\n            filteredProps[key] =\n                props[key];\n        }\n    }\n    return filteredProps;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-constant.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConstant: () => (/* binding */ useConstant)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    if (ref.current === null) {\n        ref.current = init();\n    }\n    return ref.current;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS1jb25zdGFudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7O0FBRS9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsNkNBQU07QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHV0aWxzXFx1c2UtY29uc3RhbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBDcmVhdGVzIGEgY29uc3RhbnQgdmFsdWUgb3ZlciB0aGUgbGlmZWN5Y2xlIG9mIGEgY29tcG9uZW50LlxuICpcbiAqIEV2ZW4gaWYgYHVzZU1lbW9gIGlzIHByb3ZpZGVkIGFuIGVtcHR5IGFycmF5IGFzIGl0cyBmaW5hbCBhcmd1bWVudCwgaXQgZG9lc24ndCBvZmZlclxuICogYSBndWFyYW50ZWUgdGhhdCBpdCB3b24ndCByZS1ydW4gZm9yIHBlcmZvcm1hbmNlIHJlYXNvbnMgbGF0ZXIgb24uIEJ5IHVzaW5nIGB1c2VDb25zdGFudGBcbiAqIHlvdSBjYW4gZW5zdXJlIHRoYXQgaW5pdGlhbGlzZXJzIGRvbid0IGV4ZWN1dGUgdHdpY2Ugb3IgbW9yZS5cbiAqL1xuZnVuY3Rpb24gdXNlQ29uc3RhbnQoaW5pdCkge1xuICAgIGNvbnN0IHJlZiA9IHVzZVJlZihudWxsKTtcbiAgICBpZiAocmVmLmN1cnJlbnQgPT09IG51bGwpIHtcbiAgICAgICAgcmVmLmN1cnJlbnQgPSBpbml0KCk7XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudDtcbn1cblxuZXhwb3J0IHsgdXNlQ29uc3RhbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\n");

/***/ })

};
;