[2025-06-25 13:16:20,404] INFO - main - main.py:617 - Logging configuration complete with immediate console output
[2025-06-25 13:16:20,408] INFO - main - main.py:693 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-25 13:16:20,410] INFO - main - main.py:872 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-25 13:16:20,418] INFO - main - main.py:901 - Phase transition fixes imported successfully
[2025-06-25 13:16:20,426] INFO - main - main.py:3186 - Successfully imported utils functions
[2025-06-25 13:16:20,428] INFO - main - main.py:3194 - Successfully imported extract_ai_state functions
[2025-06-25 13:16:20,439] INFO - main - main.py:3644 - FLASK: Using unified Firebase initialization approach...
[2025-06-25 13:16:20,442] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-25 13:16:20,514] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-25 13:16:20,516] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-25 13:16:21,117] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-25 13:16:21,119] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-25 13:16:21,633] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-25 13:16:22,494] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-25 13:16:22,495] INFO - main - main.py:3652 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-25 13:16:22,496] INFO - main - main.py:3742 - Gemini API will be initialized on first use (lazy loading).
[2025-06-25 13:16:22,512] INFO - main - main.py:1042 - Successfully imported timetable_generator functions
[2025-06-25 13:16:22,519] DEBUG - google.auth._default - _default.py:256 - Checking C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json for explicit credentials as part of auth process...
[2025-06-25 13:16:22,576] DEBUG - google.auth._default - _default.py:256 - Checking C:\Users\<USER>\OneDrive\Desktop\Desktop\firebase_upload_project\solynta-academy-firebase-adminsdk-ys8ys-977bc8cd7b.json for explicit credentials as part of auth process...
[2025-06-25 13:16:22,647] INFO - main - main.py:16518 - Google Cloud Storage client initialized successfully.
