[2025-06-25 13:35:26,726] INFO - __main__ - main.py:617 - Logging configuration complete with immediate console output
[2025-06-25 13:35:26,730] INFO - __main__ - main.py:693 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-25 13:35:26,732] INFO - __main__ - main.py:872 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-25 13:35:26,740] INFO - __main__ - main.py:901 - Phase transition fixes imported successfully
[2025-06-25 13:35:26,745] INFO - __main__ - main.py:3192 - Successfully imported utils functions
[2025-06-25 13:35:26,747] INFO - __main__ - main.py:3200 - Successfully imported extract_ai_state functions
[2025-06-25 13:35:26,753] INFO - __main__ - main.py:3650 - FLASK: Using unified Firebase initialization approach...
[2025-06-25 13:35:26,757] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-25 13:35:26,824] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-25 13:35:26,824] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-25 13:35:27,427] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-25 13:35:27,432] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-25 13:35:27,962] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-25 13:35:28,794] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-25 13:35:28,794] INFO - __main__ - main.py:3658 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-25 13:35:28,794] INFO - __main__ - main.py:3748 - Gemini API will be initialized on first use (lazy loading).
[2025-06-25 13:35:28,816] INFO - __main__ - main.py:1042 - Successfully imported timetable_generator functions
[2025-06-25 13:35:28,822] INFO - __main__ - main.py:16469 - Starting Lesson Manager Service...
[2025-06-25 13:35:28,823] INFO - __main__ - main.py:16475 - Flask server starting on host 0.0.0.0, port 5000
[2025-06-25 13:35:28,823] INFO - __main__ - main.py:16476 - Debug mode: ON
[2025-06-25 13:35:28,891] INFO - werkzeug - _internal.py:97 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
[2025-06-25 13:35:28,891] INFO - werkzeug - _internal.py:97 - [33mPress CTRL+C to quit[0m
[2025-06-25 13:37:05,418] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-25 13:37:05,446] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "e605850a-7164-4e2e-9d68-8bab1a032f97", "timestamp": "2025-06-25T12:37:05.435398+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "subject": "Basic Science and Technology", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-25 13:37:05,451] INFO - __main__ - main.py:12327 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3NzQ4NTAwMmYwNWJlMDI2N2VmNDU5ZjViNTEzNTMzYjVjNThjMTIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.zsBv2_O22Bz4yZzAYyvxbf4oEoZDw7Mw2H1Uw-mVJttDm1RbvEA198RtE9zi_o-mDX19qFEruwEir-HK31osHwAvuw8YNlShvqTA5Vn9LnibEnJ79lq6atRtq7cVsKcymCeMh8KiDxEy8h39XOE8Oka_cVQ7Rr8WlqU8rXfg7SMeI3yYapIT8kTVlc8dmHHi58LL0pdQ0AzvNrsIZI8IWL9ABIim5koU-c9HthfPNbQIy918xmJjH_790CWZkLVtPK6Ilkev91xuPrmEUPmdMQPtKk2OxzRy5dMbSXY__zDTj9-tHFugSr7jSN4N8C5RPgWu0qcC1rCT8caB9aYFdw', 'User-Agent': 'axios/1.9.0', 'Content-Length': '231', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-25 13:37:05,456] INFO - auth_decorator - auth_decorator.py:39 - [e605850a-7164-4e2e-9d68-8bab1a032f97][require_auth] Decorator invoked for path: /lesson-content
[2025-06-25 13:37:05,458] INFO - auth_decorator - auth_decorator.py:56 - [e605850a-7164-4e2e-9d68-8bab1a032f97][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:37:05,459] WARNING - __main__ - main.py:12334 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-25 13:37:05,462] INFO - __main__ - main.py:12341 - [e605850a-7164-4e2e-9d68-8bab1a032f97] lesson_content_and_quiz invoked by /lesson-content
[2025-06-25 13:37:05,464] INFO - __main__ - main.py:12357 - Lesson content request by authenticated user: dev_student_uid_123 (Development Student)
[2025-06-25 13:37:05,465] INFO - __main__ - main.py:12361 - !!! [lesson_content_and_quiz] [e605850a-7164-4e2e-9d68-8bab1a032f97] RAW BODY AT START (bytes length: 231): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-BST-002","subject":"Basic Science and Technology","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-25 13:37:05,470] INFO - __main__ - main.py:1177 - [e605850a-7164-4e2e-9d68-8bab1a032f97] fetch_lesson_data: Fetching lesson with parameters:
[2025-06-25 13:37:05,471] INFO - __main__ - main.py:1178 -   • Country: Nigeria
[2025-06-25 13:37:05,471] INFO - __main__ - main.py:1179 -   • Curriculum: National Curriculum
[2025-06-25 13:37:05,472] INFO - __main__ - main.py:1180 -   • Grade: Primary 5
[2025-06-25 13:37:05,472] INFO - __main__ - main.py:1181 -   • Level: P5
[2025-06-25 13:37:05,472] INFO - __main__ - main.py:1182 -   • Subject: Basic Science and Technology
[2025-06-25 13:37:05,473] INFO - __main__ - main.py:1183 -   • Lesson ID: P5-BST-002
[2025-06-25 13:37:05,474] INFO - __main__ - main.py:1202 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Basic Science and Technology/lessonRef/P5-BST-002
[2025-06-25 13:37:08,132] INFO - __main__ - main.py:1266 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Successfully retrieved document with keys: ['additionalNotes', 'digitalMaterials', 'subject', 'metadata', 'last_accessed', 'existingAssessments', 'session_started_at', 'learningObjectives', 'last_updated', 'gradeLevel', 'topic', 'access_count', 'lessonTimeLength', 'introduction', 'quizzesAndAssessments', 'content', 'session_id', 'instructionalSteps', 'id', 'lessonRef', 'lessonTitle', 'extensionActivities', 'country', 'theme', 'adaptiveStrategies', 'curriculumType', 'conclusion', 'quizzes']
[2025-06-25 13:37:08,141] INFO - __main__ - main.py:1417 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Extracted 10 key concepts: ['Identify', 'three', 'states', 'matter', 'Understand']...
[2025-06-25 13:37:08,141] INFO - __main__ - main.py:1497 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Universal content extraction: 406 characters from 3 steps
[2025-06-25 13:37:08,141] INFO - __main__ - main.py:1534 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Universal conversion: 3 steps → 3 sections
[2025-06-25 13:37:08,142] INFO - __main__ - main.py:1352 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Field mapping completed:
[2025-06-25 13:37:08,142] INFO - __main__ - main.py:1353 -   - Subject: Basic Science and Technology
[2025-06-25 13:37:08,142] INFO - __main__ - main.py:1354 -   - Topic: States of Matter
[2025-06-25 13:37:08,142] INFO - __main__ - main.py:1355 -   - Grade: Primary 5
[2025-06-25 13:37:08,144] INFO - __main__ - main.py:1356 -   - Key Concepts: 10 extracted
[2025-06-25 13:37:08,145] INFO - __main__ - main.py:1357 -   - Instructional Steps: 3
[2025-06-25 13:37:08,147] INFO - __main__ - main.py:1555 - [e605850a-7164-4e2e-9d68-8bab1a032f97] ✅ Universal content structure recognized: instructionalSteps (3 steps)
[2025-06-25 13:37:08,148] INFO - __main__ - main.py:1570 - [e605850a-7164-4e2e-9d68-8bab1a032f97] ✅ All required fields present after universal mapping
[2025-06-25 13:37:08,148] INFO - __main__ - main.py:1275 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Successfully mapped lesson fields for AI inference
[2025-06-25 13:37:08,148] DEBUG - __main__ - main.py:657 - Cached result for fetch_lesson_data
[2025-06-25 13:37:08,149] INFO - __main__ - main.py:12454 - [e605850a-7164-4e2e-9d68-8bab1a032f97] ✅ USING existing 2 learning objectives
[2025-06-25 13:37:08,150] INFO - __main__ - main.py:12455 - [e605850a-7164-4e2e-9d68-8bab1a032f97] 🎯 EXISTING OBJECTIVES: ['Identify the three states of matter.', 'Understand how matter changes state.']
[2025-06-25 13:37:09,482] INFO - __main__ - main.py:12461 - [e605850a-7164-4e2e-9d68-8bab1a032f97] 💾 SAVED learning objectives to Firestore
[2025-06-25 13:37:09,482] INFO - __main__ - main.py:10877 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Attempting to initialize lesson session for student_id: dev_student_uid_123
[2025-06-25 13:37:09,483] DEBUG - __main__ - main.py:10878 - [e605850a-7164-4e2e-9d68-8bab1a032f97] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'digitalMaterials', 'metadata', 'last_accessed', 'existingAssessments', 'session_started_at', 'last_updated', 'gradeLevel', 'access_count', 'quizzesAndAssessments', 'session_id', 'id', 'extensionActivities', 'country', 'theme', 'curriculumType', 'curriculum', 'level']
[2025-06-25 13:37:09,483] INFO - __main__ - main.py:10917 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Using student name: 'Development Student' for session session_03e96fe4-29fb-4639-980b-508ad9549ecb
[2025-06-25 13:37:09,485] INFO - __main__ - main.py:1592 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Constructing initial lesson state data for Firestore session: session_03e96fe4-29fb-4639-980b-508ad9549ecb
[2025-06-25 13:37:09,487] INFO - __main__ - main.py:1642 - [e605850a-7164-4e2e-9d68-8bab1a032f97] ✅ Constructed initial state dictionary for session session_03e96fe4-29fb-4639-980b-508ad9549ecb. Phase: diagnostic_start_probe
[2025-06-25 13:37:09,487] INFO - __main__ - main.py:1643 - [e605850a-7164-4e2e-9d68-8bab1a032f97] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-25 13:37:09,489] INFO - __main__ - main.py:10970 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Preparing to create 'lesson_sessions' doc 'session_03e96fe4-29fb-4639-980b-508ad9549ecb'.
[2025-06-25 13:37:09,489] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 13:37:09,490] DEBUG - __main__ - main.py:10971 - [e605850a-7164-4e2e-9d68-8bab1a032f97] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_03e96fe4-29fb-4639-980b-508ad9549ecb', 'student_id': 'dev_student_uid_123', 'lessonRef': 'P5-BST-002', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Basic Science and Technology', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Development Student', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'Exploring States of Matter', 'topic': 'States of Matter', 'learningObjectives': ['Identify the three states of matter.', 'Understand how matter changes state.'], 'key_concepts': ['Identify', 'three', 'states', 'matter', 'Understand', 'changes', 'state', 'Warm', 'Up Activity', 'Pose'], 'metadata': {'blooms_level': ['Remembering', 'Understanding'], 'context': 'Introduction', 'apiConnections': ["Gemini: Dynamically determines Bloom's Taxonomy level based on lesson content"], 'skills': ['Observing', 'Experimenting'], 'difficulty': 'easy', 'taxonomy_alignment': "This lesson primarily focuses on the Remembering and Understanding levels of Bloom's Taxonomy. Students will recall basic facts about states of matter and understand how matter changes state through demonstrations and hands-on activities."}}
[2025-06-25 13:37:09,490] INFO - __main__ - main.py:10973 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Preparing to create 'lesson_states' doc 'session_03e96fe4-29fb-4639-980b-508ad9549ecb'.
[2025-06-25 13:37:09,491] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 13:37:09,491] DEBUG - __main__ - main.py:10974 - [e605850a-7164-4e2e-9d68-8bab1a032f97] 'lesson_states' data: {'session_id': 'session_03e96fe4-29fb-4639-980b-508ad9549ecb', 'student_id': 'dev_student_uid_123', 'student_name': 'Development Student', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-BST-002', 'subject': 'Basic Science and Technology', 'grade': 'Primary 5', 'topic': 'States of Matter', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Identify, three, states, matter, Understand, changes, state, Warm, Up Activity, Pose', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-25 13:37:10,008] INFO - __main__ - main.py:10984 - [e605850a-7164-4e2e-9d68-8bab1a032f97] Successfully created Firestore docs for session 'session_03e96fe4-29fb-4639-980b-508ad9549ecb', student 'dev_student_uid_123'
[2025-06-25 13:37:10,025] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:37:10] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-25 13:37:10,850] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:37:10,851] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "8af34026-5089-46a1-ba5f-95536b9c7666", "timestamp": "2025-06-25T12:37:10.851186+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_03e96fe4-29fb-4639-980b-508ad9549ecb", "chat_history": []}}
[2025-06-25 13:37:10,852] INFO - auth_decorator - auth_decorator.py:39 - [8af34026-5089-46a1-ba5f-95536b9c7666][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:37:10,853] INFO - auth_decorator - auth_decorator.py:56 - [8af34026-5089-46a1-ba5f-95536b9c7666][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:37:10,863] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:37:10,870] WARNING - __main__ - main.py:5262 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:37:10,871] INFO - __main__ - main.py:5315 - [8af34026-5089-46a1-ba5f-95536b9c7666] Parsed Params: student_id='andrea_ugono_33305', session_id='session_03e96fe4-29fb-4639-980b-508ad9549ecb', lesson_ref='P5-BST-002'
[2025-06-25 13:37:11,191] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:37:11,191] INFO - __main__ - main.py:5361 - [8af34026-5089-46a1-ba5f-95536b9c7666] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:37:11,192] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:37:11,192] INFO - __main__ - main.py:5380 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:37:11,193] INFO - __main__ - main.py:5419 - [8af34026-5089-46a1-ba5f-95536b9c7666] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:37:11,194] INFO - __main__ - main.py:2332 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:37:11,200] INFO - __main__ - main.py:3739 - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
[2025-06-25 13:37:11,727] INFO - __main__ - main.py:2391 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:37:11,727] INFO - __main__ - main.py:2391 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:37:11,728] INFO - __main__ - main.py:2391 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:37:11,728] INFO - __main__ - main.py:2391 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:37:11,728] INFO - __main__ - main.py:2391 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:37:11,730] INFO - __main__ - main.py:2460 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:37:11,730] DEBUG - __main__ - main.py:2474 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:37:11,731] DEBUG - __main__ - main.py:2477 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:37:11,732] DEBUG - __main__ - main.py:2478 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:37:11,733] DEBUG - __main__ - main.py:2479 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:37:11,734] INFO - __main__ - main.py:2483 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:37:12,695] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:37:12,696] INFO - __main__ - main.py:2493 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Gemini API call completed in 0.96s. Raw response: 'materials'
[2025-06-25 13:37:12,697] DEBUG - __main__ - main.py:2515 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:37:12,698] INFO - __main__ - main.py:2520 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:37:12,699] INFO - __main__ - main.py:5452 - [8af34026-5089-46a1-ba5f-95536b9c7666] Successfully inferred module ID via AI: materials
[2025-06-25 13:37:12,701] INFO - __main__ - main.py:2569 - [8af34026-5089-46a1-ba5f-95536b9c7666] CACHE MISS or fetch: Getting GS levels for subject 'science', module 'materials'.
[2025-06-25 13:37:13,013] INFO - __main__ - main.py:2592 - [8af34026-5089-46a1-ba5f-95536b9c7666] Fetched metadata for module: 'Materials'
[2025-06-25 13:37:13,556] INFO - __main__ - main.py:2624 - [8af34026-5089-46a1-ba5f-95536b9c7666] Successfully fetched 10 levels for module 'materials'.
[2025-06-25 13:37:13,558] INFO - __main__ - main.py:5478 - [8af34026-5089-46a1-ba5f-95536b9c7666] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:37:13,853] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:37:14,379] WARNING - __main__ - main.py:5502 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:37:14,380] WARNING - __main__ - main.py:5503 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   - Session exists: True
[2025-06-25 13:37:14,381] WARNING - __main__ - main.py:5504 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:37:14,382] WARNING - __main__ - main.py:5505 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'current_phase', 'diagnostic_completed_this_session', 'student_name', 'lesson_context_snapshot', 'current_probing_level_number', 'levels_probed_and_failed', 'assigned_level_for_teaching', 'current_lesson_phase', 'current_question_index', 'student_id', 'last_modified', 'student_answers_for_probing_level']
[2025-06-25 13:37:14,384] WARNING - __main__ - main.py:5525 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:37:14,385] INFO - __main__ - main.py:5561 - [8af34026-5089-46a1-ba5f-95536b9c7666] State protection not triggered
[2025-06-25 13:37:14,385] INFO - __main__ - main.py:5596 - [8af34026-5089-46a1-ba5f-95536b9c7666] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:37:14,385] INFO - __main__ - main.py:5597 - [8af34026-5089-46a1-ba5f-95536b9c7666]   assigned_level_for_teaching (session): None
[2025-06-25 13:37:14,386] INFO - __main__ - main.py:5598 - [8af34026-5089-46a1-ba5f-95536b9c7666]   latest_assessed_level (profile): None
[2025-06-25 13:37:14,386] INFO - __main__ - main.py:5599 - [8af34026-5089-46a1-ba5f-95536b9c7666]   teaching_level_for_returning_student: None
[2025-06-25 13:37:14,386] INFO - __main__ - main.py:5600 - [8af34026-5089-46a1-ba5f-95536b9c7666]   has_completed_diagnostic_before: False
[2025-06-25 13:37:14,387] INFO - __main__ - main.py:5601 - [8af34026-5089-46a1-ba5f-95536b9c7666]   is_first_encounter_for_module: True
[2025-06-25 13:37:14,387] WARNING - __main__ - main.py:5606 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:37:14,388] INFO - __main__ - main.py:5612 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:37:14,388] INFO - __main__ - main.py:5613 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:37:14,388] INFO - __main__ - main.py:5614 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:37:14,388] INFO - __main__ - main.py:5615 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Is first encounter: True
[2025-06-25 13:37:14,389] INFO - __main__ - main.py:5616 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Diagnostic completed: False
[2025-06-25 13:37:14,389] INFO - __main__ - main.py:5622 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:37:14,390] INFO - __main__ - main.py:5636 - [8af34026-5089-46a1-ba5f-95536b9c7666] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:37:14,390] INFO - __main__ - main.py:5638 - [8af34026-5089-46a1-ba5f-95536b9c7666] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:37:14,390] INFO - __main__ - main.py:5658 - [8af34026-5089-46a1-ba5f-95536b9c7666] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-25 13:37:14,391] INFO - __main__ - main.py:3813 - [8af34026-5089-46a1-ba5f-95536b9c7666] Diagnostic context validation passed
[2025-06-25 13:37:14,391] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:37:14,391] WARNING - __main__ - main.py:5746 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:37:14,392] INFO - __main__ - main.py:3923 - [8af34026-5089-46a1-ba5f-95536b9c7666] Enhanced diagnostic context with 45 fields
[2025-06-25 13:37:14,393] INFO - __main__ - main.py:5765 - [8af34026-5089-46a1-ba5f-95536b9c7666] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:37:14,393] INFO - __main__ - main.py:5778 - [8af34026-5089-46a1-ba5f-95536b9c7666] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:37:14,394] DEBUG - __main__ - main.py:5779 - [8af34026-5089-46a1-ba5f-95536b9c7666] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:37:14,394] WARNING - __main__ - main.py:5980 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:37:14,395] WARNING - __main__ - main.py:5981 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:37:14,396] WARNING - __main__ - main.py:5982 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖   - Student query: Start diagnostic assessment...
[2025-06-25 13:37:14,398] INFO - __main__ - main.py:10403 - [8af34026-5089-46a1-ba5f-95536b9c7666] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:37:14,398] INFO - __main__ - main.py:10404 - [8af34026-5089-46a1-ba5f-95536b9c7666] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:37:14,398] INFO - __main__ - main.py:10405 - [8af34026-5089-46a1-ba5f-95536b9c7666] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:37:14,399] INFO - __main__ - main.py:10406 - [8af34026-5089-46a1-ba5f-95536b9c7666] 👤   final student_name: Andrea
[2025-06-25 13:37:14,399] INFO - __main__ - main.py:10486 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:37:14,951] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 200 characters
[2025-06-25 13:37:14,951] INFO - __main__ - main.py:10507 - [8af34026-5089-46a1-ba5f-95536b9c7666] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:37:14,951] INFO - __main__ - main.py:10538 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ Generated natural response for Andrea: 200 chars
[2025-06-25 13:37:14,952] INFO - __main__ - main.py:10539 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:14,952] WARNING - __main__ - main.py:6004 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:37:14,953] WARNING - __main__ - main.py:6005 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖   - Content length: 200 chars
[2025-06-25 13:37:14,953] WARNING - __main__ - main.py:6006 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:14,954] WARNING - __main__ - main.py:6007 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🤖   - Raw state block: None...
[2025-06-25 13:37:14,955] INFO - __main__ - main.py:6032 - [8af34026-5089-46a1-ba5f-95536b9c7666] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:37:14,956] INFO - __main__ - main.py:6033 - [8af34026-5089-46a1-ba5f-95536b9c7666] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:37:15,391] INFO - __main__ - main.py:6082 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI processing completed in 1.00s
[2025-06-25 13:37:15,392] WARNING - __main__ - main.py:6093 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:37:15,392] INFO - __main__ - main.py:4018 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:37:15,392] WARNING - __main__ - main.py:6102 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:37:15,393] WARNING - __main__ - main.py:6109 - [8af34026-5089-46a1-ba5f-95536b9c7666] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:37:15,393] WARNING - __main__ - main.py:6116 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:37:15,394] WARNING - __main__ - main.py:6117 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:37:15,395] WARNING - __main__ - main.py:6118 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:37:15,396] WARNING - __main__ - main.py:6119 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:15,396] WARNING - __main__ - main.py:6120 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:37:15,397] WARNING - __main__ - main.py:6123 - [8af34026-5089-46a1-ba5f-95536b9c7666] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:37:15,397] WARNING - __main__ - main.py:6124 - [8af34026-5089-46a1-ba5f-95536b9c7666] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:37:15,398] WARNING - __main__ - main.py:6125 - [8af34026-5089-46a1-ba5f-95536b9c7666] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:15,398] WARNING - __main__ - main.py:6126 - [8af34026-5089-46a1-ba5f-95536b9c7666] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:37:15,399] WARNING - __main__ - main.py:6127 - [8af34026-5089-46a1-ba5f-95536b9c7666] 💾   - Phase change: False
[2025-06-25 13:37:15,399] INFO - __main__ - main.py:4050 - [8af34026-5089-46a1-ba5f-95536b9c7666] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:37:15,400] INFO - __main__ - main.py:4051 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:37:15,400] INFO - __main__ - main.py:4052 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Current level: 5
[2025-06-25 13:37:15,401] INFO - __main__ - main.py:4053 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Question index: 0
[2025-06-25 13:37:15,401] INFO - __main__ - main.py:4054 - [8af34026-5089-46a1-ba5f-95536b9c7666]   First encounter: True
[2025-06-25 13:37:15,401] INFO - __main__ - main.py:4059 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Answers collected: 0
[2025-06-25 13:37:15,401] INFO - __main__ - main.py:4060 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Levels failed: 0
[2025-06-25 13:37:15,402] INFO - __main__ - main.py:4018 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:37:15,402] INFO - __main__ - main.py:4064 - [8af34026-5089-46a1-ba5f-95536b9c7666]   State update valid: True
[2025-06-25 13:37:15,402] INFO - __main__ - main.py:4071 - [8af34026-5089-46a1-ba5f-95536b9c7666]   Diagnostic complete: False
[2025-06-25 13:37:15,402] WARNING - __main__ - main.py:6140 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:37:15,403] INFO - __main__ - main.py:6149 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:37:15,403] INFO - __main__ - main.py:6150 - [8af34026-5089-46a1-ba5f-95536b9c7666] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:37:15,930] WARNING - __main__ - main.py:6195 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:37:15,931] WARNING - __main__ - main.py:6196 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:37:15,931] WARNING - __main__ - main.py:6197 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Probing Level: 5
[2025-06-25 13:37:15,931] WARNING - __main__ - main.py:6198 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Question Index: 1
[2025-06-25 13:37:15,932] WARNING - __main__ - main.py:6199 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Diagnostic Complete: False
[2025-06-25 13:37:15,933] WARNING - __main__ - main.py:6206 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:37:15,934] WARNING - __main__ - main.py:6207 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:37:15,935] WARNING - __main__ - main.py:6208 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Quiz Started: False
[2025-06-25 13:37:16,769] INFO - __main__ - main.py:6268 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ Updated existing session document: session_03e96fe4-29fb-4639-980b-508ad9549ecb
[2025-06-25 13:37:16,770] WARNING - __main__ - main.py:6269 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:37:16,770] WARNING - __main__ - main.py:6270 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Session ID: session_03e96fe4-29fb-4639-980b-508ad9549ecb
[2025-06-25 13:37:16,771] WARNING - __main__ - main.py:6271 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:37:16,772] WARNING - __main__ - main.py:6272 - [8af34026-5089-46a1-ba5f-95536b9c7666] ✅   - Interaction logged successfully
[2025-06-25 13:37:16,773] INFO - __main__ - main.py:11315 - [8af34026-5089-46a1-ba5f-95536b9c7666] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:37:16,774] DEBUG - __main__ - main.py:2812 - [8af34026-5089-46a1-ba5f-95536b9c7666] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:37:16,775] DEBUG - __main__ - main.py:6318 - [8af34026-5089-46a1-ba5f-95536b9c7666] No final assessment data found in AI response
[2025-06-25 13:37:16,777] INFO - __main__ - main.py:6404 - [8af34026-5089-46a1-ba5f-95536b9c7666] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:37:16,779] WARNING - __main__ - main.py:680 - High response time detected: 5.93s for enhance_content_api
[2025-06-25 13:37:16,780] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:37:16] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:37:38,870] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:37:38,871] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "47f83057-05c6-42c4-a732-3e46b384ac4f", "timestamp": "2025-06-25T12:37:38.871531+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "Yes I'm ready", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_03e96fe4-29fb-4639-980b-508ad9549ecb", "chat_history": [{"role": "assistant", "content": "Hello Andrea! It's great to see you! I'm ready to start our diagnostic assessment whenever you are. We're going to explore the amazing world of the states of matter together. Are you ready to dive in?", "timestamp": "2025-06-25T12:37:16.796Z"}]}}
[2025-06-25 13:37:38,872] INFO - auth_decorator - auth_decorator.py:39 - [47f83057-05c6-42c4-a732-3e46b384ac4f][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:37:38,873] INFO - auth_decorator - auth_decorator.py:56 - [47f83057-05c6-42c4-a732-3e46b384ac4f][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:37:38,874] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:37:38,876] WARNING - __main__ - main.py:5262 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:37:38,878] INFO - __main__ - main.py:5315 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Parsed Params: student_id='andrea_ugono_33305', session_id='session_03e96fe4-29fb-4639-980b-508ad9549ecb', lesson_ref='P5-BST-002'
[2025-06-25 13:37:40,129] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:37:40,130] INFO - __main__ - main.py:5361 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:37:40,131] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:37:40,131] INFO - __main__ - main.py:5380 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:37:40,132] INFO - __main__ - main.py:5419 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:37:40,132] INFO - __main__ - main.py:2332 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:37:40,505] INFO - __main__ - main.py:2391 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:37:40,506] INFO - __main__ - main.py:2391 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:37:40,506] INFO - __main__ - main.py:2391 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:37:40,507] INFO - __main__ - main.py:2391 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:37:40,507] INFO - __main__ - main.py:2391 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:37:40,507] INFO - __main__ - main.py:2460 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:37:40,508] DEBUG - __main__ - main.py:2474 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:37:40,508] DEBUG - __main__ - main.py:2477 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:37:40,509] DEBUG - __main__ - main.py:2478 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:37:40,509] DEBUG - __main__ - main.py:2479 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:37:40,510] INFO - __main__ - main.py:2483 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:37:40,902] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:37:40,902] INFO - __main__ - main.py:2493 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Gemini API call completed in 0.39s. Raw response: 'materials'
[2025-06-25 13:37:40,903] DEBUG - __main__ - main.py:2515 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:37:40,903] INFO - __main__ - main.py:2520 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:37:40,904] INFO - __main__ - main.py:5452 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Successfully inferred module ID via AI: materials
[2025-06-25 13:37:40,904] INFO - __main__ - main.py:5478 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:37:41,217] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:37:41,734] WARNING - __main__ - main.py:5502 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:37:41,734] WARNING - __main__ - main.py:5503 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   - Session exists: True
[2025-06-25 13:37:41,735] WARNING - __main__ - main.py:5504 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:37:41,735] WARNING - __main__ - main.py:5505 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 13:37:41,736] WARNING - __main__ - main.py:5525 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:37:41,736] INFO - __main__ - main.py:5561 - [47f83057-05c6-42c4-a732-3e46b384ac4f] State protection not triggered
[2025-06-25 13:37:41,737] INFO - __main__ - main.py:5596 - [47f83057-05c6-42c4-a732-3e46b384ac4f] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:37:41,737] INFO - __main__ - main.py:5597 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   assigned_level_for_teaching (session): None
[2025-06-25 13:37:41,737] INFO - __main__ - main.py:5598 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   latest_assessed_level (profile): None
[2025-06-25 13:37:41,738] INFO - __main__ - main.py:5599 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   teaching_level_for_returning_student: None
[2025-06-25 13:37:41,738] INFO - __main__ - main.py:5600 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   has_completed_diagnostic_before: False
[2025-06-25 13:37:41,738] INFO - __main__ - main.py:5601 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   is_first_encounter_for_module: True
[2025-06-25 13:37:41,739] WARNING - __main__ - main.py:5606 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:37:41,739] INFO - __main__ - main.py:5612 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:37:41,739] INFO - __main__ - main.py:5613 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:37:41,740] INFO - __main__ - main.py:5614 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:37:41,740] INFO - __main__ - main.py:5615 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Is first encounter: True
[2025-06-25 13:37:41,740] INFO - __main__ - main.py:5616 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Diagnostic completed: False
[2025-06-25 13:37:41,740] INFO - __main__ - main.py:5622 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:37:41,741] INFO - __main__ - main.py:5636 - [47f83057-05c6-42c4-a732-3e46b384ac4f] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:37:41,741] INFO - __main__ - main.py:5638 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:37:41,742] INFO - __main__ - main.py:5658 - [47f83057-05c6-42c4-a732-3e46b384ac4f] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 13:37:41,742] INFO - __main__ - main.py:3813 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Diagnostic context validation passed
[2025-06-25 13:37:41,742] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:37:41,742] WARNING - __main__ - main.py:5746 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:37:41,743] INFO - __main__ - main.py:3923 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Enhanced diagnostic context with 45 fields
[2025-06-25 13:37:41,743] INFO - __main__ - main.py:5765 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:37:41,744] INFO - __main__ - main.py:5778 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:37:41,744] DEBUG - __main__ - main.py:5779 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:37:41,745] WARNING - __main__ - main.py:5980 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:37:41,745] WARNING - __main__ - main.py:5981 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:37:41,745] WARNING - __main__ - main.py:5982 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖   - Student query: Yes I'm ready...
[2025-06-25 13:37:41,746] INFO - __main__ - main.py:10403 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:37:41,747] INFO - __main__ - main.py:10404 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:37:41,747] INFO - __main__ - main.py:10405 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:37:41,747] INFO - __main__ - main.py:10406 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 👤   final student_name: Andrea
[2025-06-25 13:37:41,748] INFO - __main__ - main.py:10486 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:37:42,354] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 325 characters
[2025-06-25 13:37:42,355] INFO - __main__ - main.py:10507 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:37:42,356] INFO - __main__ - main.py:10538 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ Generated natural response for Andrea: 325 chars
[2025-06-25 13:37:42,356] INFO - __main__ - main.py:10539 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:42,357] WARNING - __main__ - main.py:6004 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:37:42,358] WARNING - __main__ - main.py:6005 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖   - Content length: 325 chars
[2025-06-25 13:37:42,359] WARNING - __main__ - main.py:6006 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:42,360] WARNING - __main__ - main.py:6007 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🤖   - Raw state block: None...
[2025-06-25 13:37:42,361] INFO - __main__ - main.py:6032 - [47f83057-05c6-42c4-a732-3e46b384ac4f] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:37:42,362] INFO - __main__ - main.py:6033 - [47f83057-05c6-42c4-a732-3e46b384ac4f] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:37:42,672] INFO - __main__ - main.py:6082 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI processing completed in 0.93s
[2025-06-25 13:37:42,672] WARNING - __main__ - main.py:6093 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:37:42,673] INFO - __main__ - main.py:4018 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:37:42,673] WARNING - __main__ - main.py:6102 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:37:42,673] WARNING - __main__ - main.py:6109 - [47f83057-05c6-42c4-a732-3e46b384ac4f] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:37:42,674] WARNING - __main__ - main.py:6116 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:37:42,674] WARNING - __main__ - main.py:6117 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:37:42,674] WARNING - __main__ - main.py:6118 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:37:42,675] WARNING - __main__ - main.py:6119 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:42,675] WARNING - __main__ - main.py:6120 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:37:42,676] WARNING - __main__ - main.py:6123 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:37:42,676] WARNING - __main__ - main.py:6124 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:37:42,677] WARNING - __main__ - main.py:6125 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:37:42,677] WARNING - __main__ - main.py:6126 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:37:42,678] WARNING - __main__ - main.py:6127 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 💾   - Phase change: False
[2025-06-25 13:37:42,678] INFO - __main__ - main.py:4050 - [47f83057-05c6-42c4-a732-3e46b384ac4f] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:37:42,679] INFO - __main__ - main.py:4051 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:37:42,679] INFO - __main__ - main.py:4052 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Current level: 5
[2025-06-25 13:37:42,679] INFO - __main__ - main.py:4053 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Question index: 0
[2025-06-25 13:37:42,679] INFO - __main__ - main.py:4054 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   First encounter: True
[2025-06-25 13:37:42,680] INFO - __main__ - main.py:4059 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Answers collected: 0
[2025-06-25 13:37:42,680] INFO - __main__ - main.py:4060 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Levels failed: 0
[2025-06-25 13:37:42,680] INFO - __main__ - main.py:4018 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:37:42,681] INFO - __main__ - main.py:4064 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   State update valid: True
[2025-06-25 13:37:42,681] INFO - __main__ - main.py:4071 - [47f83057-05c6-42c4-a732-3e46b384ac4f]   Diagnostic complete: False
[2025-06-25 13:37:42,682] WARNING - __main__ - main.py:6140 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:37:42,682] INFO - __main__ - main.py:6149 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:37:42,683] INFO - __main__ - main.py:6150 - [47f83057-05c6-42c4-a732-3e46b384ac4f] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:37:43,191] WARNING - __main__ - main.py:6195 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:37:43,192] WARNING - __main__ - main.py:6196 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:37:43,192] WARNING - __main__ - main.py:6197 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Probing Level: 5
[2025-06-25 13:37:43,192] WARNING - __main__ - main.py:6198 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Question Index: 1
[2025-06-25 13:37:43,193] WARNING - __main__ - main.py:6199 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Diagnostic Complete: False
[2025-06-25 13:37:43,193] WARNING - __main__ - main.py:6206 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:37:43,194] WARNING - __main__ - main.py:6207 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:37:43,194] WARNING - __main__ - main.py:6208 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Quiz Started: False
[2025-06-25 13:37:44,013] INFO - __main__ - main.py:6268 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ Updated existing session document: session_03e96fe4-29fb-4639-980b-508ad9549ecb
[2025-06-25 13:37:44,014] WARNING - __main__ - main.py:6269 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:37:44,014] WARNING - __main__ - main.py:6270 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Session ID: session_03e96fe4-29fb-4639-980b-508ad9549ecb
[2025-06-25 13:37:44,015] WARNING - __main__ - main.py:6271 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:37:44,015] WARNING - __main__ - main.py:6272 - [47f83057-05c6-42c4-a732-3e46b384ac4f] ✅   - Interaction logged successfully
[2025-06-25 13:37:44,016] INFO - __main__ - main.py:11315 - [47f83057-05c6-42c4-a732-3e46b384ac4f] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:37:44,016] DEBUG - __main__ - main.py:2812 - [47f83057-05c6-42c4-a732-3e46b384ac4f] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:37:44,017] DEBUG - __main__ - main.py:6318 - [47f83057-05c6-42c4-a732-3e46b384ac4f] No final assessment data found in AI response
[2025-06-25 13:37:44,020] INFO - __main__ - main.py:6404 - [47f83057-05c6-42c4-a732-3e46b384ac4f] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:37:44,021] WARNING - __main__ - main.py:680 - High response time detected: 5.15s for enhance_content_api
[2025-06-25 13:37:44,022] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:37:44] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:42:47,144] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-25 13:42:47,163] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "b2b39e76-7f36-4fe3-b19c-dc35e092b2df", "timestamp": "2025-06-25T12:42:47.151734+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "subject": "Basic Science and Technology", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-25 13:42:47,167] INFO - __main__ - main.py:12327 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3NzQ4NTAwMmYwNWJlMDI2N2VmNDU5ZjViNTEzNTMzYjVjNThjMTIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hQfVfNpP6kXVKdGvX3lTU-GaCrcVX3_6c7Z0uQ1CMYZQsUE-LN_l0FlolTLvghdFyIQSfCNRnZr4hJiGGAHJ4kCK42L9qPWVGCsBslFeICgZddON4euq-BQ1guzjIH8YS6TV8Zj-fdmYrH06zG1WH86_aP4yNWZMZTch9ZSvsjDn35WM3hbWNguIS_g38eQe2LrUXFVthjLrgicfq4oQyLPZYLLM9w8qI61osCCRcfZlNPQyAHDSoskJXqwUtV34dvXC9slGlYY9ZVtXKuw7JmhfFYCWlXI-By4dD38cXh0Y4jKmU7aUvTqfpnwTYPX-eAjTZ6r337sL8xZPwbWn5w', 'User-Agent': 'axios/1.9.0', 'Content-Length': '231', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-25 13:42:47,169] INFO - auth_decorator - auth_decorator.py:39 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df][require_auth] Decorator invoked for path: /lesson-content
[2025-06-25 13:42:47,171] INFO - auth_decorator - auth_decorator.py:56 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:42:47,172] WARNING - __main__ - main.py:12334 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-25 13:42:47,173] INFO - __main__ - main.py:12341 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] lesson_content_and_quiz invoked by /lesson-content
[2025-06-25 13:42:47,173] INFO - __main__ - main.py:12357 - Lesson content request by authenticated user: dev_student_uid_123 (Development Student)
[2025-06-25 13:42:47,175] INFO - __main__ - main.py:12361 - !!! [lesson_content_and_quiz] [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] RAW BODY AT START (bytes length: 231): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-BST-002","subject":"Basic Science and Technology","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-25 13:42:47,176] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:42:47,177] INFO - __main__ - main.py:12454 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] ✅ USING existing 2 learning objectives
[2025-06-25 13:42:47,178] INFO - __main__ - main.py:12455 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] 🎯 EXISTING OBJECTIVES: ['Identify the three states of matter.', 'Understand how matter changes state.']
[2025-06-25 13:42:48,387] INFO - __main__ - main.py:12461 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] 💾 SAVED learning objectives to Firestore
[2025-06-25 13:42:48,388] INFO - __main__ - main.py:10877 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] Attempting to initialize lesson session for student_id: dev_student_uid_123
[2025-06-25 13:42:48,390] DEBUG - __main__ - main.py:10878 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'digitalMaterials', 'metadata', 'last_accessed', 'existingAssessments', 'session_started_at', 'last_updated', 'gradeLevel', 'access_count', 'quizzesAndAssessments', 'session_id', 'id', 'extensionActivities', 'country', 'theme', 'curriculumType', 'curriculum', 'level']
[2025-06-25 13:42:48,391] INFO - __main__ - main.py:10917 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] Using student name: 'Development Student' for session session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:42:48,393] INFO - __main__ - main.py:1592 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] Constructing initial lesson state data for Firestore session: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:42:48,395] INFO - __main__ - main.py:1642 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] ✅ Constructed initial state dictionary for session session_ffd70b39-9953-417b-8fcd-0c9985c06878. Phase: diagnostic_start_probe
[2025-06-25 13:42:48,396] INFO - __main__ - main.py:1643 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-25 13:42:48,397] INFO - __main__ - main.py:10970 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] Preparing to create 'lesson_sessions' doc 'session_ffd70b39-9953-417b-8fcd-0c9985c06878'.
[2025-06-25 13:42:48,398] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 13:42:48,399] DEBUG - __main__ - main.py:10971 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_ffd70b39-9953-417b-8fcd-0c9985c06878', 'student_id': 'dev_student_uid_123', 'lessonRef': 'P5-BST-002', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Basic Science and Technology', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Development Student', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'Exploring States of Matter', 'topic': 'States of Matter', 'learningObjectives': ['Identify the three states of matter.', 'Understand how matter changes state.'], 'key_concepts': ['Identify', 'three', 'states', 'matter', 'Understand', 'changes', 'state', 'Warm', 'Up Activity', 'Pose'], 'metadata': {'blooms_level': ['Remembering', 'Understanding'], 'context': 'Introduction', 'apiConnections': ["Gemini: Dynamically determines Bloom's Taxonomy level based on lesson content"], 'skills': ['Observing', 'Experimenting'], 'difficulty': 'easy', 'taxonomy_alignment': "This lesson primarily focuses on the Remembering and Understanding levels of Bloom's Taxonomy. Students will recall basic facts about states of matter and understand how matter changes state through demonstrations and hands-on activities."}}
[2025-06-25 13:42:48,399] INFO - __main__ - main.py:10973 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] Preparing to create 'lesson_states' doc 'session_ffd70b39-9953-417b-8fcd-0c9985c06878'.
[2025-06-25 13:42:48,400] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 13:42:48,401] DEBUG - __main__ - main.py:10974 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] 'lesson_states' data: {'session_id': 'session_ffd70b39-9953-417b-8fcd-0c9985c06878', 'student_id': 'dev_student_uid_123', 'student_name': 'Development Student', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-BST-002', 'subject': 'Basic Science and Technology', 'grade': 'Primary 5', 'topic': 'States of Matter', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Identify, three, states, matter, Understand, changes, state, Warm, Up Activity, Pose', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-25 13:42:48,879] INFO - __main__ - main.py:10984 - [b2b39e76-7f36-4fe3-b19c-dc35e092b2df] Successfully created Firestore docs for session 'session_ffd70b39-9953-417b-8fcd-0c9985c06878', student 'dev_student_uid_123'
[2025-06-25 13:42:48,890] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:42:48] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-25 13:42:57,628] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:42:57,629] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "648b8103-1309-4fbc-9ccf-2c65879002ba", "timestamp": "2025-06-25T12:42:57.629233+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_ffd70b39-9953-417b-8fcd-0c9985c06878", "chat_history": []}}
[2025-06-25 13:42:57,633] INFO - auth_decorator - auth_decorator.py:39 - [648b8103-1309-4fbc-9ccf-2c65879002ba][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:42:57,635] INFO - auth_decorator - auth_decorator.py:56 - [648b8103-1309-4fbc-9ccf-2c65879002ba][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:42:57,651] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:42:57,658] WARNING - __main__ - main.py:5262 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:42:57,661] INFO - __main__ - main.py:5315 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ffd70b39-9953-417b-8fcd-0c9985c06878', lesson_ref='P5-BST-002'
[2025-06-25 13:42:58,238] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:42:58,241] INFO - __main__ - main.py:5361 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:42:58,261] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:42:58,262] INFO - __main__ - main.py:5380 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:42:58,265] INFO - __main__ - main.py:5419 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:42:58,266] INFO - __main__ - main.py:2332 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:42:58,581] INFO - __main__ - main.py:2391 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:42:58,582] INFO - __main__ - main.py:2391 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:42:58,583] INFO - __main__ - main.py:2391 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:42:58,583] INFO - __main__ - main.py:2391 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:42:58,584] INFO - __main__ - main.py:2391 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:42:58,586] INFO - __main__ - main.py:2460 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:42:58,588] DEBUG - __main__ - main.py:2474 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:42:58,589] DEBUG - __main__ - main.py:2477 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:42:58,590] DEBUG - __main__ - main.py:2478 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:42:58,592] DEBUG - __main__ - main.py:2479 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:42:58,592] INFO - __main__ - main.py:2483 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:42:59,516] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:42:59,518] INFO - __main__ - main.py:2493 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Gemini API call completed in 0.92s. Raw response: 'materials'
[2025-06-25 13:42:59,518] DEBUG - __main__ - main.py:2515 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:42:59,518] INFO - __main__ - main.py:2520 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:42:59,519] INFO - __main__ - main.py:5452 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Successfully inferred module ID via AI: materials
[2025-06-25 13:42:59,522] INFO - __main__ - main.py:5478 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:42:59,826] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:43:00,373] WARNING - __main__ - main.py:5502 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:43:00,374] WARNING - __main__ - main.py:5503 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   - Session exists: True
[2025-06-25 13:43:00,374] WARNING - __main__ - main.py:5504 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:43:00,374] WARNING - __main__ - main.py:5505 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'current_phase', 'diagnostic_completed_this_session', 'student_name', 'lesson_context_snapshot', 'current_probing_level_number', 'levels_probed_and_failed', 'assigned_level_for_teaching', 'current_lesson_phase', 'current_question_index', 'student_id', 'last_modified', 'student_answers_for_probing_level']
[2025-06-25 13:43:00,375] WARNING - __main__ - main.py:5525 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:43:00,376] INFO - __main__ - main.py:5561 - [648b8103-1309-4fbc-9ccf-2c65879002ba] State protection not triggered
[2025-06-25 13:43:00,377] INFO - __main__ - main.py:5596 - [648b8103-1309-4fbc-9ccf-2c65879002ba] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:43:00,378] INFO - __main__ - main.py:5597 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   assigned_level_for_teaching (session): None
[2025-06-25 13:43:00,378] INFO - __main__ - main.py:5598 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   latest_assessed_level (profile): None
[2025-06-25 13:43:00,379] INFO - __main__ - main.py:5599 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   teaching_level_for_returning_student: None
[2025-06-25 13:43:00,380] INFO - __main__ - main.py:5600 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   has_completed_diagnostic_before: False
[2025-06-25 13:43:00,380] INFO - __main__ - main.py:5601 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   is_first_encounter_for_module: True
[2025-06-25 13:43:00,381] WARNING - __main__ - main.py:5606 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:43:00,384] INFO - __main__ - main.py:5612 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:43:00,385] INFO - __main__ - main.py:5613 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:43:00,386] INFO - __main__ - main.py:5614 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:43:00,386] INFO - __main__ - main.py:5615 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Is first encounter: True
[2025-06-25 13:43:00,386] INFO - __main__ - main.py:5616 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Diagnostic completed: False
[2025-06-25 13:43:00,387] INFO - __main__ - main.py:5622 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:43:00,387] INFO - __main__ - main.py:5636 - [648b8103-1309-4fbc-9ccf-2c65879002ba] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:43:00,387] INFO - __main__ - main.py:5638 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:43:00,388] INFO - __main__ - main.py:5658 - [648b8103-1309-4fbc-9ccf-2c65879002ba] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-25 13:43:00,390] INFO - __main__ - main.py:3813 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Diagnostic context validation passed
[2025-06-25 13:43:00,390] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:43:00,392] WARNING - __main__ - main.py:5746 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:43:00,393] INFO - __main__ - main.py:3923 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Enhanced diagnostic context with 45 fields
[2025-06-25 13:43:00,394] INFO - __main__ - main.py:5765 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:43:00,394] INFO - __main__ - main.py:5778 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:43:00,395] DEBUG - __main__ - main.py:5779 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:43:00,396] WARNING - __main__ - main.py:5980 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:43:00,396] WARNING - __main__ - main.py:5981 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:43:00,397] WARNING - __main__ - main.py:5982 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖   - Student query: Start diagnostic assessment...
[2025-06-25 13:43:00,398] INFO - __main__ - main.py:10403 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:43:00,399] INFO - __main__ - main.py:10404 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:43:00,401] INFO - __main__ - main.py:10405 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:43:00,402] INFO - __main__ - main.py:10406 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 👤   final student_name: Andrea
[2025-06-25 13:43:00,402] INFO - __main__ - main.py:10486 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:43:00,954] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 338 characters
[2025-06-25 13:43:00,954] INFO - __main__ - main.py:10507 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:43:00,955] INFO - __main__ - main.py:10538 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ Generated natural response for Andrea: 338 chars
[2025-06-25 13:43:00,956] INFO - __main__ - main.py:10539 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:43:00,957] WARNING - __main__ - main.py:6004 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:43:00,957] WARNING - __main__ - main.py:6005 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖   - Content length: 338 chars
[2025-06-25 13:43:00,958] WARNING - __main__ - main.py:6006 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:43:00,959] WARNING - __main__ - main.py:6007 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🤖   - Raw state block: None...
[2025-06-25 13:43:00,960] INFO - __main__ - main.py:6032 - [648b8103-1309-4fbc-9ccf-2c65879002ba] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:43:00,962] INFO - __main__ - main.py:6033 - [648b8103-1309-4fbc-9ccf-2c65879002ba] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:43:01,277] INFO - __main__ - main.py:6082 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI processing completed in 0.88s
[2025-06-25 13:43:01,278] WARNING - __main__ - main.py:6093 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:43:01,279] INFO - __main__ - main.py:4018 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:43:01,279] WARNING - __main__ - main.py:6102 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:43:01,280] WARNING - __main__ - main.py:6109 - [648b8103-1309-4fbc-9ccf-2c65879002ba] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:43:01,281] WARNING - __main__ - main.py:6116 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:43:01,282] WARNING - __main__ - main.py:6117 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:43:01,283] WARNING - __main__ - main.py:6118 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:43:01,284] WARNING - __main__ - main.py:6119 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:43:01,284] WARNING - __main__ - main.py:6120 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:43:01,285] WARNING - __main__ - main.py:6123 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:43:01,286] WARNING - __main__ - main.py:6124 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:43:01,287] WARNING - __main__ - main.py:6125 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:43:01,288] WARNING - __main__ - main.py:6126 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:43:01,289] WARNING - __main__ - main.py:6127 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 💾   - Phase change: False
[2025-06-25 13:43:01,289] INFO - __main__ - main.py:4050 - [648b8103-1309-4fbc-9ccf-2c65879002ba] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:43:01,290] INFO - __main__ - main.py:4051 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:43:01,291] INFO - __main__ - main.py:4052 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Current level: 5
[2025-06-25 13:43:01,291] INFO - __main__ - main.py:4053 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Question index: 0
[2025-06-25 13:43:01,292] INFO - __main__ - main.py:4054 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   First encounter: True
[2025-06-25 13:43:01,292] INFO - __main__ - main.py:4059 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Answers collected: 0
[2025-06-25 13:43:01,293] INFO - __main__ - main.py:4060 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Levels failed: 0
[2025-06-25 13:43:01,293] INFO - __main__ - main.py:4018 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:43:01,294] INFO - __main__ - main.py:4064 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   State update valid: True
[2025-06-25 13:43:01,294] INFO - __main__ - main.py:4071 - [648b8103-1309-4fbc-9ccf-2c65879002ba]   Diagnostic complete: False
[2025-06-25 13:43:01,295] WARNING - __main__ - main.py:6140 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:43:01,296] INFO - __main__ - main.py:6149 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:43:01,297] INFO - __main__ - main.py:6150 - [648b8103-1309-4fbc-9ccf-2c65879002ba] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:43:01,827] WARNING - __main__ - main.py:6195 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:43:01,827] WARNING - __main__ - main.py:6196 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:43:01,828] WARNING - __main__ - main.py:6197 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Probing Level: 5
[2025-06-25 13:43:01,828] WARNING - __main__ - main.py:6198 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Question Index: 1
[2025-06-25 13:43:01,829] WARNING - __main__ - main.py:6199 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Diagnostic Complete: False
[2025-06-25 13:43:01,829] WARNING - __main__ - main.py:6206 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:43:01,829] WARNING - __main__ - main.py:6207 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:43:01,831] WARNING - __main__ - main.py:6208 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Quiz Started: False
[2025-06-25 13:43:02,674] INFO - __main__ - main.py:6268 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ Updated existing session document: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:43:02,675] WARNING - __main__ - main.py:6269 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:43:02,676] WARNING - __main__ - main.py:6270 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Session ID: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:43:02,677] WARNING - __main__ - main.py:6271 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:43:02,678] WARNING - __main__ - main.py:6272 - [648b8103-1309-4fbc-9ccf-2c65879002ba] ✅   - Interaction logged successfully
[2025-06-25 13:43:02,680] INFO - __main__ - main.py:11315 - [648b8103-1309-4fbc-9ccf-2c65879002ba] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:43:02,682] DEBUG - __main__ - main.py:2812 - [648b8103-1309-4fbc-9ccf-2c65879002ba] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:43:02,683] DEBUG - __main__ - main.py:6318 - [648b8103-1309-4fbc-9ccf-2c65879002ba] No final assessment data found in AI response
[2025-06-25 13:43:02,685] INFO - __main__ - main.py:6404 - [648b8103-1309-4fbc-9ccf-2c65879002ba] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:43:02,691] WARNING - __main__ - main.py:680 - High response time detected: 5.05s for enhance_content_api
[2025-06-25 13:43:02,692] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:43:02] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:44:09,415] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:44:09,424] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "f4a8e99b-984e-4ebd-a7bf-b46ed863aa78", "timestamp": "2025-06-25T12:44:09.417843+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "Matter is like all the stuff in the world. It's anything you can touch, see, or feel.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_ffd70b39-9953-417b-8fcd-0c9985c06878", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm so excited to start our States of Matter adventure today! You want to begin the diagnostic assessment? That's a fantastic idea, Andrea! It helps me see what you already know so we can learn even more together. Let's get started!\n\nTo kick things off, Andrea, can you tell me in your own words, what do you think \"matter\" is?", "timestamp": "2025-06-25T12:43:02.716Z"}]}}
[2025-06-25 13:44:09,426] INFO - auth_decorator - auth_decorator.py:39 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:44:09,427] INFO - auth_decorator - auth_decorator.py:56 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:44:09,429] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:44:09,431] WARNING - __main__ - main.py:5262 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:44:09,436] INFO - __main__ - main.py:5315 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ffd70b39-9953-417b-8fcd-0c9985c06878', lesson_ref='P5-BST-002'
[2025-06-25 13:44:10,886] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:44:10,886] INFO - __main__ - main.py:5361 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:44:10,887] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:44:10,887] INFO - __main__ - main.py:5380 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:44:10,888] INFO - __main__ - main.py:5419 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:44:10,888] INFO - __main__ - main.py:2332 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:44:11,924] INFO - __main__ - main.py:2391 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:44:11,925] INFO - __main__ - main.py:2391 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:44:11,926] INFO - __main__ - main.py:2391 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:44:11,926] INFO - __main__ - main.py:2391 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:44:11,926] INFO - __main__ - main.py:2391 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:44:11,927] INFO - __main__ - main.py:2460 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:44:11,927] DEBUG - __main__ - main.py:2474 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:44:11,928] DEBUG - __main__ - main.py:2477 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:44:11,929] DEBUG - __main__ - main.py:2478 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:44:11,930] DEBUG - __main__ - main.py:2479 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:44:11,931] INFO - __main__ - main.py:2483 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:44:12,339] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:44:12,340] INFO - __main__ - main.py:2493 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Gemini API call completed in 0.41s. Raw response: 'materials'
[2025-06-25 13:44:12,341] DEBUG - __main__ - main.py:2515 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:44:12,341] INFO - __main__ - main.py:2520 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:44:12,342] INFO - __main__ - main.py:5452 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Successfully inferred module ID via AI: materials
[2025-06-25 13:44:12,343] INFO - __main__ - main.py:5478 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:44:12,689] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:44:13,163] WARNING - __main__ - main.py:5502 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:44:13,163] WARNING - __main__ - main.py:5503 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   - Session exists: True
[2025-06-25 13:44:13,164] WARNING - __main__ - main.py:5504 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:44:13,167] WARNING - __main__ - main.py:5505 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 13:44:13,171] WARNING - __main__ - main.py:5525 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:44:13,172] INFO - __main__ - main.py:5561 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] State protection not triggered
[2025-06-25 13:44:13,173] INFO - __main__ - main.py:5596 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:44:13,173] INFO - __main__ - main.py:5597 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   assigned_level_for_teaching (session): None
[2025-06-25 13:44:13,174] INFO - __main__ - main.py:5598 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   latest_assessed_level (profile): None
[2025-06-25 13:44:13,175] INFO - __main__ - main.py:5599 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   teaching_level_for_returning_student: None
[2025-06-25 13:44:13,175] INFO - __main__ - main.py:5600 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   has_completed_diagnostic_before: False
[2025-06-25 13:44:13,176] INFO - __main__ - main.py:5601 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   is_first_encounter_for_module: True
[2025-06-25 13:44:13,176] WARNING - __main__ - main.py:5606 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:44:13,177] INFO - __main__ - main.py:5612 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:44:13,178] INFO - __main__ - main.py:5613 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:44:13,178] INFO - __main__ - main.py:5614 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:44:13,179] INFO - __main__ - main.py:5615 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Is first encounter: True
[2025-06-25 13:44:13,180] INFO - __main__ - main.py:5616 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Diagnostic completed: False
[2025-06-25 13:44:13,180] INFO - __main__ - main.py:5622 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:44:13,181] INFO - __main__ - main.py:5636 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:44:13,182] INFO - __main__ - main.py:5638 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:44:13,182] INFO - __main__ - main.py:5658 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 13:44:13,183] INFO - __main__ - main.py:3813 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Diagnostic context validation passed
[2025-06-25 13:44:13,184] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:44:13,184] WARNING - __main__ - main.py:5746 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:44:13,185] INFO - __main__ - main.py:3923 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Enhanced diagnostic context with 45 fields
[2025-06-25 13:44:13,186] INFO - __main__ - main.py:5765 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:44:13,188] INFO - __main__ - main.py:5778 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:44:13,188] DEBUG - __main__ - main.py:5779 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:44:13,189] WARNING - __main__ - main.py:5980 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:44:13,190] WARNING - __main__ - main.py:5981 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:44:13,191] WARNING - __main__ - main.py:5982 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖   - Student query: Matter is like all the stuff in the world. It's anything you can touch, see, or feel....
[2025-06-25 13:44:13,192] INFO - __main__ - main.py:10403 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:44:13,193] INFO - __main__ - main.py:10404 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:44:13,195] INFO - __main__ - main.py:10405 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:44:13,196] INFO - __main__ - main.py:10406 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 👤   final student_name: Andrea
[2025-06-25 13:44:13,198] INFO - __main__ - main.py:10486 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:44:14,246] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 391 characters
[2025-06-25 13:44:14,247] INFO - __main__ - main.py:10507 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:44:14,248] INFO - __main__ - main.py:10538 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ Generated natural response for Andrea: 391 chars
[2025-06-25 13:44:14,248] INFO - __main__ - main.py:10539 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:44:14,249] WARNING - __main__ - main.py:6004 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:44:14,251] WARNING - __main__ - main.py:6005 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖   - Content length: 391 chars
[2025-06-25 13:44:14,252] WARNING - __main__ - main.py:6006 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:44:14,253] WARNING - __main__ - main.py:6007 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🤖   - Raw state block: None...
[2025-06-25 13:44:14,254] INFO - __main__ - main.py:6032 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:44:14,255] INFO - __main__ - main.py:6033 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:44:14,561] INFO - __main__ - main.py:6082 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI processing completed in 1.37s
[2025-06-25 13:44:14,562] WARNING - __main__ - main.py:6093 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:44:14,562] INFO - __main__ - main.py:4018 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:44:14,563] WARNING - __main__ - main.py:6102 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:44:14,563] WARNING - __main__ - main.py:6109 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:44:14,564] WARNING - __main__ - main.py:6116 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:44:14,565] WARNING - __main__ - main.py:6117 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:44:14,567] WARNING - __main__ - main.py:6118 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:44:14,567] WARNING - __main__ - main.py:6119 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:44:14,569] WARNING - __main__ - main.py:6120 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:44:14,570] WARNING - __main__ - main.py:6123 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:44:14,570] WARNING - __main__ - main.py:6124 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:44:14,571] WARNING - __main__ - main.py:6125 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:44:14,572] WARNING - __main__ - main.py:6126 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:44:14,573] WARNING - __main__ - main.py:6127 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 💾   - Phase change: False
[2025-06-25 13:44:14,573] INFO - __main__ - main.py:4050 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:44:14,574] INFO - __main__ - main.py:4051 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:44:14,575] INFO - __main__ - main.py:4052 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Current level: 5
[2025-06-25 13:44:14,575] INFO - __main__ - main.py:4053 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Question index: 0
[2025-06-25 13:44:14,576] INFO - __main__ - main.py:4054 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   First encounter: True
[2025-06-25 13:44:14,576] INFO - __main__ - main.py:4059 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Answers collected: 0
[2025-06-25 13:44:14,577] INFO - __main__ - main.py:4060 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Levels failed: 0
[2025-06-25 13:44:14,578] INFO - __main__ - main.py:4018 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:44:14,578] INFO - __main__ - main.py:4064 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   State update valid: True
[2025-06-25 13:44:14,579] INFO - __main__ - main.py:4071 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78]   Diagnostic complete: False
[2025-06-25 13:44:14,580] WARNING - __main__ - main.py:6140 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:44:14,581] INFO - __main__ - main.py:6149 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:44:14,582] INFO - __main__ - main.py:6150 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:44:15,097] WARNING - __main__ - main.py:6195 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:44:15,098] WARNING - __main__ - main.py:6196 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:44:15,099] WARNING - __main__ - main.py:6197 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Probing Level: 5
[2025-06-25 13:44:15,099] WARNING - __main__ - main.py:6198 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Question Index: 1
[2025-06-25 13:44:15,101] WARNING - __main__ - main.py:6199 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Diagnostic Complete: False
[2025-06-25 13:44:15,101] WARNING - __main__ - main.py:6206 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:44:15,102] WARNING - __main__ - main.py:6207 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:44:15,102] WARNING - __main__ - main.py:6208 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Quiz Started: False
[2025-06-25 13:44:15,955] INFO - __main__ - main.py:6268 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ Updated existing session document: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:44:15,956] WARNING - __main__ - main.py:6269 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:44:15,956] WARNING - __main__ - main.py:6270 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Session ID: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:44:15,957] WARNING - __main__ - main.py:6271 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:44:15,957] WARNING - __main__ - main.py:6272 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] ✅   - Interaction logged successfully
[2025-06-25 13:44:15,958] INFO - __main__ - main.py:11315 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:44:15,958] DEBUG - __main__ - main.py:2812 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:44:15,959] DEBUG - __main__ - main.py:6318 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] No final assessment data found in AI response
[2025-06-25 13:44:15,962] INFO - __main__ - main.py:6404 - [f4a8e99b-984e-4ebd-a7bf-b46ed863aa78] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:44:15,963] WARNING - __main__ - main.py:680 - High response time detected: 6.53s for enhance_content_api
[2025-06-25 13:44:15,963] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:44:15] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:45:22,118] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:45:22,121] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "c41fae84-9c55-45f4-9baa-15746b17ada8", "timestamp": "2025-06-25T12:45:22.119343+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "no", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_ffd70b39-9953-417b-8fcd-0c9985c06878", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm so excited to start our States of Matter adventure today! You want to begin the diagnostic assessment? That's a fantastic idea, Andrea! It helps me see what you already know so we can learn even more together. Let's get started!\n\nTo kick things off, Andrea, can you tell me in your own words, what do you think \"matter\" is?", "timestamp": "2025-06-25T12:43:02.716Z"}, {"role": "user", "content": "Matter is like all the stuff in the world. It's anything you can touch, see, or feel.", "timestamp": "2025-06-25T12:44:09.233Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"All the stuff in the world\" is exactly right. And yes, anything you can touch, see, or feel is a great clue. You're already thinking like a scientist, Andrea!\n\nSo, if matter is all the stuff around us, what do you think happens when we look at different kinds of stuff, like water, ice, and the steam from a kettle? Do they all seem to be the same?", "timestamp": "2025-06-25T12:44:15.983Z"}]}}
[2025-06-25 13:45:22,125] INFO - auth_decorator - auth_decorator.py:39 - [c41fae84-9c55-45f4-9baa-15746b17ada8][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:45:22,126] INFO - auth_decorator - auth_decorator.py:56 - [c41fae84-9c55-45f4-9baa-15746b17ada8][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:45:22,127] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:45:22,130] WARNING - __main__ - main.py:5262 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:45:22,133] INFO - __main__ - main.py:5315 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ffd70b39-9953-417b-8fcd-0c9985c06878', lesson_ref='P5-BST-002'
[2025-06-25 13:45:23,543] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:45:23,544] INFO - __main__ - main.py:5361 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:45:23,545] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:45:23,545] INFO - __main__ - main.py:5380 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:45:23,545] INFO - __main__ - main.py:5419 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:45:23,546] INFO - __main__ - main.py:2332 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:45:24,016] INFO - __main__ - main.py:2391 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:45:24,018] INFO - __main__ - main.py:2391 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:45:24,019] INFO - __main__ - main.py:2391 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:45:24,019] INFO - __main__ - main.py:2391 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:45:24,020] INFO - __main__ - main.py:2391 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:45:24,021] INFO - __main__ - main.py:2460 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:45:24,021] DEBUG - __main__ - main.py:2474 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:45:24,022] DEBUG - __main__ - main.py:2477 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:45:24,023] DEBUG - __main__ - main.py:2478 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:45:24,023] DEBUG - __main__ - main.py:2479 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:45:24,024] INFO - __main__ - main.py:2483 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:45:24,541] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:45:24,542] INFO - __main__ - main.py:2493 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Gemini API call completed in 0.52s. Raw response: 'materials'
[2025-06-25 13:45:24,543] DEBUG - __main__ - main.py:2515 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:45:24,543] INFO - __main__ - main.py:2520 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:45:24,544] INFO - __main__ - main.py:5452 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Successfully inferred module ID via AI: materials
[2025-06-25 13:45:24,544] INFO - __main__ - main.py:5478 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:45:25,092] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:45:25,461] WARNING - __main__ - main.py:5502 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:45:25,462] WARNING - __main__ - main.py:5503 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   - Session exists: True
[2025-06-25 13:45:25,463] WARNING - __main__ - main.py:5504 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:45:25,463] WARNING - __main__ - main.py:5505 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 13:45:25,465] WARNING - __main__ - main.py:5525 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:45:25,465] INFO - __main__ - main.py:5561 - [c41fae84-9c55-45f4-9baa-15746b17ada8] State protection not triggered
[2025-06-25 13:45:25,466] INFO - __main__ - main.py:5596 - [c41fae84-9c55-45f4-9baa-15746b17ada8] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:45:25,467] INFO - __main__ - main.py:5597 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   assigned_level_for_teaching (session): None
[2025-06-25 13:45:25,468] INFO - __main__ - main.py:5598 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   latest_assessed_level (profile): None
[2025-06-25 13:45:25,469] INFO - __main__ - main.py:5599 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   teaching_level_for_returning_student: None
[2025-06-25 13:45:25,469] INFO - __main__ - main.py:5600 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   has_completed_diagnostic_before: False
[2025-06-25 13:45:25,470] INFO - __main__ - main.py:5601 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   is_first_encounter_for_module: True
[2025-06-25 13:45:25,471] WARNING - __main__ - main.py:5606 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:45:25,472] INFO - __main__ - main.py:5612 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:45:25,473] INFO - __main__ - main.py:5613 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:45:25,473] INFO - __main__ - main.py:5614 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:45:25,474] INFO - __main__ - main.py:5615 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Is first encounter: True
[2025-06-25 13:45:25,475] INFO - __main__ - main.py:5616 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Diagnostic completed: False
[2025-06-25 13:45:25,476] INFO - __main__ - main.py:5622 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:45:25,476] INFO - __main__ - main.py:5636 - [c41fae84-9c55-45f4-9baa-15746b17ada8] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:45:25,477] INFO - __main__ - main.py:5638 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:45:25,478] INFO - __main__ - main.py:5658 - [c41fae84-9c55-45f4-9baa-15746b17ada8] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 13:45:25,479] INFO - __main__ - main.py:3813 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Diagnostic context validation passed
[2025-06-25 13:45:25,479] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:45:25,480] WARNING - __main__ - main.py:5746 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:45:25,481] INFO - __main__ - main.py:3923 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Enhanced diagnostic context with 45 fields
[2025-06-25 13:45:25,482] INFO - __main__ - main.py:5765 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:45:25,483] INFO - __main__ - main.py:5778 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:45:25,484] DEBUG - __main__ - main.py:5779 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:45:25,485] WARNING - __main__ - main.py:5980 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:45:25,486] WARNING - __main__ - main.py:5981 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:45:25,487] WARNING - __main__ - main.py:5982 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖   - Student query: no...
[2025-06-25 13:45:25,488] INFO - __main__ - main.py:10403 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:45:25,489] INFO - __main__ - main.py:10404 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:45:25,496] INFO - __main__ - main.py:10405 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:45:25,496] INFO - __main__ - main.py:10406 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 👤   final student_name: Andrea
[2025-06-25 13:45:25,497] INFO - __main__ - main.py:10486 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:45:26,621] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 348 characters
[2025-06-25 13:45:26,622] INFO - __main__ - main.py:10507 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:45:26,623] INFO - __main__ - main.py:10538 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ Generated natural response for Andrea: 348 chars
[2025-06-25 13:45:26,623] INFO - __main__ - main.py:10539 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:45:26,624] WARNING - __main__ - main.py:6004 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:45:26,625] WARNING - __main__ - main.py:6005 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖   - Content length: 348 chars
[2025-06-25 13:45:26,626] WARNING - __main__ - main.py:6006 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:45:26,627] WARNING - __main__ - main.py:6007 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🤖   - Raw state block: None...
[2025-06-25 13:45:26,629] INFO - __main__ - main.py:6032 - [c41fae84-9c55-45f4-9baa-15746b17ada8] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:45:26,630] INFO - __main__ - main.py:6033 - [c41fae84-9c55-45f4-9baa-15746b17ada8] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:45:26,929] INFO - __main__ - main.py:6082 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI processing completed in 1.44s
[2025-06-25 13:45:26,930] WARNING - __main__ - main.py:6093 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:45:26,930] INFO - __main__ - main.py:4018 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:45:26,931] WARNING - __main__ - main.py:6102 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:45:26,931] WARNING - __main__ - main.py:6109 - [c41fae84-9c55-45f4-9baa-15746b17ada8] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:45:26,931] WARNING - __main__ - main.py:6116 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:45:26,932] WARNING - __main__ - main.py:6117 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:45:26,933] WARNING - __main__ - main.py:6118 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:45:26,935] WARNING - __main__ - main.py:6119 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:45:26,936] WARNING - __main__ - main.py:6120 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:45:26,937] WARNING - __main__ - main.py:6123 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:45:26,938] WARNING - __main__ - main.py:6124 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:45:26,939] WARNING - __main__ - main.py:6125 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:45:26,939] WARNING - __main__ - main.py:6126 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:45:26,940] WARNING - __main__ - main.py:6127 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 💾   - Phase change: False
[2025-06-25 13:45:26,940] INFO - __main__ - main.py:4050 - [c41fae84-9c55-45f4-9baa-15746b17ada8] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:45:26,941] INFO - __main__ - main.py:4051 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:45:26,941] INFO - __main__ - main.py:4052 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Current level: 5
[2025-06-25 13:45:26,942] INFO - __main__ - main.py:4053 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Question index: 0
[2025-06-25 13:45:26,943] INFO - __main__ - main.py:4054 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   First encounter: True
[2025-06-25 13:45:26,943] INFO - __main__ - main.py:4059 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Answers collected: 0
[2025-06-25 13:45:26,944] INFO - __main__ - main.py:4060 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Levels failed: 0
[2025-06-25 13:45:26,944] INFO - __main__ - main.py:4018 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:45:26,945] INFO - __main__ - main.py:4064 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   State update valid: True
[2025-06-25 13:45:26,945] INFO - __main__ - main.py:4071 - [c41fae84-9c55-45f4-9baa-15746b17ada8]   Diagnostic complete: False
[2025-06-25 13:45:26,946] WARNING - __main__ - main.py:6140 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:45:26,946] INFO - __main__ - main.py:6149 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:45:26,947] INFO - __main__ - main.py:6150 - [c41fae84-9c55-45f4-9baa-15746b17ada8] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:45:27,464] WARNING - __main__ - main.py:6195 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:45:27,464] WARNING - __main__ - main.py:6196 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:45:27,465] WARNING - __main__ - main.py:6197 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Probing Level: 5
[2025-06-25 13:45:27,467] WARNING - __main__ - main.py:6198 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Question Index: 1
[2025-06-25 13:45:27,468] WARNING - __main__ - main.py:6199 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Diagnostic Complete: False
[2025-06-25 13:45:27,469] WARNING - __main__ - main.py:6206 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:45:27,470] WARNING - __main__ - main.py:6207 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:45:27,470] WARNING - __main__ - main.py:6208 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Quiz Started: False
[2025-06-25 13:45:28,958] INFO - __main__ - main.py:6268 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ Updated existing session document: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:45:28,959] WARNING - __main__ - main.py:6269 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:45:28,960] WARNING - __main__ - main.py:6270 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Session ID: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:45:28,960] WARNING - __main__ - main.py:6271 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:45:28,961] WARNING - __main__ - main.py:6272 - [c41fae84-9c55-45f4-9baa-15746b17ada8] ✅   - Interaction logged successfully
[2025-06-25 13:45:28,962] INFO - __main__ - main.py:11315 - [c41fae84-9c55-45f4-9baa-15746b17ada8] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:45:28,963] DEBUG - __main__ - main.py:2812 - [c41fae84-9c55-45f4-9baa-15746b17ada8] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:45:28,964] DEBUG - __main__ - main.py:6318 - [c41fae84-9c55-45f4-9baa-15746b17ada8] No final assessment data found in AI response
[2025-06-25 13:45:28,967] INFO - __main__ - main.py:6404 - [c41fae84-9c55-45f4-9baa-15746b17ada8] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:45:28,969] WARNING - __main__ - main.py:680 - High response time detected: 6.84s for enhance_content_api
[2025-06-25 13:45:28,971] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:45:28] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:46:04,578] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:46:04,581] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "ebd62dde-1054-447e-9a67-ecdd94f809bd", "timestamp": "2025-06-25T12:46:04.579463+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "Ice is hard and solid. Water is wet and you can pour it. Steam is like a cloud you can't touch.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_ffd70b39-9953-417b-8fcd-0c9985c06878", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm so excited to start our States of Matter adventure today! You want to begin the diagnostic assessment? That's a fantastic idea, Andrea! It helps me see what you already know so we can learn even more together. Let's get started!\n\nTo kick things off, Andrea, can you tell me in your own words, what do you think \"matter\" is?", "timestamp": "2025-06-25T12:43:02.716Z"}, {"role": "user", "content": "Matter is like all the stuff in the world. It's anything you can touch, see, or feel.", "timestamp": "2025-06-25T12:44:09.233Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"All the stuff in the world\" is exactly right. And yes, anything you can touch, see, or feel is a great clue. You're already thinking like a scientist, Andrea!\n\nSo, if matter is all the stuff around us, what do you think happens when we look at different kinds of stuff, like water, ice, and the steam from a kettle? Do they all seem to be the same?", "timestamp": "2025-06-25T12:44:15.983Z"}, {"role": "user", "content": "no", "timestamp": "2025-06-25T12:45:22.009Z"}, {"role": "assistant", "content": "That's a great answer, Andrea! You're absolutely right, they don't all seem to be the same, do they? Water, ice, and steam look and feel very different.\n\nThis difference is actually what helps us understand the **states of matter**!\n\nThinking about water, ice, and steam, what makes them different from each other in terms of how they look or feel?", "timestamp": "2025-06-25T12:45:28.993Z"}]}}
[2025-06-25 13:46:04,583] INFO - auth_decorator - auth_decorator.py:39 - [ebd62dde-1054-447e-9a67-ecdd94f809bd][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:46:04,586] INFO - auth_decorator - auth_decorator.py:56 - [ebd62dde-1054-447e-9a67-ecdd94f809bd][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:46:04,588] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:46:04,591] WARNING - __main__ - main.py:5262 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:46:04,594] INFO - __main__ - main.py:5315 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ffd70b39-9953-417b-8fcd-0c9985c06878', lesson_ref='P5-BST-002'
[2025-06-25 13:46:04,888] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:46:04,889] INFO - __main__ - main.py:5361 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:46:04,890] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:46:04,891] INFO - __main__ - main.py:5380 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:46:04,891] INFO - __main__ - main.py:5419 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:46:04,892] INFO - __main__ - main.py:2332 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:46:05,395] INFO - __main__ - main.py:2391 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:46:05,396] INFO - __main__ - main.py:2391 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:46:05,396] INFO - __main__ - main.py:2391 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:46:05,397] INFO - __main__ - main.py:2391 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:46:05,397] INFO - __main__ - main.py:2391 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:46:05,398] INFO - __main__ - main.py:2460 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:46:05,399] DEBUG - __main__ - main.py:2474 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:46:05,399] DEBUG - __main__ - main.py:2477 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:46:05,402] DEBUG - __main__ - main.py:2478 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:46:05,402] DEBUG - __main__ - main.py:2479 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:46:05,404] INFO - __main__ - main.py:2483 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:46:05,784] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:46:05,785] INFO - __main__ - main.py:2493 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Gemini API call completed in 0.38s. Raw response: 'materials'
[2025-06-25 13:46:05,787] DEBUG - __main__ - main.py:2515 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:46:05,788] INFO - __main__ - main.py:2520 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:46:05,789] INFO - __main__ - main.py:5452 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Successfully inferred module ID via AI: materials
[2025-06-25 13:46:05,790] INFO - __main__ - main.py:5478 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:46:06,077] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:46:06,584] WARNING - __main__ - main.py:5502 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:46:06,586] WARNING - __main__ - main.py:5503 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   - Session exists: True
[2025-06-25 13:46:06,587] WARNING - __main__ - main.py:5504 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:46:06,587] WARNING - __main__ - main.py:5505 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 13:46:06,589] WARNING - __main__ - main.py:5525 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:46:06,590] INFO - __main__ - main.py:5561 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] State protection not triggered
[2025-06-25 13:46:06,591] INFO - __main__ - main.py:5596 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:46:06,592] INFO - __main__ - main.py:5597 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   assigned_level_for_teaching (session): None
[2025-06-25 13:46:06,593] INFO - __main__ - main.py:5598 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   latest_assessed_level (profile): None
[2025-06-25 13:46:06,593] INFO - __main__ - main.py:5599 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   teaching_level_for_returning_student: None
[2025-06-25 13:46:06,594] INFO - __main__ - main.py:5600 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   has_completed_diagnostic_before: False
[2025-06-25 13:46:06,595] INFO - __main__ - main.py:5601 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   is_first_encounter_for_module: True
[2025-06-25 13:46:06,595] WARNING - __main__ - main.py:5606 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:46:06,596] INFO - __main__ - main.py:5612 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:46:06,597] INFO - __main__ - main.py:5613 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:46:06,597] INFO - __main__ - main.py:5614 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:46:06,598] INFO - __main__ - main.py:5615 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Is first encounter: True
[2025-06-25 13:46:06,599] INFO - __main__ - main.py:5616 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Diagnostic completed: False
[2025-06-25 13:46:06,600] INFO - __main__ - main.py:5622 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:46:06,600] INFO - __main__ - main.py:5636 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:46:06,601] INFO - __main__ - main.py:5638 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:46:06,602] INFO - __main__ - main.py:5658 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 13:46:06,603] INFO - __main__ - main.py:3813 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Diagnostic context validation passed
[2025-06-25 13:46:06,604] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:46:06,605] WARNING - __main__ - main.py:5746 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:46:06,605] INFO - __main__ - main.py:3923 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Enhanced diagnostic context with 45 fields
[2025-06-25 13:46:06,606] INFO - __main__ - main.py:5765 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:46:06,607] INFO - __main__ - main.py:5778 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:46:06,608] DEBUG - __main__ - main.py:5779 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:46:06,608] WARNING - __main__ - main.py:5980 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:46:06,609] WARNING - __main__ - main.py:5981 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:46:06,610] WARNING - __main__ - main.py:5982 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖   - Student query: Ice is hard and solid. Water is wet and you can pour it. Steam is like a cloud you can't touch....
[2025-06-25 13:46:06,612] INFO - __main__ - main.py:10403 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:46:06,612] INFO - __main__ - main.py:10404 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:46:06,613] INFO - __main__ - main.py:10405 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:46:06,614] INFO - __main__ - main.py:10406 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 👤   final student_name: Andrea
[2025-06-25 13:46:06,616] INFO - __main__ - main.py:10486 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:46:07,450] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 390 characters
[2025-06-25 13:46:07,452] INFO - __main__ - main.py:10507 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:46:07,453] INFO - __main__ - main.py:10538 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ Generated natural response for Andrea: 390 chars
[2025-06-25 13:46:07,453] INFO - __main__ - main.py:10539 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:07,454] WARNING - __main__ - main.py:6004 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:46:07,455] WARNING - __main__ - main.py:6005 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖   - Content length: 390 chars
[2025-06-25 13:46:07,456] WARNING - __main__ - main.py:6006 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:07,457] WARNING - __main__ - main.py:6007 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🤖   - Raw state block: None...
[2025-06-25 13:46:07,458] INFO - __main__ - main.py:6032 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:46:07,459] INFO - __main__ - main.py:6033 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:46:08,209] INFO - __main__ - main.py:6082 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI processing completed in 1.60s
[2025-06-25 13:46:08,210] WARNING - __main__ - main.py:6093 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:46:08,211] INFO - __main__ - main.py:4018 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:46:08,212] WARNING - __main__ - main.py:6102 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:46:08,212] WARNING - __main__ - main.py:6109 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:46:08,213] WARNING - __main__ - main.py:6116 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:46:08,214] WARNING - __main__ - main.py:6117 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:46:08,215] WARNING - __main__ - main.py:6118 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:46:08,217] WARNING - __main__ - main.py:6119 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:08,218] WARNING - __main__ - main.py:6120 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:46:08,218] WARNING - __main__ - main.py:6123 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:46:08,219] WARNING - __main__ - main.py:6124 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:46:08,221] WARNING - __main__ - main.py:6125 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:08,223] WARNING - __main__ - main.py:6126 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:46:08,224] WARNING - __main__ - main.py:6127 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 💾   - Phase change: False
[2025-06-25 13:46:08,224] INFO - __main__ - main.py:4050 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:46:08,224] INFO - __main__ - main.py:4051 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:46:08,225] INFO - __main__ - main.py:4052 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Current level: 5
[2025-06-25 13:46:08,226] INFO - __main__ - main.py:4053 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Question index: 0
[2025-06-25 13:46:08,226] INFO - __main__ - main.py:4054 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   First encounter: True
[2025-06-25 13:46:08,226] INFO - __main__ - main.py:4059 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Answers collected: 0
[2025-06-25 13:46:08,227] INFO - __main__ - main.py:4060 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Levels failed: 0
[2025-06-25 13:46:08,227] INFO - __main__ - main.py:4018 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:46:08,228] INFO - __main__ - main.py:4064 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   State update valid: True
[2025-06-25 13:46:08,229] INFO - __main__ - main.py:4071 - [ebd62dde-1054-447e-9a67-ecdd94f809bd]   Diagnostic complete: False
[2025-06-25 13:46:08,229] WARNING - __main__ - main.py:6140 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:46:08,230] INFO - __main__ - main.py:6149 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:46:08,231] INFO - __main__ - main.py:6150 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:46:08,849] WARNING - __main__ - main.py:6195 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:46:08,851] WARNING - __main__ - main.py:6196 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:46:08,852] WARNING - __main__ - main.py:6197 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Probing Level: 5
[2025-06-25 13:46:08,853] WARNING - __main__ - main.py:6198 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Question Index: 1
[2025-06-25 13:46:08,854] WARNING - __main__ - main.py:6199 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Diagnostic Complete: False
[2025-06-25 13:46:08,855] WARNING - __main__ - main.py:6206 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:46:08,856] WARNING - __main__ - main.py:6207 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:46:08,857] WARNING - __main__ - main.py:6208 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Quiz Started: False
[2025-06-25 13:46:10,240] INFO - __main__ - main.py:6268 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ Updated existing session document: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:46:10,241] WARNING - __main__ - main.py:6269 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:46:10,241] WARNING - __main__ - main.py:6270 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Session ID: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:46:10,242] WARNING - __main__ - main.py:6271 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:46:10,243] WARNING - __main__ - main.py:6272 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] ✅   - Interaction logged successfully
[2025-06-25 13:46:10,244] INFO - __main__ - main.py:11315 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:46:10,245] DEBUG - __main__ - main.py:2812 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:46:10,245] DEBUG - __main__ - main.py:6318 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] No final assessment data found in AI response
[2025-06-25 13:46:10,248] INFO - __main__ - main.py:6404 - [ebd62dde-1054-447e-9a67-ecdd94f809bd] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:46:10,249] WARNING - __main__ - main.py:680 - High response time detected: 5.66s for enhance_content_api
[2025-06-25 13:46:10,250] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:46:10] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:46:34,273] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:46:34,275] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "7504e748-82d5-47d7-8975-8ed8eb042be1", "timestamp": "2025-06-25T12:46:34.274608+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "Ice is a solid. Water is a liquid.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_ffd70b39-9953-417b-8fcd-0c9985c06878", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm so excited to start our States of Matter adventure today! You want to begin the diagnostic assessment? That's a fantastic idea, Andrea! It helps me see what you already know so we can learn even more together. Let's get started!\n\nTo kick things off, Andrea, can you tell me in your own words, what do you think \"matter\" is?", "timestamp": "2025-06-25T12:43:02.716Z"}, {"role": "user", "content": "Matter is like all the stuff in the world. It's anything you can touch, see, or feel.", "timestamp": "2025-06-25T12:44:09.233Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"All the stuff in the world\" is exactly right. And yes, anything you can touch, see, or feel is a great clue. You're already thinking like a scientist, Andrea!\n\nSo, if matter is all the stuff around us, what do you think happens when we look at different kinds of stuff, like water, ice, and the steam from a kettle? Do they all seem to be the same?", "timestamp": "2025-06-25T12:44:15.983Z"}, {"role": "user", "content": "no", "timestamp": "2025-06-25T12:45:22.009Z"}, {"role": "assistant", "content": "That's a great answer, Andrea! You're absolutely right, they don't all seem to be the same, do they? Water, ice, and steam look and feel very different.\n\nThis difference is actually what helps us understand the **states of matter**!\n\nThinking about water, ice, and steam, what makes them different from each other in terms of how they look or feel?", "timestamp": "2025-06-25T12:45:28.993Z"}, {"role": "user", "content": "Ice is hard and solid. Water is wet and you can pour it. Steam is like a cloud you can't touch.", "timestamp": "2025-06-25T12:46:04.476Z"}, {"role": "assistant", "content": "That's a fantastic description, Andrea! You've perfectly captured the differences. Ice is indeed hard and solid, water is wet and pourable, and steam is like an invisible cloud.\n\nYou've just described the three main **states of matter**: solid, liquid, and gas.\n\nSo, Andrea, based on your excellent descriptions, can you tell me which state of matter you think ice is? And what about water?", "timestamp": "2025-06-25T12:46:10.264Z"}]}}
[2025-06-25 13:46:34,279] INFO - auth_decorator - auth_decorator.py:39 - [7504e748-82d5-47d7-8975-8ed8eb042be1][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:46:34,280] INFO - auth_decorator - auth_decorator.py:56 - [7504e748-82d5-47d7-8975-8ed8eb042be1][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:46:34,281] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:46:34,285] WARNING - __main__ - main.py:5262 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:46:34,289] INFO - __main__ - main.py:5315 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ffd70b39-9953-417b-8fcd-0c9985c06878', lesson_ref='P5-BST-002'
[2025-06-25 13:46:35,786] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:46:35,787] INFO - __main__ - main.py:5361 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:46:35,789] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:46:35,790] INFO - __main__ - main.py:5380 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:46:35,790] INFO - __main__ - main.py:5419 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:46:35,791] INFO - __main__ - main.py:2332 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:46:36,182] INFO - __main__ - main.py:2391 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:46:36,183] INFO - __main__ - main.py:2391 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:46:36,184] INFO - __main__ - main.py:2391 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:46:36,185] INFO - __main__ - main.py:2391 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:46:36,186] INFO - __main__ - main.py:2391 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:46:36,187] INFO - __main__ - main.py:2460 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:46:36,188] DEBUG - __main__ - main.py:2474 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:46:36,188] DEBUG - __main__ - main.py:2477 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:46:36,190] DEBUG - __main__ - main.py:2478 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:46:36,190] DEBUG - __main__ - main.py:2479 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:46:36,191] INFO - __main__ - main.py:2483 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:46:37,045] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:46:37,046] INFO - __main__ - main.py:2493 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Gemini API call completed in 0.85s. Raw response: 'materials'
[2025-06-25 13:46:37,047] DEBUG - __main__ - main.py:2515 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:46:37,047] INFO - __main__ - main.py:2520 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:46:37,048] INFO - __main__ - main.py:5452 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Successfully inferred module ID via AI: materials
[2025-06-25 13:46:37,048] INFO - __main__ - main.py:5478 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:46:37,338] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:46:37,847] WARNING - __main__ - main.py:5502 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:46:37,848] WARNING - __main__ - main.py:5503 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   - Session exists: True
[2025-06-25 13:46:37,848] WARNING - __main__ - main.py:5504 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:46:37,848] WARNING - __main__ - main.py:5505 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 13:46:37,850] WARNING - __main__ - main.py:5525 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:46:37,852] INFO - __main__ - main.py:5561 - [7504e748-82d5-47d7-8975-8ed8eb042be1] State protection not triggered
[2025-06-25 13:46:37,855] INFO - __main__ - main.py:5596 - [7504e748-82d5-47d7-8975-8ed8eb042be1] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:46:37,855] INFO - __main__ - main.py:5597 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   assigned_level_for_teaching (session): None
[2025-06-25 13:46:37,856] INFO - __main__ - main.py:5598 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   latest_assessed_level (profile): None
[2025-06-25 13:46:37,856] INFO - __main__ - main.py:5599 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   teaching_level_for_returning_student: None
[2025-06-25 13:46:37,856] INFO - __main__ - main.py:5600 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   has_completed_diagnostic_before: False
[2025-06-25 13:46:37,857] INFO - __main__ - main.py:5601 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   is_first_encounter_for_module: True
[2025-06-25 13:46:37,857] WARNING - __main__ - main.py:5606 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:46:37,857] INFO - __main__ - main.py:5612 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:46:37,858] INFO - __main__ - main.py:5613 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:46:37,858] INFO - __main__ - main.py:5614 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:46:37,858] INFO - __main__ - main.py:5615 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Is first encounter: True
[2025-06-25 13:46:37,859] INFO - __main__ - main.py:5616 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Diagnostic completed: False
[2025-06-25 13:46:37,859] INFO - __main__ - main.py:5622 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:46:37,859] INFO - __main__ - main.py:5636 - [7504e748-82d5-47d7-8975-8ed8eb042be1] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:46:37,860] INFO - __main__ - main.py:5638 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:46:37,860] INFO - __main__ - main.py:5658 - [7504e748-82d5-47d7-8975-8ed8eb042be1] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 13:46:37,860] INFO - __main__ - main.py:3813 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Diagnostic context validation passed
[2025-06-25 13:46:37,861] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:46:37,861] WARNING - __main__ - main.py:5746 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:46:37,861] INFO - __main__ - main.py:3923 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Enhanced diagnostic context with 45 fields
[2025-06-25 13:46:37,862] INFO - __main__ - main.py:5765 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:46:37,862] INFO - __main__ - main.py:5778 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:46:37,863] DEBUG - __main__ - main.py:5779 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:46:37,863] WARNING - __main__ - main.py:5980 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:46:37,863] WARNING - __main__ - main.py:5981 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:46:37,863] WARNING - __main__ - main.py:5982 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖   - Student query: Ice is a solid. Water is a liquid....
[2025-06-25 13:46:37,864] INFO - __main__ - main.py:10403 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:46:37,865] INFO - __main__ - main.py:10404 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:46:37,865] INFO - __main__ - main.py:10405 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:46:37,865] INFO - __main__ - main.py:10406 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 👤   final student_name: Andrea
[2025-06-25 13:46:37,867] INFO - __main__ - main.py:10486 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:46:38,522] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 301 characters
[2025-06-25 13:46:38,524] INFO - __main__ - main.py:10507 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:46:38,524] INFO - __main__ - main.py:10538 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ Generated natural response for Andrea: 301 chars
[2025-06-25 13:46:38,525] INFO - __main__ - main.py:10539 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:38,526] WARNING - __main__ - main.py:6004 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:46:38,527] WARNING - __main__ - main.py:6005 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖   - Content length: 301 chars
[2025-06-25 13:46:38,528] WARNING - __main__ - main.py:6006 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:38,528] WARNING - __main__ - main.py:6007 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🤖   - Raw state block: None...
[2025-06-25 13:46:38,530] INFO - __main__ - main.py:6032 - [7504e748-82d5-47d7-8975-8ed8eb042be1] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:46:38,531] INFO - __main__ - main.py:6033 - [7504e748-82d5-47d7-8975-8ed8eb042be1] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:46:38,838] INFO - __main__ - main.py:6082 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI processing completed in 0.97s
[2025-06-25 13:46:38,838] WARNING - __main__ - main.py:6093 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:46:38,839] INFO - __main__ - main.py:4018 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:46:38,840] WARNING - __main__ - main.py:6102 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:46:38,840] WARNING - __main__ - main.py:6109 - [7504e748-82d5-47d7-8975-8ed8eb042be1] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:46:38,841] WARNING - __main__ - main.py:6116 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:46:38,841] WARNING - __main__ - main.py:6117 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:46:38,843] WARNING - __main__ - main.py:6118 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:46:38,844] WARNING - __main__ - main.py:6119 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:38,845] WARNING - __main__ - main.py:6120 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:46:38,846] WARNING - __main__ - main.py:6123 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:46:38,847] WARNING - __main__ - main.py:6124 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:46:38,847] WARNING - __main__ - main.py:6125 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:46:38,848] WARNING - __main__ - main.py:6126 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:46:38,849] WARNING - __main__ - main.py:6127 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 💾   - Phase change: False
[2025-06-25 13:46:38,849] INFO - __main__ - main.py:4050 - [7504e748-82d5-47d7-8975-8ed8eb042be1] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:46:38,850] INFO - __main__ - main.py:4051 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:46:38,851] INFO - __main__ - main.py:4052 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Current level: 5
[2025-06-25 13:46:38,852] INFO - __main__ - main.py:4053 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Question index: 0
[2025-06-25 13:46:38,853] INFO - __main__ - main.py:4054 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   First encounter: True
[2025-06-25 13:46:38,854] INFO - __main__ - main.py:4059 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Answers collected: 0
[2025-06-25 13:46:38,854] INFO - __main__ - main.py:4060 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Levels failed: 0
[2025-06-25 13:46:38,855] INFO - __main__ - main.py:4018 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:46:38,856] INFO - __main__ - main.py:4064 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   State update valid: True
[2025-06-25 13:46:38,856] INFO - __main__ - main.py:4071 - [7504e748-82d5-47d7-8975-8ed8eb042be1]   Diagnostic complete: False
[2025-06-25 13:46:38,857] WARNING - __main__ - main.py:6140 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:46:38,858] INFO - __main__ - main.py:6149 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:46:38,858] INFO - __main__ - main.py:6150 - [7504e748-82d5-47d7-8975-8ed8eb042be1] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:46:39,355] WARNING - __main__ - main.py:6195 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:46:39,356] WARNING - __main__ - main.py:6196 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:46:39,357] WARNING - __main__ - main.py:6197 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Probing Level: 5
[2025-06-25 13:46:39,357] WARNING - __main__ - main.py:6198 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Question Index: 1
[2025-06-25 13:46:39,358] WARNING - __main__ - main.py:6199 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Diagnostic Complete: False
[2025-06-25 13:46:39,359] WARNING - __main__ - main.py:6206 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:46:39,360] WARNING - __main__ - main.py:6207 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:46:39,361] WARNING - __main__ - main.py:6208 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Quiz Started: False
[2025-06-25 13:46:40,203] INFO - __main__ - main.py:6268 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ Updated existing session document: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:46:40,203] WARNING - __main__ - main.py:6269 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:46:40,204] WARNING - __main__ - main.py:6270 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Session ID: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:46:40,204] WARNING - __main__ - main.py:6271 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:46:40,205] WARNING - __main__ - main.py:6272 - [7504e748-82d5-47d7-8975-8ed8eb042be1] ✅   - Interaction logged successfully
[2025-06-25 13:46:40,206] INFO - __main__ - main.py:11315 - [7504e748-82d5-47d7-8975-8ed8eb042be1] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:46:40,207] DEBUG - __main__ - main.py:2812 - [7504e748-82d5-47d7-8975-8ed8eb042be1] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:46:40,207] DEBUG - __main__ - main.py:6318 - [7504e748-82d5-47d7-8975-8ed8eb042be1] No final assessment data found in AI response
[2025-06-25 13:46:40,210] INFO - __main__ - main.py:6404 - [7504e748-82d5-47d7-8975-8ed8eb042be1] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:46:40,212] WARNING - __main__ - main.py:680 - High response time detected: 5.93s for enhance_content_api
[2025-06-25 13:46:40,212] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:46:40] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 13:47:11,447] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 13:47:11,451] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "3681be1a-4526-4aa1-9484-48c813cc54cb", "timestamp": "2025-06-25T12:47:11.448460+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-BST-002", "content_to_enhance": "gas", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Basic Science and Technology", "session_id": "session_ffd70b39-9953-417b-8fcd-0c9985c06878", "chat_history": [{"role": "user", "content": "Matter is like all the stuff in the world. It's anything you can touch, see, or feel.", "timestamp": "2025-06-25T12:44:09.233Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! \"All the stuff in the world\" is exactly right. And yes, anything you can touch, see, or feel is a great clue. You're already thinking like a scientist, Andrea!\n\nSo, if matter is all the stuff around us, what do you think happens when we look at different kinds of stuff, like water, ice, and the steam from a kettle? Do they all seem to be the same?", "timestamp": "2025-06-25T12:44:15.983Z"}, {"role": "user", "content": "no", "timestamp": "2025-06-25T12:45:22.009Z"}, {"role": "assistant", "content": "That's a great answer, Andrea! You're absolutely right, they don't all seem to be the same, do they? Water, ice, and steam look and feel very different.\n\nThis difference is actually what helps us understand the **states of matter**!\n\nThinking about water, ice, and steam, what makes them different from each other in terms of how they look or feel?", "timestamp": "2025-06-25T12:45:28.993Z"}, {"role": "user", "content": "Ice is hard and solid. Water is wet and you can pour it. Steam is like a cloud you can't touch.", "timestamp": "2025-06-25T12:46:04.476Z"}, {"role": "assistant", "content": "That's a fantastic description, Andrea! You've perfectly captured the differences. Ice is indeed hard and solid, water is wet and pourable, and steam is like an invisible cloud.\n\nYou've just described the three main **states of matter**: solid, liquid, and gas.\n\nSo, Andrea, based on your excellent descriptions, can you tell me which state of matter you think ice is? And what about water?", "timestamp": "2025-06-25T12:46:10.264Z"}, {"role": "user", "content": "Ice is a solid. Water is a liquid.", "timestamp": "2025-06-25T12:46:34.168Z"}, {"role": "assistant", "content": "That's absolutely correct, Andrea! Ice is a perfect example of a **solid**, and water is a classic **liquid**. You're doing so well!\n\nNow, thinking about steam, which you described as a cloud you can't touch, which of the three states of matter \u2013 solid, liquid, or gas \u2013 do you think steam belongs to?", "timestamp": "2025-06-25T12:46:40.228Z"}]}}
[2025-06-25 13:47:11,457] INFO - auth_decorator - auth_decorator.py:39 - [3681be1a-4526-4aa1-9484-48c813cc54cb][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 13:47:11,459] INFO - auth_decorator - auth_decorator.py:56 - [3681be1a-4526-4aa1-9484-48c813cc54cb][require_auth] Development mode detected - bypassing authentication
[2025-06-25 13:47:11,461] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 13:47:11,463] WARNING - __main__ - main.py:5262 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 13:47:11,467] INFO - __main__ - main.py:5315 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ffd70b39-9953-417b-8fcd-0c9985c06878', lesson_ref='P5-BST-002'
[2025-06-25 13:47:12,659] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 13:47:12,660] INFO - __main__ - main.py:5361 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:47:12,661] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 13:47:12,662] INFO - __main__ - main.py:5380 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 13:47:12,662] INFO - __main__ - main.py:5419 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Attempting to infer module. GS Subject Slug: 'science'.
[2025-06-25 13:47:12,663] INFO - __main__ - main.py:2332 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Inferring module for subject 'science', lesson 'Exploring States of Matter'.
[2025-06-25 13:47:13,591] INFO - __main__ - main.py:2391 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Loaded metadata for module 'earth_and_space' ('Earth & Space')
[2025-06-25 13:47:13,592] INFO - __main__ - main.py:2391 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Loaded metadata for module 'forces_and_motion' ('Forces & Motion')
[2025-06-25 13:47:13,592] INFO - __main__ - main.py:2391 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Loaded metadata for module 'living_things_and_their_habitats' ('Living Things & Their Habitats')
[2025-06-25 13:47:13,593] INFO - __main__ - main.py:2391 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Loaded metadata for module 'materials' ('Materials')
[2025-06-25 13:47:13,594] INFO - __main__ - main.py:2391 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Loaded metadata for module 'working_scientifically' ('Working Scientifically')
[2025-06-25 13:47:13,594] INFO - __main__ - main.py:2460 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Starting module inference for subject 'science' with 5 module options
[2025-06-25 13:47:13,595] DEBUG - __main__ - main.py:2474 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Sample modules available (first 3):
- earth_and_space: Earth & Space
- forces_and_motion: Forces & Motion
- living_things_and_their_habitats: Living Things & Their Habitats
[2025-06-25 13:47:13,596] DEBUG - __main__ - main.py:2477 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on ...
[2025-06-25 13:47:13,597] DEBUG - __main__ - main.py:2478 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference Lesson Summary (first 300 chars): Lesson Title: Exploring States of Matter. Topic: States of Matter. Learning Objectives: Identify the three states of matter.; Understand how matter changes state.. Key Concepts: Identify; three; states; matter; Understand; changes; state; Warm; Up Activity; Pose. Introduction: Today, we embark on an...
[2025-06-25 13:47:13,598] DEBUG - __main__ - main.py:2479 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference Module Options (first 200 chars): 1. Slug: "earth_and_space", Name: "Earth & Space", Description: "Earth science and astronomy, contextualised with Nigerian geography and climate...."
2. Slug: "forces_and_motion", Name: "Forces & Moti...
[2025-06-25 13:47:13,599] INFO - __main__ - main.py:2483 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Calling Gemini API for module inference...
[2025-06-25 13:47:13,993] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 9 characters
[2025-06-25 13:47:13,994] INFO - __main__ - main.py:2493 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Gemini API call completed in 0.39s. Raw response: 'materials'
[2025-06-25 13:47:13,994] DEBUG - __main__ - main.py:2515 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Cleaned slug: 'materials'
[2025-06-25 13:47:13,994] INFO - __main__ - main.py:2520 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI Inference: Successfully matched module by slug. Chosen: 'materials' (Materials)
[2025-06-25 13:47:13,995] INFO - __main__ - main.py:5452 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Successfully inferred module ID via AI: materials
[2025-06-25 13:47:13,995] INFO - __main__ - main.py:5478 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Effective module name for prompt context: 'Materials' (Module ID: materials)
[2025-06-25 13:47:14,298] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: science_Primary 5_science_materials
[2025-06-25 13:47:14,786] WARNING - __main__ - main.py:5502 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍 SESSION STATE DEBUG:
[2025-06-25 13:47:14,787] WARNING - __main__ - main.py:5503 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   - Session exists: True
[2025-06-25 13:47:14,788] WARNING - __main__ - main.py:5504 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 13:47:14,789] WARNING - __main__ - main.py:5505 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 13:47:14,791] WARNING - __main__ - main.py:5525 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 13:47:14,792] INFO - __main__ - main.py:5561 - [3681be1a-4526-4aa1-9484-48c813cc54cb] State protection not triggered
[2025-06-25 13:47:14,793] INFO - __main__ - main.py:5596 - [3681be1a-4526-4aa1-9484-48c813cc54cb] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 13:47:14,794] INFO - __main__ - main.py:5597 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   assigned_level_for_teaching (session): None
[2025-06-25 13:47:14,794] INFO - __main__ - main.py:5598 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   latest_assessed_level (profile): None
[2025-06-25 13:47:14,795] INFO - __main__ - main.py:5599 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   teaching_level_for_returning_student: None
[2025-06-25 13:47:14,796] INFO - __main__ - main.py:5600 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   has_completed_diagnostic_before: False
[2025-06-25 13:47:14,796] INFO - __main__ - main.py:5601 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   is_first_encounter_for_module: True
[2025-06-25 13:47:14,797] WARNING - __main__ - main.py:5606 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 13:47:14,798] INFO - __main__ - main.py:5612 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍 PHASE INVESTIGATION:
[2025-06-25 13:47:14,799] INFO - __main__ - main.py:5613 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:47:14,800] INFO - __main__ - main.py:5614 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 13:47:14,800] INFO - __main__ - main.py:5615 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Is first encounter: True
[2025-06-25 13:47:14,801] INFO - __main__ - main.py:5616 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Diagnostic completed: False
[2025-06-25 13:47:14,801] INFO - __main__ - main.py:5622 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 13:47:14,802] INFO - __main__ - main.py:5636 - [3681be1a-4526-4aa1-9484-48c813cc54cb] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 13:47:14,804] INFO - __main__ - main.py:5638 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 13:47:14,805] INFO - __main__ - main.py:5658 - [3681be1a-4526-4aa1-9484-48c813cc54cb] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 13:47:14,806] INFO - __main__ - main.py:3813 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Diagnostic context validation passed
[2025-06-25 13:47:14,806] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 13:47:14,807] WARNING - __main__ - main.py:5746 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 13:47:14,808] INFO - __main__ - main.py:3923 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Enhanced diagnostic context with 45 fields
[2025-06-25 13:47:14,809] INFO - __main__ - main.py:5765 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 13:47:14,810] INFO - __main__ - main.py:5778 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 13:47:14,810] DEBUG - __main__ - main.py:5779 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 13:47:14,811] WARNING - __main__ - main.py:5980 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 13:47:14,812] WARNING - __main__ - main.py:5981 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 13:47:14,813] WARNING - __main__ - main.py:5982 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖   - Student query: gas...
[2025-06-25 13:47:14,814] INFO - __main__ - main.py:10403 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 👤 STUDENT NAME DEBUG:
[2025-06-25 13:47:14,815] INFO - __main__ - main.py:10404 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 13:47:14,816] INFO - __main__ - main.py:10405 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 👤   context.student_name: NOT_FOUND
[2025-06-25 13:47:14,817] INFO - __main__ - main.py:10406 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 👤   final student_name: Andrea
[2025-06-25 13:47:14,819] INFO - __main__ - main.py:10486 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖 Generating natural AI response for Andrea...
[2025-06-25 13:47:15,470] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 398 characters
[2025-06-25 13:47:15,470] INFO - __main__ - main.py:10507 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 13:47:15,471] INFO - __main__ - main.py:10538 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ Generated natural response for Andrea: 398 chars
[2025-06-25 13:47:15,472] INFO - __main__ - main.py:10539 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:47:15,472] WARNING - __main__ - main.py:6004 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 13:47:15,473] WARNING - __main__ - main.py:6005 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖   - Content length: 398 chars
[2025-06-25 13:47:15,473] WARNING - __main__ - main.py:6006 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:47:15,474] WARNING - __main__ - main.py:6007 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🤖   - Raw state block: None...
[2025-06-25 13:47:15,475] INFO - __main__ - main.py:6032 - [3681be1a-4526-4aa1-9484-48c813cc54cb] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 13:47:15,476] INFO - __main__ - main.py:6033 - [3681be1a-4526-4aa1-9484-48c813cc54cb] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 13:47:15,776] INFO - __main__ - main.py:6082 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI processing completed in 0.96s
[2025-06-25 13:47:15,777] WARNING - __main__ - main.py:6093 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 13:47:15,777] INFO - __main__ - main.py:4018 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:47:15,777] WARNING - __main__ - main.py:6102 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 13:47:15,778] WARNING - __main__ - main.py:6109 - [3681be1a-4526-4aa1-9484-48c813cc54cb] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 13:47:15,779] WARNING - __main__ - main.py:6116 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 13:47:15,779] WARNING - __main__ - main.py:6117 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 13:47:15,780] WARNING - __main__ - main.py:6118 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 13:47:15,781] WARNING - __main__ - main.py:6119 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:47:15,782] WARNING - __main__ - main.py:6120 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:47:15,783] WARNING - __main__ - main.py:6123 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 💾 FINAL STATE APPLICATION:
[2025-06-25 13:47:15,783] WARNING - __main__ - main.py:6124 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 13:47:15,784] WARNING - __main__ - main.py:6125 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 13:47:15,784] WARNING - __main__ - main.py:6126 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 13:47:15,786] WARNING - __main__ - main.py:6127 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 💾   - Phase change: False
[2025-06-25 13:47:15,788] INFO - __main__ - main.py:4050 - [3681be1a-4526-4aa1-9484-48c813cc54cb] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 13:47:15,788] INFO - __main__ - main.py:4051 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 13:47:15,789] INFO - __main__ - main.py:4052 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Current level: 5
[2025-06-25 13:47:15,790] INFO - __main__ - main.py:4053 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Question index: 0
[2025-06-25 13:47:15,790] INFO - __main__ - main.py:4054 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   First encounter: True
[2025-06-25 13:47:15,791] INFO - __main__ - main.py:4059 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Answers collected: 0
[2025-06-25 13:47:15,792] INFO - __main__ - main.py:4060 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Levels failed: 0
[2025-06-25 13:47:15,792] INFO - __main__ - main.py:4018 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:47:15,793] INFO - __main__ - main.py:4064 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   State update valid: True
[2025-06-25 13:47:15,793] INFO - __main__ - main.py:4071 - [3681be1a-4526-4aa1-9484-48c813cc54cb]   Diagnostic complete: False
[2025-06-25 13:47:15,794] WARNING - __main__ - main.py:6140 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 13:47:15,795] INFO - __main__ - main.py:6149 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 13:47:15,796] INFO - __main__ - main.py:6150 - [3681be1a-4526-4aa1-9484-48c813cc54cb] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 13:47:16,394] WARNING - __main__ - main.py:6195 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 13:47:16,394] WARNING - __main__ - main.py:6196 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Phase: diagnostic_start_probe
[2025-06-25 13:47:16,395] WARNING - __main__ - main.py:6197 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Probing Level: 5
[2025-06-25 13:47:16,396] WARNING - __main__ - main.py:6198 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Question Index: 1
[2025-06-25 13:47:16,396] WARNING - __main__ - main.py:6199 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Diagnostic Complete: False
[2025-06-25 13:47:16,397] WARNING - __main__ - main.py:6206 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Quiz Questions Saved: 0
[2025-06-25 13:47:16,398] WARNING - __main__ - main.py:6207 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Quiz Answers Saved: 0
[2025-06-25 13:47:16,399] WARNING - __main__ - main.py:6208 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Quiz Started: False
[2025-06-25 13:47:18,434] INFO - __main__ - main.py:6268 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ Updated existing session document: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:47:18,435] WARNING - __main__ - main.py:6269 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 13:47:18,437] WARNING - __main__ - main.py:6270 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Session ID: session_ffd70b39-9953-417b-8fcd-0c9985c06878
[2025-06-25 13:47:18,438] WARNING - __main__ - main.py:6271 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 13:47:18,439] WARNING - __main__ - main.py:6272 - [3681be1a-4526-4aa1-9484-48c813cc54cb] ✅   - Interaction logged successfully
[2025-06-25 13:47:18,439] INFO - __main__ - main.py:11315 - [3681be1a-4526-4aa1-9484-48c813cc54cb] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 13:47:18,440] DEBUG - __main__ - main.py:2812 - [3681be1a-4526-4aa1-9484-48c813cc54cb] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 13:47:18,440] DEBUG - __main__ - main.py:6318 - [3681be1a-4526-4aa1-9484-48c813cc54cb] No final assessment data found in AI response
[2025-06-25 13:47:18,442] INFO - __main__ - main.py:6404 - [3681be1a-4526-4aa1-9484-48c813cc54cb] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 13:47:18,443] WARNING - __main__ - main.py:680 - High response time detected: 6.98s for enhance_content_api
[2025-06-25 13:47:18,444] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 13:47:18] "POST /api/enhance-content HTTP/1.1" 200 -
