[2025-06-25 14:33:27,385] INFO - __main__ - main.py:617 - Logging configuration complete with immediate console output
[2025-06-25 14:33:27,388] INFO - __main__ - main.py:693 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-25 14:33:27,390] INFO - __main__ - main.py:872 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-25 14:33:27,403] INFO - __main__ - main.py:901 - Phase transition fixes imported successfully
[2025-06-25 14:33:27,409] INFO - __main__ - main.py:3192 - Successfully imported utils functions
[2025-06-25 14:33:27,411] INFO - __main__ - main.py:3200 - Successfully imported extract_ai_state functions
[2025-06-25 14:33:27,421] INFO - __main__ - main.py:3650 - FLASK: Using unified Firebase initialization approach...
[2025-06-25 14:33:27,424] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-25 14:33:27,492] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-25 14:33:27,492] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-25 14:33:28,096] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-25 14:33:28,101] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-25 14:33:28,595] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-25 14:33:28,957] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-25 14:33:28,958] INFO - __main__ - main.py:3658 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-25 14:33:28,958] INFO - __main__ - main.py:3748 - Gemini API will be initialized on first use (lazy loading).
[2025-06-25 14:33:28,975] INFO - __main__ - main.py:1042 - Successfully imported timetable_generator functions
[2025-06-25 14:33:28,979] INFO - __main__ - main.py:16471 - Starting Lesson Manager Service...
[2025-06-25 14:33:28,979] INFO - __main__ - main.py:16477 - Flask server starting on host 0.0.0.0, port 5000
[2025-06-25 14:33:28,980] INFO - __main__ - main.py:16478 - Debug mode: ON
[2025-06-25 14:33:29,037] INFO - werkzeug - _internal.py:97 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
[2025-06-25 14:33:29,037] INFO - werkzeug - _internal.py:97 - [33mPress CTRL+C to quit[0m
[2025-06-25 14:33:51,162] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-25 14:33:51,163] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "02447db4-7227-4663-b3e7-cbeeedc2e534", "timestamp": "2025-06-25T13:33:51.163030+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "subject": "Artificial Intelligence", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-25 14:33:51,164] INFO - __main__ - main.py:12329 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3NzQ4NTAwMmYwNWJlMDI2N2VmNDU5ZjViNTEzNTMzYjVjNThjMTIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cdslNLnuIPhmhAqwhyoDjl8PW5QJLgPhUdMRKWEdbVYZQh3wiz_ECGE-_MYTMxFEm_IcVTN8PPiJDEyt71h8BqTRXs8hoyo-EvV7ZC5MNn6ME2lwno1fhVIT4o4nqKDRv8_BVKmBBmJ-L5d1IwJ6F10eTE5tF-t1-jQ6xbt2qsAQM7PzlaB0XLupGjhTEviDJjxSgPXMonCNQM2dqrzVLSIcXpvl3LPSrJy5TEWIu7mVtFbTqijnoEdGirj7krg5z_M1vPRfCHBSC3NFyAtxoIOZU6MxD4YLKBkJnerb2MJVqhOASc9lZCokhNIhXWYevGlpnIKrQcWylmYaeZFfBQ', 'User-Agent': 'axios/1.9.0', 'Content-Length': '225', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-25 14:33:51,165] INFO - auth_decorator - auth_decorator.py:39 - [02447db4-7227-4663-b3e7-cbeeedc2e534][require_auth] Decorator invoked for path: /lesson-content
[2025-06-25 14:33:51,167] INFO - auth_decorator - auth_decorator.py:56 - [02447db4-7227-4663-b3e7-cbeeedc2e534][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:33:51,167] WARNING - __main__ - main.py:12336 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-25 14:33:51,168] INFO - __main__ - main.py:12343 - [02447db4-7227-4663-b3e7-cbeeedc2e534] lesson_content_and_quiz invoked by /lesson-content
[2025-06-25 14:33:51,169] INFO - __main__ - main.py:12359 - Lesson content request by authenticated user: dev_student_uid_123 (Development Student)
[2025-06-25 14:33:51,169] INFO - __main__ - main.py:12363 - !!! [lesson_content_and_quiz] [02447db4-7227-4663-b3e7-cbeeedc2e534] RAW BODY AT START (bytes length: 225): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-002","subject":"Artificial Intelligence","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-25 14:33:51,170] INFO - __main__ - main.py:1177 - [02447db4-7227-4663-b3e7-cbeeedc2e534] fetch_lesson_data: Fetching lesson with parameters:
[2025-06-25 14:33:51,170] INFO - __main__ - main.py:1178 -   • Country: Nigeria
[2025-06-25 14:33:51,171] INFO - __main__ - main.py:1179 -   • Curriculum: National Curriculum
[2025-06-25 14:33:51,171] INFO - __main__ - main.py:1180 -   • Grade: Primary 5
[2025-06-25 14:33:51,172] INFO - __main__ - main.py:1181 -   • Level: P5
[2025-06-25 14:33:51,172] INFO - __main__ - main.py:1182 -   • Subject: Artificial Intelligence
[2025-06-25 14:33:51,173] INFO - __main__ - main.py:1183 -   • Lesson ID: P5-AI-002
[2025-06-25 14:33:51,173] INFO - __main__ - main.py:1202 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/P5/subjects/Artificial Intelligence/lessonRef/P5-AI-002
[2025-06-25 14:33:51,510] INFO - __main__ - main.py:1266 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Successfully retrieved document with keys: ['additionalNotes', 'content', 'subject', 'existingAssessments', 'id', 'instructionalSteps', 'lessonTimeLength', 'learningObjectives', 'metadata', 'digitalMaterials', 'quizzes', 'gradeLevel', 'lessonRef', 'lessonTitle', 'topic', 'country', 'extensionActivities', 'theme', 'adaptiveStrategies', 'conclusion', 'curriculumType', 'quizzesAndAssessments', 'introduction']
[2025-06-25 14:33:51,510] INFO - __main__ - main.py:1417 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Extracted 10 key concepts: ['Explore', 'chatbots', 'voice', 'assistants', 'work']...
[2025-06-25 14:33:51,511] INFO - __main__ - main.py:1497 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Universal content extraction: 1122 characters from 4 steps
[2025-06-25 14:33:51,511] INFO - __main__ - main.py:1534 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Universal conversion: 4 steps → 4 sections
[2025-06-25 14:33:51,512] INFO - __main__ - main.py:1352 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Field mapping completed:
[2025-06-25 14:33:51,512] INFO - __main__ - main.py:1353 -   - Subject: Artificial Intelligence
[2025-06-25 14:33:51,512] INFO - __main__ - main.py:1354 -   - Topic: Talking to AI
[2025-06-25 14:33:51,513] INFO - __main__ - main.py:1355 -   - Grade: Primary 5
[2025-06-25 14:33:51,513] INFO - __main__ - main.py:1356 -   - Key Concepts: 10 extracted
[2025-06-25 14:33:51,513] INFO - __main__ - main.py:1357 -   - Instructional Steps: 4
[2025-06-25 14:33:51,514] INFO - __main__ - main.py:1555 - [02447db4-7227-4663-b3e7-cbeeedc2e534] ✅ Universal content structure recognized: instructionalSteps (4 steps)
[2025-06-25 14:33:51,514] INFO - __main__ - main.py:1570 - [02447db4-7227-4663-b3e7-cbeeedc2e534] ✅ All required fields present after universal mapping
[2025-06-25 14:33:51,515] INFO - __main__ - main.py:1275 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Successfully mapped lesson fields for AI inference
[2025-06-25 14:33:51,515] DEBUG - __main__ - main.py:657 - Cached result for fetch_lesson_data
[2025-06-25 14:33:51,515] INFO - __main__ - main.py:12456 - [02447db4-7227-4663-b3e7-cbeeedc2e534] ✅ USING existing 2 learning objectives
[2025-06-25 14:33:51,516] INFO - __main__ - main.py:12457 - [02447db4-7227-4663-b3e7-cbeeedc2e534] 🎯 EXISTING OBJECTIVES: ['Explore how chatbots and voice assistants work.', 'Discuss AI’s role in breaking language barriers.']
[2025-06-25 14:33:51,987] INFO - __main__ - main.py:12463 - [02447db4-7227-4663-b3e7-cbeeedc2e534] 💾 SAVED learning objectives to Firestore
[2025-06-25 14:33:51,989] INFO - __main__ - main.py:10879 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Attempting to initialize lesson session for student_id: dev_student_uid_123
[2025-06-25 14:33:51,990] DEBUG - __main__ - main.py:10880 - [02447db4-7227-4663-b3e7-cbeeedc2e534] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'existingAssessments', 'id', 'metadata', 'digitalMaterials', 'gradeLevel', 'country', 'extensionActivities', 'theme', 'curriculumType', 'quizzesAndAssessments', 'curriculum', 'level']
[2025-06-25 14:33:51,991] INFO - __main__ - main.py:10919 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Using student name: 'Development Student' for session session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:33:51,993] INFO - __main__ - main.py:1592 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Constructing initial lesson state data for Firestore session: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:33:51,994] INFO - __main__ - main.py:1642 - [02447db4-7227-4663-b3e7-cbeeedc2e534] ✅ Constructed initial state dictionary for session session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c. Phase: diagnostic_start_probe
[2025-06-25 14:33:51,995] INFO - __main__ - main.py:1643 - [02447db4-7227-4663-b3e7-cbeeedc2e534] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-25 14:33:51,997] INFO - __main__ - main.py:10972 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Preparing to create 'lesson_sessions' doc 'session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c'.
[2025-06-25 14:33:51,998] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 14:33:51,999] DEBUG - __main__ - main.py:10973 - [02447db4-7227-4663-b3e7-cbeeedc2e534] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c', 'student_id': 'dev_student_uid_123', 'lessonRef': 'P5-AI-002', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Development Student', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'Talking to AI!', 'topic': 'Talking to AI', 'learningObjectives': ['Explore how chatbots and voice assistants work.', 'Discuss AI’s role in breaking language barriers.'], 'key_concepts': ['Explore', 'chatbots', 'voice', 'assistants', 'work', 'Discuss', 'role', 'breaking', 'language', 'barriers'], 'metadata': {'blooms_level': ['Understanding', 'Applying', 'Analyzing'], 'context': 'Introduction', 'apiConnections': ["Gemini: Dynamically determines Bloom's Taxonomy level based on lesson content"], 'skills': ['Language', 'Interaction'], 'difficulty': 'medium', 'taxonomy_alignment': "Aligned to Bloom's Taxonomy by promoting understanding of AI communication tools, application through experimentation, and analysis of the impact on language barriers."}}
[2025-06-25 14:33:52,001] INFO - __main__ - main.py:10975 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Preparing to create 'lesson_states' doc 'session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c'.
[2025-06-25 14:33:52,002] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 14:33:52,002] DEBUG - __main__ - main.py:10976 - [02447db4-7227-4663-b3e7-cbeeedc2e534] 'lesson_states' data: {'session_id': 'session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c', 'student_id': 'dev_student_uid_123', 'student_name': 'Development Student', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-AI-002', 'subject': 'Artificial Intelligence', 'grade': 'Primary 5', 'topic': 'Talking to AI', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Explore, chatbots, voice, assistants, work, Discuss, role, breaking, language, barriers', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-25 14:33:52,420] INFO - __main__ - main.py:10986 - [02447db4-7227-4663-b3e7-cbeeedc2e534] Successfully created Firestore docs for session 'session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c', student 'dev_student_uid_123'
[2025-06-25 14:33:52,424] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:33:52] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-25 14:33:52,769] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 14:33:52,770] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "66eae6a0-2f3e-4934-b5e2-27c23e7fac91", "timestamp": "2025-06-25T13:33:52.770047+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c", "chat_history": []}}
[2025-06-25 14:33:52,771] INFO - auth_decorator - auth_decorator.py:39 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 14:33:52,772] INFO - auth_decorator - auth_decorator.py:56 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:33:52,774] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 14:33:52,777] WARNING - __main__ - main.py:5262 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 14:33:52,779] INFO - __main__ - main.py:5315 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Parsed Params: student_id='andrea_ugono_33305', session_id='session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c', lesson_ref='P5-AI-002'
[2025-06-25 14:33:53,116] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 14:33:53,118] INFO - __main__ - main.py:5361 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:33:53,120] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:33:53,122] INFO - __main__ - main.py:5380 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 14:33:53,123] INFO - __main__ - main.py:5419 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-25 14:33:53,124] INFO - __main__ - main.py:2332 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'Talking to AI!'.
[2025-06-25 14:33:53,126] INFO - __main__ - main.py:3739 - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
[2025-06-25 14:33:53,601] INFO - __main__ - main.py:2391 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-25 14:33:53,602] INFO - __main__ - main.py:2391 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-25 14:33:53,604] INFO - __main__ - main.py:2391 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-25 14:33:53,605] INFO - __main__ - main.py:2391 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-25 14:33:53,606] INFO - __main__ - main.py:2391 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-25 14:33:53,608] INFO - __main__ - main.py:2460 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-25 14:33:53,610] DEBUG - __main__ - main.py:2474 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-25 14:33:53,611] DEBUG - __main__ - main.py:2477 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ev...
[2025-06-25 14:33:53,612] DEBUG - __main__ - main.py:2478 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference Lesson Summary (first 300 chars): Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ever...
[2025-06-25 14:33:53,614] DEBUG - __main__ - main.py:2479 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-25 14:33:53,615] INFO - __main__ - main.py:2483 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Calling Gemini API for module inference...
[2025-06-25 14:33:54,916] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-25 14:33:54,917] INFO - __main__ - main.py:2493 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Gemini API call completed in 1.30s. Raw response: 'ai_tools_and_applications'
[2025-06-25 14:33:54,917] DEBUG - __main__ - main.py:2515 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-25 14:33:54,918] INFO - __main__ - main.py:2520 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-25 14:33:54,919] INFO - __main__ - main.py:5452 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-25 14:33:54,920] INFO - __main__ - main.py:2569 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] CACHE MISS or fetch: Getting GS levels for subject 'artificial_intelligence', module 'ai_tools_and_applications'.
[2025-06-25 14:33:55,270] INFO - __main__ - main.py:2592 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Fetched metadata for module: 'AI Tools & Applications'
[2025-06-25 14:33:55,806] INFO - __main__ - main.py:2624 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Successfully fetched 10 levels for module 'ai_tools_and_applications'.
[2025-06-25 14:33:55,807] INFO - __main__ - main.py:5478 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-25 14:33:56,469] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-25 14:33:56,868] WARNING - __main__ - main.py:5502 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍 SESSION STATE DEBUG:
[2025-06-25 14:33:56,869] WARNING - __main__ - main.py:5503 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   - Session exists: True
[2025-06-25 14:33:56,871] WARNING - __main__ - main.py:5504 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 14:33:56,872] WARNING - __main__ - main.py:5505 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'current_phase', 'diagnostic_completed_this_session', 'student_name', 'lesson_context_snapshot', 'current_probing_level_number', 'levels_probed_and_failed', 'assigned_level_for_teaching', 'current_lesson_phase', 'current_question_index', 'student_id', 'last_modified', 'student_answers_for_probing_level']
[2025-06-25 14:33:56,875] WARNING - __main__ - main.py:5525 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 14:33:56,876] INFO - __main__ - main.py:5561 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] State protection not triggered
[2025-06-25 14:33:56,877] INFO - __main__ - main.py:5596 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 14:33:56,878] INFO - __main__ - main.py:5597 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   assigned_level_for_teaching (session): None
[2025-06-25 14:33:56,879] INFO - __main__ - main.py:5598 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   latest_assessed_level (profile): None
[2025-06-25 14:33:56,880] INFO - __main__ - main.py:5599 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   teaching_level_for_returning_student: None
[2025-06-25 14:33:56,881] INFO - __main__ - main.py:5600 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   has_completed_diagnostic_before: False
[2025-06-25 14:33:56,882] INFO - __main__ - main.py:5601 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   is_first_encounter_for_module: True
[2025-06-25 14:33:56,883] WARNING - __main__ - main.py:5606 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 14:33:56,885] INFO - __main__ - main.py:5612 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍 PHASE INVESTIGATION:
[2025-06-25 14:33:56,886] INFO - __main__ - main.py:5613 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:33:56,887] INFO - __main__ - main.py:5614 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 14:33:56,888] INFO - __main__ - main.py:5615 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Is first encounter: True
[2025-06-25 14:33:56,889] INFO - __main__ - main.py:5616 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Diagnostic completed: False
[2025-06-25 14:33:56,889] INFO - __main__ - main.py:5622 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:33:56,890] INFO - __main__ - main.py:5636 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 14:33:56,891] INFO - __main__ - main.py:5638 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 14:33:56,891] INFO - __main__ - main.py:5658 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-25 14:33:56,892] INFO - __main__ - main.py:3813 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Diagnostic context validation passed
[2025-06-25 14:33:56,892] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 14:33:56,893] WARNING - __main__ - main.py:5746 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 14:33:56,894] INFO - __main__ - main.py:3923 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Enhanced diagnostic context with 45 fields
[2025-06-25 14:33:56,894] INFO - __main__ - main.py:5765 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 14:33:56,895] INFO - __main__ - main.py:5778 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 14:33:56,895] DEBUG - __main__ - main.py:5779 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 14:33:56,896] WARNING - __main__ - main.py:5980 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 14:33:56,896] WARNING - __main__ - main.py:5981 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 14:33:56,897] WARNING - __main__ - main.py:5982 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖   - Student query: Start diagnostic assessment...
[2025-06-25 14:33:56,898] INFO - __main__ - main.py:10405 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 👤 STUDENT NAME DEBUG:
[2025-06-25 14:33:56,898] INFO - __main__ - main.py:10406 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:33:56,898] INFO - __main__ - main.py:10407 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 👤   context.student_name: NOT_FOUND
[2025-06-25 14:33:56,899] INFO - __main__ - main.py:10408 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 👤   final student_name: Andrea
[2025-06-25 14:33:56,900] INFO - __main__ - main.py:10488 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖 Generating natural AI response for Andrea...
[2025-06-25 14:33:57,565] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 379 characters
[2025-06-25 14:33:57,566] INFO - __main__ - main.py:10509 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 14:33:57,568] INFO - __main__ - main.py:10540 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ Generated natural response for Andrea: 379 chars
[2025-06-25 14:33:57,569] INFO - __main__ - main.py:10541 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:33:57,571] WARNING - __main__ - main.py:6004 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 14:33:57,572] WARNING - __main__ - main.py:6005 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖   - Content length: 379 chars
[2025-06-25 14:33:57,574] WARNING - __main__ - main.py:6006 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:33:57,576] WARNING - __main__ - main.py:6007 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🤖   - Raw state block: None...
[2025-06-25 14:33:57,579] INFO - __main__ - main.py:6032 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 14:33:57,580] INFO - __main__ - main.py:6033 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 14:33:57,885] INFO - __main__ - main.py:6082 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI processing completed in 0.99s
[2025-06-25 14:33:57,887] WARNING - __main__ - main.py:6093 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 14:33:57,888] INFO - __main__ - main.py:4018 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:33:57,889] WARNING - __main__ - main.py:6102 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 14:33:57,890] WARNING - __main__ - main.py:6109 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 14:33:57,891] WARNING - __main__ - main.py:6116 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 14:33:57,892] WARNING - __main__ - main.py:6117 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 14:33:57,893] WARNING - __main__ - main.py:6118 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 14:33:57,894] WARNING - __main__ - main.py:6119 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:33:57,896] WARNING - __main__ - main.py:6120 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:33:57,897] WARNING - __main__ - main.py:6123 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 💾 FINAL STATE APPLICATION:
[2025-06-25 14:33:57,899] WARNING - __main__ - main.py:6124 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 14:33:57,900] WARNING - __main__ - main.py:6125 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:33:57,902] WARNING - __main__ - main.py:6126 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:33:57,903] WARNING - __main__ - main.py:6127 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 💾   - Phase change: False
[2025-06-25 14:33:57,904] INFO - __main__ - main.py:4050 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 14:33:57,905] INFO - __main__ - main.py:4051 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 14:33:57,906] INFO - __main__ - main.py:4052 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Current level: 5
[2025-06-25 14:33:57,908] INFO - __main__ - main.py:4053 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Question index: 0
[2025-06-25 14:33:57,908] INFO - __main__ - main.py:4054 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   First encounter: True
[2025-06-25 14:33:57,909] INFO - __main__ - main.py:4059 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Answers collected: 0
[2025-06-25 14:33:57,910] INFO - __main__ - main.py:4060 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Levels failed: 0
[2025-06-25 14:33:57,910] INFO - __main__ - main.py:4018 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:33:57,911] INFO - __main__ - main.py:4064 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   State update valid: True
[2025-06-25 14:33:57,911] INFO - __main__ - main.py:4071 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91]   Diagnostic complete: False
[2025-06-25 14:33:57,912] WARNING - __main__ - main.py:6140 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 14:33:57,913] INFO - __main__ - main.py:6149 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 14:33:57,913] INFO - __main__ - main.py:6150 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 14:33:58,402] WARNING - __main__ - main.py:6195 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 14:33:58,403] WARNING - __main__ - main.py:6196 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Phase: diagnostic_start_probe
[2025-06-25 14:33:58,403] WARNING - __main__ - main.py:6197 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Probing Level: 5
[2025-06-25 14:33:58,404] WARNING - __main__ - main.py:6198 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Question Index: 1
[2025-06-25 14:33:58,404] WARNING - __main__ - main.py:6199 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Diagnostic Complete: False
[2025-06-25 14:33:58,405] WARNING - __main__ - main.py:6206 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Quiz Questions Saved: 0
[2025-06-25 14:33:58,406] WARNING - __main__ - main.py:6207 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Quiz Answers Saved: 0
[2025-06-25 14:33:58,406] WARNING - __main__ - main.py:6208 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Quiz Started: False
[2025-06-25 14:33:59,250] INFO - __main__ - main.py:6268 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ Updated existing session document: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:33:59,252] WARNING - __main__ - main.py:6269 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 14:33:59,253] WARNING - __main__ - main.py:6270 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Session ID: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:33:59,255] WARNING - __main__ - main.py:6271 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:33:59,256] WARNING - __main__ - main.py:6272 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] ✅   - Interaction logged successfully
[2025-06-25 14:33:59,258] INFO - __main__ - main.py:11317 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 14:33:59,260] DEBUG - __main__ - main.py:2812 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 14:33:59,260] DEBUG - __main__ - main.py:6318 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] No final assessment data found in AI response
[2025-06-25 14:33:59,264] INFO - __main__ - main.py:6404 - [66eae6a0-2f3e-4934-b5e2-27c23e7fac91] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 14:33:59,266] WARNING - __main__ - main.py:680 - High response time detected: 6.49s for enhance_content_api
[2025-06-25 14:33:59,267] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:33:59] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 14:34:55,040] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 14:34:55,041] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "a427b4ef-c94b-442a-a475-0174b046da50", "timestamp": "2025-06-25T13:34:55.040963+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "content_to_enhance": "It means talking to a computer that can understand me and talk back, like asking it questions and it giving me answers.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c", "chat_history": [{"role": "assistant", "content": "Hi Andrea! It's great to have you here to learn about talking to AI. I'm excited to get started!\n\nSo, you're ready for a little diagnostic assessment? That's a fantastic way to see what we already know! Let's jump right in.\n\nTo begin, Andrea, can you tell me in your own words, what do you think it means when we say we are \"talking to AI\"? What comes to mind when you hear that?", "timestamp": "2025-06-25T13:33:59.280Z"}]}}
[2025-06-25 14:34:55,042] INFO - auth_decorator - auth_decorator.py:39 - [a427b4ef-c94b-442a-a475-0174b046da50][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 14:34:55,043] INFO - auth_decorator - auth_decorator.py:56 - [a427b4ef-c94b-442a-a475-0174b046da50][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:34:55,045] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 14:34:55,049] WARNING - __main__ - main.py:5262 - [a427b4ef-c94b-442a-a475-0174b046da50] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 14:34:55,052] INFO - __main__ - main.py:5315 - [a427b4ef-c94b-442a-a475-0174b046da50] Parsed Params: student_id='andrea_ugono_33305', session_id='session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c', lesson_ref='P5-AI-002'
[2025-06-25 14:34:55,419] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 14:34:55,420] INFO - __main__ - main.py:5361 - [a427b4ef-c94b-442a-a475-0174b046da50] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:34:55,421] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:34:55,422] INFO - __main__ - main.py:5380 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 14:34:55,422] INFO - __main__ - main.py:5419 - [a427b4ef-c94b-442a-a475-0174b046da50] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-25 14:34:55,423] INFO - __main__ - main.py:2332 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'Talking to AI!'.
[2025-06-25 14:34:55,870] INFO - __main__ - main.py:2391 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-25 14:34:55,870] INFO - __main__ - main.py:2391 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-25 14:34:55,870] INFO - __main__ - main.py:2391 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-25 14:34:55,871] INFO - __main__ - main.py:2391 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-25 14:34:55,871] INFO - __main__ - main.py:2391 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-25 14:34:55,872] INFO - __main__ - main.py:2460 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-25 14:34:55,873] DEBUG - __main__ - main.py:2474 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-25 14:34:55,874] DEBUG - __main__ - main.py:2477 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ev...
[2025-06-25 14:34:55,875] DEBUG - __main__ - main.py:2478 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference Lesson Summary (first 300 chars): Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ever...
[2025-06-25 14:34:55,876] DEBUG - __main__ - main.py:2479 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-25 14:34:55,876] INFO - __main__ - main.py:2483 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Calling Gemini API for module inference...
[2025-06-25 14:34:56,297] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-25 14:34:56,297] INFO - __main__ - main.py:2493 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Gemini API call completed in 0.42s. Raw response: 'ai_tools_and_applications'
[2025-06-25 14:34:56,297] DEBUG - __main__ - main.py:2515 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-25 14:34:56,298] INFO - __main__ - main.py:2520 - [a427b4ef-c94b-442a-a475-0174b046da50] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-25 14:34:56,299] INFO - __main__ - main.py:5452 - [a427b4ef-c94b-442a-a475-0174b046da50] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-25 14:34:56,300] INFO - __main__ - main.py:5478 - [a427b4ef-c94b-442a-a475-0174b046da50] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-25 14:34:56,596] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-25 14:34:57,122] WARNING - __main__ - main.py:5502 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍 SESSION STATE DEBUG:
[2025-06-25 14:34:57,123] WARNING - __main__ - main.py:5503 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   - Session exists: True
[2025-06-25 14:34:57,123] WARNING - __main__ - main.py:5504 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 14:34:57,124] WARNING - __main__ - main.py:5505 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 14:34:57,126] WARNING - __main__ - main.py:5525 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 14:34:57,128] INFO - __main__ - main.py:5561 - [a427b4ef-c94b-442a-a475-0174b046da50] State protection not triggered
[2025-06-25 14:34:57,128] INFO - __main__ - main.py:5596 - [a427b4ef-c94b-442a-a475-0174b046da50] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 14:34:57,129] INFO - __main__ - main.py:5597 - [a427b4ef-c94b-442a-a475-0174b046da50]   assigned_level_for_teaching (session): None
[2025-06-25 14:34:57,129] INFO - __main__ - main.py:5598 - [a427b4ef-c94b-442a-a475-0174b046da50]   latest_assessed_level (profile): None
[2025-06-25 14:34:57,130] INFO - __main__ - main.py:5599 - [a427b4ef-c94b-442a-a475-0174b046da50]   teaching_level_for_returning_student: None
[2025-06-25 14:34:57,130] INFO - __main__ - main.py:5600 - [a427b4ef-c94b-442a-a475-0174b046da50]   has_completed_diagnostic_before: False
[2025-06-25 14:34:57,131] INFO - __main__ - main.py:5601 - [a427b4ef-c94b-442a-a475-0174b046da50]   is_first_encounter_for_module: True
[2025-06-25 14:34:57,131] WARNING - __main__ - main.py:5606 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 14:34:57,131] INFO - __main__ - main.py:5612 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍 PHASE INVESTIGATION:
[2025-06-25 14:34:57,132] INFO - __main__ - main.py:5613 - [a427b4ef-c94b-442a-a475-0174b046da50]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:34:57,132] INFO - __main__ - main.py:5614 - [a427b4ef-c94b-442a-a475-0174b046da50]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 14:34:57,133] INFO - __main__ - main.py:5615 - [a427b4ef-c94b-442a-a475-0174b046da50]   Is first encounter: True
[2025-06-25 14:34:57,133] INFO - __main__ - main.py:5616 - [a427b4ef-c94b-442a-a475-0174b046da50]   Diagnostic completed: False
[2025-06-25 14:34:57,134] INFO - __main__ - main.py:5622 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:34:57,135] INFO - __main__ - main.py:5636 - [a427b4ef-c94b-442a-a475-0174b046da50] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 14:34:57,135] INFO - __main__ - main.py:5638 - [a427b4ef-c94b-442a-a475-0174b046da50] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 14:34:57,136] INFO - __main__ - main.py:5658 - [a427b4ef-c94b-442a-a475-0174b046da50] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 14:34:57,137] INFO - __main__ - main.py:3813 - [a427b4ef-c94b-442a-a475-0174b046da50] Diagnostic context validation passed
[2025-06-25 14:34:57,137] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 14:34:57,137] WARNING - __main__ - main.py:5746 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 14:34:57,138] INFO - __main__ - main.py:3923 - [a427b4ef-c94b-442a-a475-0174b046da50] Enhanced diagnostic context with 45 fields
[2025-06-25 14:34:57,138] INFO - __main__ - main.py:5765 - [a427b4ef-c94b-442a-a475-0174b046da50] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 14:34:57,139] INFO - __main__ - main.py:5778 - [a427b4ef-c94b-442a-a475-0174b046da50] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 14:34:57,139] DEBUG - __main__ - main.py:5779 - [a427b4ef-c94b-442a-a475-0174b046da50] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 14:34:57,140] WARNING - __main__ - main.py:5980 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 14:34:57,140] WARNING - __main__ - main.py:5981 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 14:34:57,140] WARNING - __main__ - main.py:5982 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖   - Student query: It means talking to a computer that can understand me and talk back, like asking it questions and it...
[2025-06-25 14:34:57,141] INFO - __main__ - main.py:10405 - [a427b4ef-c94b-442a-a475-0174b046da50] 👤 STUDENT NAME DEBUG:
[2025-06-25 14:34:57,141] INFO - __main__ - main.py:10406 - [a427b4ef-c94b-442a-a475-0174b046da50] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:34:57,142] INFO - __main__ - main.py:10407 - [a427b4ef-c94b-442a-a475-0174b046da50] 👤   context.student_name: NOT_FOUND
[2025-06-25 14:34:57,142] INFO - __main__ - main.py:10408 - [a427b4ef-c94b-442a-a475-0174b046da50] 👤   final student_name: Andrea
[2025-06-25 14:34:57,143] INFO - __main__ - main.py:10488 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖 Generating natural AI response for Andrea...
[2025-06-25 14:34:57,841] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 394 characters
[2025-06-25 14:34:57,842] INFO - __main__ - main.py:10509 - [a427b4ef-c94b-442a-a475-0174b046da50] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 14:34:57,842] INFO - __main__ - main.py:10540 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ Generated natural response for Andrea: 394 chars
[2025-06-25 14:34:57,843] INFO - __main__ - main.py:10541 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:34:57,844] WARNING - __main__ - main.py:6004 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 14:34:57,844] WARNING - __main__ - main.py:6005 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖   - Content length: 394 chars
[2025-06-25 14:34:57,845] WARNING - __main__ - main.py:6006 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:34:57,846] WARNING - __main__ - main.py:6007 - [a427b4ef-c94b-442a-a475-0174b046da50] 🤖   - Raw state block: None...
[2025-06-25 14:34:57,847] INFO - __main__ - main.py:6032 - [a427b4ef-c94b-442a-a475-0174b046da50] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 14:34:57,847] INFO - __main__ - main.py:6033 - [a427b4ef-c94b-442a-a475-0174b046da50] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 14:34:58,134] INFO - __main__ - main.py:6082 - [a427b4ef-c94b-442a-a475-0174b046da50] AI processing completed in 0.99s
[2025-06-25 14:34:58,135] WARNING - __main__ - main.py:6093 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 14:34:58,136] INFO - __main__ - main.py:4018 - [a427b4ef-c94b-442a-a475-0174b046da50] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:34:58,137] WARNING - __main__ - main.py:6102 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 14:34:58,138] WARNING - __main__ - main.py:6109 - [a427b4ef-c94b-442a-a475-0174b046da50] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 14:34:58,138] WARNING - __main__ - main.py:6116 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 14:34:58,139] WARNING - __main__ - main.py:6117 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 14:34:58,140] WARNING - __main__ - main.py:6118 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 14:34:58,140] WARNING - __main__ - main.py:6119 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:34:58,141] WARNING - __main__ - main.py:6120 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:34:58,142] WARNING - __main__ - main.py:6123 - [a427b4ef-c94b-442a-a475-0174b046da50] 💾 FINAL STATE APPLICATION:
[2025-06-25 14:34:58,142] WARNING - __main__ - main.py:6124 - [a427b4ef-c94b-442a-a475-0174b046da50] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 14:34:58,142] WARNING - __main__ - main.py:6125 - [a427b4ef-c94b-442a-a475-0174b046da50] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:34:58,143] WARNING - __main__ - main.py:6126 - [a427b4ef-c94b-442a-a475-0174b046da50] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:34:58,143] WARNING - __main__ - main.py:6127 - [a427b4ef-c94b-442a-a475-0174b046da50] 💾   - Phase change: False
[2025-06-25 14:34:58,144] INFO - __main__ - main.py:4050 - [a427b4ef-c94b-442a-a475-0174b046da50] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 14:34:58,144] INFO - __main__ - main.py:4051 - [a427b4ef-c94b-442a-a475-0174b046da50]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 14:34:58,144] INFO - __main__ - main.py:4052 - [a427b4ef-c94b-442a-a475-0174b046da50]   Current level: 5
[2025-06-25 14:34:58,145] INFO - __main__ - main.py:4053 - [a427b4ef-c94b-442a-a475-0174b046da50]   Question index: 0
[2025-06-25 14:34:58,145] INFO - __main__ - main.py:4054 - [a427b4ef-c94b-442a-a475-0174b046da50]   First encounter: True
[2025-06-25 14:34:58,145] INFO - __main__ - main.py:4059 - [a427b4ef-c94b-442a-a475-0174b046da50]   Answers collected: 0
[2025-06-25 14:34:58,146] INFO - __main__ - main.py:4060 - [a427b4ef-c94b-442a-a475-0174b046da50]   Levels failed: 0
[2025-06-25 14:34:58,146] INFO - __main__ - main.py:4018 - [a427b4ef-c94b-442a-a475-0174b046da50] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:34:58,146] INFO - __main__ - main.py:4064 - [a427b4ef-c94b-442a-a475-0174b046da50]   State update valid: True
[2025-06-25 14:34:58,147] INFO - __main__ - main.py:4071 - [a427b4ef-c94b-442a-a475-0174b046da50]   Diagnostic complete: False
[2025-06-25 14:34:58,147] WARNING - __main__ - main.py:6140 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 14:34:58,148] INFO - __main__ - main.py:6149 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 14:34:58,148] INFO - __main__ - main.py:6150 - [a427b4ef-c94b-442a-a475-0174b046da50] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 14:34:58,814] WARNING - __main__ - main.py:6195 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 14:34:58,815] WARNING - __main__ - main.py:6196 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Phase: diagnostic_start_probe
[2025-06-25 14:34:58,815] WARNING - __main__ - main.py:6197 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Probing Level: 5
[2025-06-25 14:34:58,816] WARNING - __main__ - main.py:6198 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Question Index: 1
[2025-06-25 14:34:58,817] WARNING - __main__ - main.py:6199 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Diagnostic Complete: False
[2025-06-25 14:34:58,818] WARNING - __main__ - main.py:6206 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Quiz Questions Saved: 0
[2025-06-25 14:34:58,819] WARNING - __main__ - main.py:6207 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Quiz Answers Saved: 0
[2025-06-25 14:34:58,820] WARNING - __main__ - main.py:6208 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Quiz Started: False
[2025-06-25 14:34:59,691] INFO - __main__ - main.py:6268 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ Updated existing session document: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:34:59,691] WARNING - __main__ - main.py:6269 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 14:34:59,692] WARNING - __main__ - main.py:6270 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Session ID: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:34:59,692] WARNING - __main__ - main.py:6271 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:34:59,693] WARNING - __main__ - main.py:6272 - [a427b4ef-c94b-442a-a475-0174b046da50] ✅   - Interaction logged successfully
[2025-06-25 14:34:59,694] INFO - __main__ - main.py:11317 - [a427b4ef-c94b-442a-a475-0174b046da50] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 14:34:59,695] DEBUG - __main__ - main.py:2812 - [a427b4ef-c94b-442a-a475-0174b046da50] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 14:34:59,696] DEBUG - __main__ - main.py:6318 - [a427b4ef-c94b-442a-a475-0174b046da50] No final assessment data found in AI response
[2025-06-25 14:34:59,698] INFO - __main__ - main.py:6404 - [a427b4ef-c94b-442a-a475-0174b046da50] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 14:34:59,699] WARNING - __main__ - main.py:680 - High response time detected: 4.65s for enhance_content_api
[2025-06-25 14:34:59,700] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:34:59] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 14:35:52,582] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 14:35:52,586] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "f7194cf9-9704-4c55-880e-041f71b8c1b2", "timestamp": "2025-06-25T13:35:52.584105+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "content_to_enhance": "Ask it for information or to tell me a story.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c", "chat_history": [{"role": "assistant", "content": "Hi Andrea! It's great to have you here to learn about talking to AI. I'm excited to get started!\n\nSo, you're ready for a little diagnostic assessment? That's a fantastic way to see what we already know! Let's jump right in.\n\nTo begin, Andrea, can you tell me in your own words, what do you think it means when we say we are \"talking to AI\"? What comes to mind when you hear that?", "timestamp": "2025-06-25T13:33:59.280Z"}, {"role": "user", "content": "It means talking to a computer that can understand me and talk back, like asking it questions and it giving me answers.", "timestamp": "2025-06-25T13:34:54.936Z"}, {"role": "assistant", "content": "That's a brilliant way to put it, Andrea! You've got the main idea perfectly. When we talk about \"talking to AI,\" it really is like having a conversation with a computer that's been taught to understand our words and respond back, just like you said. It's pretty amazing, isn't it?\n\nSo, when you imagine talking to AI, Andrea, what kind of things do you think you could ask it or tell it to do?", "timestamp": "2025-06-25T13:34:59.713Z"}]}}
[2025-06-25 14:35:52,590] INFO - auth_decorator - auth_decorator.py:39 - [f7194cf9-9704-4c55-880e-041f71b8c1b2][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 14:35:52,592] INFO - auth_decorator - auth_decorator.py:56 - [f7194cf9-9704-4c55-880e-041f71b8c1b2][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:35:52,593] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 14:35:52,597] WARNING - __main__ - main.py:5262 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 14:35:52,598] INFO - __main__ - main.py:5315 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Parsed Params: student_id='andrea_ugono_33305', session_id='session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c', lesson_ref='P5-AI-002'
[2025-06-25 14:35:52,888] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 14:35:52,888] INFO - __main__ - main.py:5361 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:35:52,889] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:35:52,889] INFO - __main__ - main.py:5380 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 14:35:52,889] INFO - __main__ - main.py:5419 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-25 14:35:52,890] INFO - __main__ - main.py:2332 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'Talking to AI!'.
[2025-06-25 14:35:53,395] INFO - __main__ - main.py:2391 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-25 14:35:53,395] INFO - __main__ - main.py:2391 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-25 14:35:53,396] INFO - __main__ - main.py:2391 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-25 14:35:53,396] INFO - __main__ - main.py:2391 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-25 14:35:53,397] INFO - __main__ - main.py:2391 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-25 14:35:53,397] INFO - __main__ - main.py:2460 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-25 14:35:53,398] DEBUG - __main__ - main.py:2474 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-25 14:35:53,398] DEBUG - __main__ - main.py:2477 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ev...
[2025-06-25 14:35:53,399] DEBUG - __main__ - main.py:2478 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference Lesson Summary (first 300 chars): Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ever...
[2025-06-25 14:35:53,400] DEBUG - __main__ - main.py:2479 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-25 14:35:53,401] INFO - __main__ - main.py:2483 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Calling Gemini API for module inference...
[2025-06-25 14:35:53,792] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-25 14:35:53,793] INFO - __main__ - main.py:2493 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Gemini API call completed in 0.39s. Raw response: 'ai_tools_and_applications'
[2025-06-25 14:35:53,794] DEBUG - __main__ - main.py:2515 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-25 14:35:53,796] INFO - __main__ - main.py:2520 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-25 14:35:53,797] INFO - __main__ - main.py:5452 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-25 14:35:53,798] INFO - __main__ - main.py:5478 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-25 14:35:54,383] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-25 14:35:54,913] WARNING - __main__ - main.py:5502 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍 SESSION STATE DEBUG:
[2025-06-25 14:35:54,913] WARNING - __main__ - main.py:5503 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   - Session exists: True
[2025-06-25 14:35:54,913] WARNING - __main__ - main.py:5504 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 14:35:54,914] WARNING - __main__ - main.py:5505 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 14:35:54,915] WARNING - __main__ - main.py:5525 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 14:35:54,916] INFO - __main__ - main.py:5561 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] State protection not triggered
[2025-06-25 14:35:54,917] INFO - __main__ - main.py:5596 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 14:35:54,919] INFO - __main__ - main.py:5597 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   assigned_level_for_teaching (session): None
[2025-06-25 14:35:54,920] INFO - __main__ - main.py:5598 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   latest_assessed_level (profile): None
[2025-06-25 14:35:54,921] INFO - __main__ - main.py:5599 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   teaching_level_for_returning_student: None
[2025-06-25 14:35:54,922] INFO - __main__ - main.py:5600 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   has_completed_diagnostic_before: False
[2025-06-25 14:35:54,923] INFO - __main__ - main.py:5601 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   is_first_encounter_for_module: True
[2025-06-25 14:35:54,923] WARNING - __main__ - main.py:5606 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 14:35:54,923] INFO - __main__ - main.py:5612 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍 PHASE INVESTIGATION:
[2025-06-25 14:35:54,924] INFO - __main__ - main.py:5613 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:35:54,924] INFO - __main__ - main.py:5614 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 14:35:54,924] INFO - __main__ - main.py:5615 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Is first encounter: True
[2025-06-25 14:35:54,925] INFO - __main__ - main.py:5616 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Diagnostic completed: False
[2025-06-25 14:35:54,925] INFO - __main__ - main.py:5622 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:35:54,925] INFO - __main__ - main.py:5636 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 14:35:54,926] INFO - __main__ - main.py:5638 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 14:35:54,926] INFO - __main__ - main.py:5658 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 14:35:54,926] INFO - __main__ - main.py:3813 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Diagnostic context validation passed
[2025-06-25 14:35:54,926] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 14:35:54,927] WARNING - __main__ - main.py:5746 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 14:35:54,927] INFO - __main__ - main.py:3923 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Enhanced diagnostic context with 45 fields
[2025-06-25 14:35:54,927] INFO - __main__ - main.py:5765 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 14:35:54,928] INFO - __main__ - main.py:5778 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 14:35:54,928] DEBUG - __main__ - main.py:5779 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 14:35:54,928] WARNING - __main__ - main.py:5980 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 14:35:54,929] WARNING - __main__ - main.py:5981 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 14:35:54,929] WARNING - __main__ - main.py:5982 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖   - Student query: Ask it for information or to tell me a story....
[2025-06-25 14:35:54,930] INFO - __main__ - main.py:10405 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 👤 STUDENT NAME DEBUG:
[2025-06-25 14:35:54,931] INFO - __main__ - main.py:10406 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:35:54,931] INFO - __main__ - main.py:10407 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 👤   context.student_name: NOT_FOUND
[2025-06-25 14:35:54,931] INFO - __main__ - main.py:10408 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 👤   final student_name: Andrea
[2025-06-25 14:35:54,932] INFO - __main__ - main.py:10488 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖 Generating natural AI response for Andrea...
[2025-06-25 14:35:55,691] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 493 characters
[2025-06-25 14:35:55,691] INFO - __main__ - main.py:10509 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 14:35:55,692] INFO - __main__ - main.py:10540 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ Generated natural response for Andrea: 493 chars
[2025-06-25 14:35:55,693] INFO - __main__ - main.py:10541 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:35:55,693] WARNING - __main__ - main.py:6004 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 14:35:55,694] WARNING - __main__ - main.py:6005 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖   - Content length: 493 chars
[2025-06-25 14:35:55,695] WARNING - __main__ - main.py:6006 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:35:55,696] WARNING - __main__ - main.py:6007 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🤖   - Raw state block: None...
[2025-06-25 14:35:55,697] INFO - __main__ - main.py:6032 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 14:35:55,698] INFO - __main__ - main.py:6033 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 14:35:56,488] INFO - __main__ - main.py:6082 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI processing completed in 1.56s
[2025-06-25 14:35:56,489] WARNING - __main__ - main.py:6093 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 14:35:56,489] INFO - __main__ - main.py:4018 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:35:56,490] WARNING - __main__ - main.py:6102 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 14:35:56,490] WARNING - __main__ - main.py:6109 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 14:35:56,491] WARNING - __main__ - main.py:6116 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 14:35:56,491] WARNING - __main__ - main.py:6117 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 14:35:56,492] WARNING - __main__ - main.py:6118 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 14:35:56,492] WARNING - __main__ - main.py:6119 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:35:56,493] WARNING - __main__ - main.py:6120 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:35:56,494] WARNING - __main__ - main.py:6123 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 💾 FINAL STATE APPLICATION:
[2025-06-25 14:35:56,495] WARNING - __main__ - main.py:6124 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 14:35:56,495] WARNING - __main__ - main.py:6125 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:35:56,496] WARNING - __main__ - main.py:6126 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:35:56,496] WARNING - __main__ - main.py:6127 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 💾   - Phase change: False
[2025-06-25 14:35:56,497] INFO - __main__ - main.py:4050 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 14:35:56,497] INFO - __main__ - main.py:4051 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 14:35:56,497] INFO - __main__ - main.py:4052 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Current level: 5
[2025-06-25 14:35:56,497] INFO - __main__ - main.py:4053 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Question index: 0
[2025-06-25 14:35:56,498] INFO - __main__ - main.py:4054 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   First encounter: True
[2025-06-25 14:35:56,498] INFO - __main__ - main.py:4059 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Answers collected: 0
[2025-06-25 14:35:56,498] INFO - __main__ - main.py:4060 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Levels failed: 0
[2025-06-25 14:35:56,498] INFO - __main__ - main.py:4018 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:35:56,499] INFO - __main__ - main.py:4064 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   State update valid: True
[2025-06-25 14:35:56,499] INFO - __main__ - main.py:4071 - [f7194cf9-9704-4c55-880e-041f71b8c1b2]   Diagnostic complete: False
[2025-06-25 14:35:56,499] WARNING - __main__ - main.py:6140 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 14:35:56,500] INFO - __main__ - main.py:6149 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 14:35:56,500] INFO - __main__ - main.py:6150 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 14:35:57,036] WARNING - __main__ - main.py:6195 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 14:35:57,036] WARNING - __main__ - main.py:6196 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Phase: diagnostic_start_probe
[2025-06-25 14:35:57,037] WARNING - __main__ - main.py:6197 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Probing Level: 5
[2025-06-25 14:35:57,037] WARNING - __main__ - main.py:6198 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Question Index: 1
[2025-06-25 14:35:57,038] WARNING - __main__ - main.py:6199 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Diagnostic Complete: False
[2025-06-25 14:35:57,038] WARNING - __main__ - main.py:6206 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Quiz Questions Saved: 0
[2025-06-25 14:35:57,039] WARNING - __main__ - main.py:6207 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Quiz Answers Saved: 0
[2025-06-25 14:35:57,040] WARNING - __main__ - main.py:6208 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Quiz Started: False
[2025-06-25 14:35:58,195] INFO - __main__ - main.py:6268 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ Updated existing session document: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:35:58,196] WARNING - __main__ - main.py:6269 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 14:35:58,196] WARNING - __main__ - main.py:6270 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Session ID: session_684b806a-6ac8-4d3c-b9db-4e3a47a4420c
[2025-06-25 14:35:58,197] WARNING - __main__ - main.py:6271 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:35:58,197] WARNING - __main__ - main.py:6272 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] ✅   - Interaction logged successfully
[2025-06-25 14:35:58,198] INFO - __main__ - main.py:11317 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 14:35:58,199] DEBUG - __main__ - main.py:2812 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 14:35:58,199] DEBUG - __main__ - main.py:6318 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] No final assessment data found in AI response
[2025-06-25 14:35:58,203] INFO - __main__ - main.py:6404 - [f7194cf9-9704-4c55-880e-041f71b8c1b2] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 14:35:58,204] WARNING - __main__ - main.py:680 - High response time detected: 5.61s for enhance_content_api
[2025-06-25 14:35:58,205] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:35:58] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 14:38:42,177] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-25 14:38:42,197] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "ad7b5d57-2afb-436e-aa75-f46e4a00ac01", "timestamp": "2025-06-25T13:38:42.184252+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "subject": "Artificial Intelligence", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-25 14:38:42,199] INFO - __main__ - main.py:12329 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3NzQ4NTAwMmYwNWJlMDI2N2VmNDU5ZjViNTEzNTMzYjVjNThjMTIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2MozTrD9XexSbzAK8bVr2unLHgyYDAS-CCA7NBN5lloss3eKb9KY9OzTFfKkYEvTvszM1mlWMMnJAUS-J_RHNS0c2mZxWNHIj4LtQb3jj76X69Buu_yAS2jTUEPI6T75zF3SF7aw8fv0Ob-tnF9r4OUqS9XOqS5Wb3oawp08vw2Df42SN4VU9HU9QvCPvYHBMdjRoo9d-rzD23VWETHA841TrCwEkicYGabhyrui_BHp9CD8UOv91XTgpjaU3gGqkPMERu3Y6kxBWf2BxdUZsodHC9bRu9ID8vvNKcCaB3hWd2ltzEaBc4HCKsxOyP6-M_-yV9d3pFaHpoRcHwY14w', 'User-Agent': 'axios/1.9.0', 'Content-Length': '225', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-25 14:38:42,202] INFO - auth_decorator - auth_decorator.py:39 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01][require_auth] Decorator invoked for path: /lesson-content
[2025-06-25 14:38:42,205] INFO - auth_decorator - auth_decorator.py:56 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:38:42,208] WARNING - __main__ - main.py:12336 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-25 14:38:42,209] INFO - __main__ - main.py:12343 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] lesson_content_and_quiz invoked by /lesson-content
[2025-06-25 14:38:42,209] INFO - __main__ - main.py:12359 - Lesson content request by authenticated user: dev_student_uid_123 (Development Student)
[2025-06-25 14:38:42,210] INFO - __main__ - main.py:12363 - !!! [lesson_content_and_quiz] [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] RAW BODY AT START (bytes length: 225): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-002","subject":"Artificial Intelligence","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-25 14:38:42,219] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:38:42,225] INFO - __main__ - main.py:12456 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] ✅ USING existing 2 learning objectives
[2025-06-25 14:38:42,231] INFO - __main__ - main.py:12457 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] 🎯 EXISTING OBJECTIVES: ['Explore how chatbots and voice assistants work.', 'Discuss AI’s role in breaking language barriers.']
[2025-06-25 14:38:43,188] INFO - __main__ - main.py:12463 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] 💾 SAVED learning objectives to Firestore
[2025-06-25 14:38:43,189] INFO - __main__ - main.py:10879 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] Attempting to initialize lesson session for student_id: dev_student_uid_123
[2025-06-25 14:38:43,191] DEBUG - __main__ - main.py:10880 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'existingAssessments', 'id', 'metadata', 'digitalMaterials', 'gradeLevel', 'country', 'extensionActivities', 'theme', 'curriculumType', 'quizzesAndAssessments', 'curriculum', 'level']
[2025-06-25 14:38:43,195] INFO - __main__ - main.py:10919 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] Using student name: 'Development Student' for session session_f8bf89bd-3588-4b93-b865-a489f1d6e596
[2025-06-25 14:38:43,197] INFO - __main__ - main.py:1592 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] Constructing initial lesson state data for Firestore session: session_f8bf89bd-3588-4b93-b865-a489f1d6e596
[2025-06-25 14:38:43,198] INFO - __main__ - main.py:1642 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] ✅ Constructed initial state dictionary for session session_f8bf89bd-3588-4b93-b865-a489f1d6e596. Phase: diagnostic_start_probe
[2025-06-25 14:38:43,199] INFO - __main__ - main.py:1643 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-25 14:38:43,199] INFO - __main__ - main.py:10972 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] Preparing to create 'lesson_sessions' doc 'session_f8bf89bd-3588-4b93-b865-a489f1d6e596'.
[2025-06-25 14:38:43,202] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 14:38:43,205] DEBUG - __main__ - main.py:10973 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_f8bf89bd-3588-4b93-b865-a489f1d6e596', 'student_id': 'dev_student_uid_123', 'lessonRef': 'P5-AI-002', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Development Student', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'Talking to AI!', 'topic': 'Talking to AI', 'learningObjectives': ['Explore how chatbots and voice assistants work.', 'Discuss AI’s role in breaking language barriers.'], 'key_concepts': ['Explore', 'chatbots', 'voice', 'assistants', 'work', 'Discuss', 'role', 'breaking', 'language', 'barriers'], 'metadata': {'blooms_level': ['Understanding', 'Applying', 'Analyzing'], 'context': 'Introduction', 'apiConnections': ["Gemini: Dynamically determines Bloom's Taxonomy level based on lesson content"], 'skills': ['Language', 'Interaction'], 'difficulty': 'medium', 'taxonomy_alignment': "Aligned to Bloom's Taxonomy by promoting understanding of AI communication tools, application through experimentation, and analysis of the impact on language barriers."}}
[2025-06-25 14:38:43,209] INFO - __main__ - main.py:10975 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] Preparing to create 'lesson_states' doc 'session_f8bf89bd-3588-4b93-b865-a489f1d6e596'.
[2025-06-25 14:38:43,211] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 14:38:43,213] DEBUG - __main__ - main.py:10976 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] 'lesson_states' data: {'session_id': 'session_f8bf89bd-3588-4b93-b865-a489f1d6e596', 'student_id': 'dev_student_uid_123', 'student_name': 'Development Student', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-AI-002', 'subject': 'Artificial Intelligence', 'grade': 'Primary 5', 'topic': 'Talking to AI', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Explore, chatbots, voice, assistants, work, Discuss, role, breaking, language, barriers', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-25 14:38:43,262] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/lesson-content -> endpoint: lesson_content_and_quiz
[2025-06-25 14:38:43,263] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "4b9778ef-704d-4ac9-8e50-97dfc4d91f77", "timestamp": "2025-06-25T13:38:43.262622+00:00", "method": "POST", "path": "/lesson-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "subject": "Artificial Intelligence", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "current_phase": "diagnostic_start_probe"}}
[2025-06-25 14:38:43,264] INFO - __main__ - main.py:12329 - [RAW HEADERS /lesson-content] Received Headers: {'Accept': 'application/json, text/plain, */*', 'Content-Type': 'application/json', 'X-Student-Id': 'andrea_ugono_33305', 'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3NzQ4NTAwMmYwNWJlMDI2N2VmNDU5ZjViNTEzNTMzYjVjNThjMTIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.M6ML7KnHkblr7XBnvFk7dYQF8Qcod6Ap3gJHTPJlS10l0-2Bis1lzkMXXxDEqLbrW55j5I5UqeVvuKOoYjDXy0v0KnYPKnAuHN5_cnUEmgsxgHX-4Ji-8TjSSE9QjX1lHBESTRVSrF2FHMezRYDkqNX5w5LfybRONww8ckpIAh6wJE_jB1w8kGoCguDMMxiYIx7JlAXThpTVFsf4oeZ_bxmz-C-XvogmAf0neRwUJKPrVsElZu0-BZ-hQrGrqJpEt0TBWHOjFicGf-WIuveT6IfE0XkJjxDKHFoUav_u4Rxfd8Ec3UDWoNNlcCGofTXNYLXM80Jho2joC-enLscQSw', 'User-Agent': 'axios/1.9.0', 'Content-Length': '225', 'Accept-Encoding': 'gzip, compress, deflate, br', 'Host': 'localhost:5000', 'Connection': 'keep-alive'}
[2025-06-25 14:38:43,267] INFO - auth_decorator - auth_decorator.py:39 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77][require_auth] Decorator invoked for path: /lesson-content
[2025-06-25 14:38:43,269] INFO - auth_decorator - auth_decorator.py:56 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:38:43,270] WARNING - __main__ - main.py:12336 - 🔥 LESSON-CONTENT ENDPOINT CALLED!
[2025-06-25 14:38:43,272] INFO - __main__ - main.py:12343 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] lesson_content_and_quiz invoked by /lesson-content
[2025-06-25 14:38:43,274] INFO - __main__ - main.py:12359 - Lesson content request by authenticated user: dev_student_uid_123 (Development Student)
[2025-06-25 14:38:43,275] INFO - __main__ - main.py:12363 - !!! [lesson_content_and_quiz] [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] RAW BODY AT START (bytes length: 225): b'{"student_id":"andrea_ugono_33305","lesson_ref":"P5-AI-002","subject":"Artificial Intelligence","country":"Nigeria","curriculum":"National Curriculum","grade":"Primary 5","level":"P5","current_phase":"diagnostic_start_probe"}'
[2025-06-25 14:38:43,276] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:38:43,276] INFO - __main__ - main.py:12456 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] ✅ USING existing 2 learning objectives
[2025-06-25 14:38:43,276] INFO - __main__ - main.py:12457 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] 🎯 EXISTING OBJECTIVES: ['Explore how chatbots and voice assistants work.', 'Discuss AI’s role in breaking language barriers.']
[2025-06-25 14:38:43,771] INFO - __main__ - main.py:10986 - [ad7b5d57-2afb-436e-aa75-f46e4a00ac01] Successfully created Firestore docs for session 'session_f8bf89bd-3588-4b93-b865-a489f1d6e596', student 'dev_student_uid_123'
[2025-06-25 14:38:43,779] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:38:43] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-25 14:38:44,079] INFO - __main__ - main.py:12463 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] 💾 SAVED learning objectives to Firestore
[2025-06-25 14:38:44,079] INFO - __main__ - main.py:10879 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] Attempting to initialize lesson session for student_id: dev_student_uid_123
[2025-06-25 14:38:44,080] DEBUG - __main__ - main.py:10880 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] lesson_data keys for init: ['lessonRef', 'lessonTitle', 'topic', 'subject', 'grade', 'key_concepts', 'learningObjectives', 'createdAt', 'updatedAt', 'instructionalSteps', 'content', 'sections', 'lessonTimeLength', 'introduction', 'conclusion', 'quizzes', 'adaptiveStrategies', 'blooms_level', 'difficulty', 'taxonomy_alignment', '_original_keys', '_mapping_timestamp', 'additionalNotes', 'existingAssessments', 'id', 'metadata', 'digitalMaterials', 'gradeLevel', 'country', 'extensionActivities', 'theme', 'curriculumType', 'quizzesAndAssessments', 'curriculum', 'level']
[2025-06-25 14:38:44,080] INFO - __main__ - main.py:10919 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] Using student name: 'Development Student' for session session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:38:44,081] INFO - __main__ - main.py:1592 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] Constructing initial lesson state data for Firestore session: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:38:44,081] INFO - __main__ - main.py:1642 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] ✅ Constructed initial state dictionary for session session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4. Phase: diagnostic_start_probe
[2025-06-25 14:38:44,081] INFO - __main__ - main.py:1643 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] ✅ VALIDATION: current_phase = diagnostic_start_probe, current_lesson_phase = diagnostic_start_probe
[2025-06-25 14:38:44,082] INFO - __main__ - main.py:10972 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] Preparing to create 'lesson_sessions' doc 'session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4'.
[2025-06-25 14:38:44,082] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 14:38:44,083] DEBUG - __main__ - main.py:10973 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] 'lesson_sessions' data (excluding snapshot): {'session_id': 'session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4', 'student_id': 'dev_student_uid_123', 'lessonRef': 'P5-AI-002', 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'Primary 5', 'level': 'P5', 'subject': 'Artificial Intelligence', 'status': 'active', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_interaction': Sentinel: Value used to set a document field to the server timestamp., 'progress': 0, 'completion_status': 'in_progress', 'last_modified': Sentinel: Value used to set a document field to the server timestamp., 'student_name_at_creation': 'Development Student', 'interactions': [], 'ai_interactions': [], 'current_segment_index': 0, 'completed_segments': [], 'has_notes': False, 'lessonTitle': 'Talking to AI!', 'topic': 'Talking to AI', 'learningObjectives': ['Explore how chatbots and voice assistants work.', 'Discuss AI’s role in breaking language barriers.'], 'key_concepts': ['Explore', 'chatbots', 'voice', 'assistants', 'work', 'Discuss', 'role', 'breaking', 'language', 'barriers'], 'metadata': {'blooms_level': ['Understanding', 'Applying', 'Analyzing'], 'context': 'Introduction', 'apiConnections': ["Gemini: Dynamically determines Bloom's Taxonomy level based on lesson content"], 'skills': ['Language', 'Interaction'], 'difficulty': 'medium', 'taxonomy_alignment': "Aligned to Bloom's Taxonomy by promoting understanding of AI communication tools, application through experimentation, and analysis of the impact on language barriers."}}
[2025-06-25 14:38:44,085] INFO - __main__ - main.py:10975 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] Preparing to create 'lesson_states' doc 'session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4'.
[2025-06-25 14:38:44,087] WARNING - __main__ - main.py:1016 - JSON serialization failed, using string representation: Object of type Sentinel is not JSON serializable
[2025-06-25 14:38:44,087] DEBUG - __main__ - main.py:10976 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] 'lesson_states' data: {'session_id': 'session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4', 'student_id': 'dev_student_uid_123', 'student_name': 'Development Student', 'current_lesson_phase': 'diagnostic_start_probe', 'current_phase': 'diagnostic_start_probe', 'current_probing_level_number': 5, 'current_question_index': 0, 'student_answers_for_probing_level': {}, 'levels_probed_and_failed': [], 'assigned_level_for_teaching': None, 'diagnostic_completed_this_session': False, 'lesson_context_snapshot': {'lessonRef': 'P5-AI-002', 'subject': 'Artificial Intelligence', 'grade': 'Primary 5', 'topic': 'Talking to AI', 'module_id': None, 'module_name': None}, 'blooms_levels_str_for_ai': 'Remember, Understand, Apply, Analyze, Evaluate, Create', 'key_concepts_str_for_ai': 'Explore, chatbots, voice, assistants, work, Discuss, role, breaking, language, barriers', 'created_at': Sentinel: Value used to set a document field to the server timestamp., 'last_modified': Sentinel: Value used to set a document field to the server timestamp.}
[2025-06-25 14:38:44,531] INFO - __main__ - main.py:10986 - [4b9778ef-704d-4ac9-8e50-97dfc4d91f77] Successfully created Firestore docs for session 'session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4', student 'dev_student_uid_123'
[2025-06-25 14:38:44,532] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:38:44] "POST /lesson-content HTTP/1.1" 200 -
[2025-06-25 14:38:50,826] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 14:38:50,826] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "9867b677-eca1-4306-8f1c-c921b41318b0", "timestamp": "2025-06-25T13:38:50.826600+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "content_to_enhance": "Start diagnostic assessment", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4", "chat_history": []}}
[2025-06-25 14:38:50,890] INFO - auth_decorator - auth_decorator.py:39 - [9867b677-eca1-4306-8f1c-c921b41318b0][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 14:38:50,891] INFO - auth_decorator - auth_decorator.py:56 - [9867b677-eca1-4306-8f1c-c921b41318b0][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:38:50,901] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 14:38:50,911] WARNING - __main__ - main.py:5262 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 14:38:50,913] INFO - __main__ - main.py:5315 - [9867b677-eca1-4306-8f1c-c921b41318b0] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4', lesson_ref='P5-AI-002'
[2025-06-25 14:38:51,217] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 14:38:51,219] INFO - __main__ - main.py:5361 - [9867b677-eca1-4306-8f1c-c921b41318b0] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:38:51,223] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:38:51,224] INFO - __main__ - main.py:5380 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 14:38:51,224] INFO - __main__ - main.py:5419 - [9867b677-eca1-4306-8f1c-c921b41318b0] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-25 14:38:51,226] INFO - __main__ - main.py:2332 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'Talking to AI!'.
[2025-06-25 14:38:51,725] INFO - __main__ - main.py:2391 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-25 14:38:51,725] INFO - __main__ - main.py:2391 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-25 14:38:51,726] INFO - __main__ - main.py:2391 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-25 14:38:51,726] INFO - __main__ - main.py:2391 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-25 14:38:51,727] INFO - __main__ - main.py:2391 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-25 14:38:51,728] INFO - __main__ - main.py:2460 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-25 14:38:51,729] DEBUG - __main__ - main.py:2474 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-25 14:38:51,730] DEBUG - __main__ - main.py:2477 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ev...
[2025-06-25 14:38:51,731] DEBUG - __main__ - main.py:2478 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference Lesson Summary (first 300 chars): Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ever...
[2025-06-25 14:38:51,732] DEBUG - __main__ - main.py:2479 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-25 14:38:51,732] INFO - __main__ - main.py:2483 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Calling Gemini API for module inference...
[2025-06-25 14:38:52,263] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-25 14:38:52,265] INFO - __main__ - main.py:2493 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Gemini API call completed in 0.53s. Raw response: 'ai_tools_and_applications'
[2025-06-25 14:38:52,265] DEBUG - __main__ - main.py:2515 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-25 14:38:52,266] INFO - __main__ - main.py:2520 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-25 14:38:52,266] INFO - __main__ - main.py:5452 - [9867b677-eca1-4306-8f1c-c921b41318b0] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-25 14:38:52,268] INFO - __main__ - main.py:5478 - [9867b677-eca1-4306-8f1c-c921b41318b0] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-25 14:38:52,568] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-25 14:38:53,122] WARNING - __main__ - main.py:5502 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍 SESSION STATE DEBUG:
[2025-06-25 14:38:53,123] WARNING - __main__ - main.py:5503 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   - Session exists: True
[2025-06-25 14:38:53,123] WARNING - __main__ - main.py:5504 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 14:38:53,124] WARNING - __main__ - main.py:5505 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   - State data keys: ['created_at', 'session_id', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'current_phase', 'diagnostic_completed_this_session', 'student_name', 'lesson_context_snapshot', 'current_probing_level_number', 'levels_probed_and_failed', 'assigned_level_for_teaching', 'current_lesson_phase', 'current_question_index', 'student_id', 'last_modified', 'student_answers_for_probing_level']
[2025-06-25 14:38:53,126] WARNING - __main__ - main.py:5525 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 14:38:53,128] INFO - __main__ - main.py:5561 - [9867b677-eca1-4306-8f1c-c921b41318b0] State protection not triggered
[2025-06-25 14:38:53,128] INFO - __main__ - main.py:5596 - [9867b677-eca1-4306-8f1c-c921b41318b0] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 14:38:53,129] INFO - __main__ - main.py:5597 - [9867b677-eca1-4306-8f1c-c921b41318b0]   assigned_level_for_teaching (session): None
[2025-06-25 14:38:53,129] INFO - __main__ - main.py:5598 - [9867b677-eca1-4306-8f1c-c921b41318b0]   latest_assessed_level (profile): None
[2025-06-25 14:38:53,130] INFO - __main__ - main.py:5599 - [9867b677-eca1-4306-8f1c-c921b41318b0]   teaching_level_for_returning_student: None
[2025-06-25 14:38:53,131] INFO - __main__ - main.py:5600 - [9867b677-eca1-4306-8f1c-c921b41318b0]   has_completed_diagnostic_before: False
[2025-06-25 14:38:53,131] INFO - __main__ - main.py:5601 - [9867b677-eca1-4306-8f1c-c921b41318b0]   is_first_encounter_for_module: True
[2025-06-25 14:38:53,131] WARNING - __main__ - main.py:5606 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 14:38:53,132] INFO - __main__ - main.py:5612 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍 PHASE INVESTIGATION:
[2025-06-25 14:38:53,133] INFO - __main__ - main.py:5613 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:38:53,133] INFO - __main__ - main.py:5614 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 14:38:53,133] INFO - __main__ - main.py:5615 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Is first encounter: True
[2025-06-25 14:38:53,134] INFO - __main__ - main.py:5616 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Diagnostic completed: False
[2025-06-25 14:38:53,134] INFO - __main__ - main.py:5622 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:38:53,134] INFO - __main__ - main.py:5636 - [9867b677-eca1-4306-8f1c-c921b41318b0] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 14:38:53,135] INFO - __main__ - main.py:5638 - [9867b677-eca1-4306-8f1c-c921b41318b0] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 14:38:53,135] INFO - __main__ - main.py:5658 - [9867b677-eca1-4306-8f1c-c921b41318b0] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-25 14:38:53,137] INFO - __main__ - main.py:3813 - [9867b677-eca1-4306-8f1c-c921b41318b0] Diagnostic context validation passed
[2025-06-25 14:38:53,137] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 14:38:53,138] WARNING - __main__ - main.py:5746 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 14:38:53,139] INFO - __main__ - main.py:3923 - [9867b677-eca1-4306-8f1c-c921b41318b0] Enhanced diagnostic context with 45 fields
[2025-06-25 14:38:53,140] INFO - __main__ - main.py:5765 - [9867b677-eca1-4306-8f1c-c921b41318b0] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 14:38:53,141] INFO - __main__ - main.py:5778 - [9867b677-eca1-4306-8f1c-c921b41318b0] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 14:38:53,142] DEBUG - __main__ - main.py:5779 - [9867b677-eca1-4306-8f1c-c921b41318b0] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 14:38:53,142] WARNING - __main__ - main.py:5980 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 14:38:53,143] WARNING - __main__ - main.py:5981 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 14:38:53,143] WARNING - __main__ - main.py:5982 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖   - Student query: Start diagnostic assessment...
[2025-06-25 14:38:53,144] INFO - __main__ - main.py:10405 - [9867b677-eca1-4306-8f1c-c921b41318b0] 👤 STUDENT NAME DEBUG:
[2025-06-25 14:38:53,144] INFO - __main__ - main.py:10406 - [9867b677-eca1-4306-8f1c-c921b41318b0] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:38:53,149] INFO - __main__ - main.py:10407 - [9867b677-eca1-4306-8f1c-c921b41318b0] 👤   context.student_name: NOT_FOUND
[2025-06-25 14:38:53,149] INFO - __main__ - main.py:10408 - [9867b677-eca1-4306-8f1c-c921b41318b0] 👤   final student_name: Andrea
[2025-06-25 14:38:53,150] INFO - __main__ - main.py:10488 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖 Generating natural AI response for Andrea...
[2025-06-25 14:38:54,377] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 406 characters
[2025-06-25 14:38:54,377] INFO - __main__ - main.py:10509 - [9867b677-eca1-4306-8f1c-c921b41318b0] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 14:38:54,377] INFO - __main__ - main.py:10540 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ Generated natural response for Andrea: 406 chars
[2025-06-25 14:38:54,378] INFO - __main__ - main.py:10541 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:38:54,378] WARNING - __main__ - main.py:6004 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 14:38:54,379] WARNING - __main__ - main.py:6005 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖   - Content length: 406 chars
[2025-06-25 14:38:54,379] WARNING - __main__ - main.py:6006 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:38:54,380] WARNING - __main__ - main.py:6007 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🤖   - Raw state block: None...
[2025-06-25 14:38:54,382] INFO - __main__ - main.py:6032 - [9867b677-eca1-4306-8f1c-c921b41318b0] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 14:38:54,383] INFO - __main__ - main.py:6033 - [9867b677-eca1-4306-8f1c-c921b41318b0] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 14:38:54,714] INFO - __main__ - main.py:6082 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI processing completed in 1.57s
[2025-06-25 14:38:54,715] WARNING - __main__ - main.py:6093 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 14:38:54,715] INFO - __main__ - main.py:4018 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:38:54,715] WARNING - __main__ - main.py:6102 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 14:38:54,716] WARNING - __main__ - main.py:6109 - [9867b677-eca1-4306-8f1c-c921b41318b0] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 14:38:54,716] WARNING - __main__ - main.py:6116 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 14:38:54,716] WARNING - __main__ - main.py:6117 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 14:38:54,717] WARNING - __main__ - main.py:6118 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 14:38:54,718] WARNING - __main__ - main.py:6119 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:38:54,719] WARNING - __main__ - main.py:6120 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:38:54,719] WARNING - __main__ - main.py:6123 - [9867b677-eca1-4306-8f1c-c921b41318b0] 💾 FINAL STATE APPLICATION:
[2025-06-25 14:38:54,720] WARNING - __main__ - main.py:6124 - [9867b677-eca1-4306-8f1c-c921b41318b0] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 14:38:54,721] WARNING - __main__ - main.py:6125 - [9867b677-eca1-4306-8f1c-c921b41318b0] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:38:54,721] WARNING - __main__ - main.py:6126 - [9867b677-eca1-4306-8f1c-c921b41318b0] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:38:54,721] WARNING - __main__ - main.py:6127 - [9867b677-eca1-4306-8f1c-c921b41318b0] 💾   - Phase change: False
[2025-06-25 14:38:54,722] INFO - __main__ - main.py:4050 - [9867b677-eca1-4306-8f1c-c921b41318b0] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 14:38:54,722] INFO - __main__ - main.py:4051 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 14:38:54,722] INFO - __main__ - main.py:4052 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Current level: 5
[2025-06-25 14:38:54,723] INFO - __main__ - main.py:4053 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Question index: 0
[2025-06-25 14:38:54,723] INFO - __main__ - main.py:4054 - [9867b677-eca1-4306-8f1c-c921b41318b0]   First encounter: True
[2025-06-25 14:38:54,723] INFO - __main__ - main.py:4059 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Answers collected: 0
[2025-06-25 14:38:54,724] INFO - __main__ - main.py:4060 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Levels failed: 0
[2025-06-25 14:38:54,724] INFO - __main__ - main.py:4018 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:38:54,724] INFO - __main__ - main.py:4064 - [9867b677-eca1-4306-8f1c-c921b41318b0]   State update valid: True
[2025-06-25 14:38:54,725] INFO - __main__ - main.py:4071 - [9867b677-eca1-4306-8f1c-c921b41318b0]   Diagnostic complete: False
[2025-06-25 14:38:54,725] WARNING - __main__ - main.py:6140 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 14:38:54,725] INFO - __main__ - main.py:6149 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 14:38:54,726] INFO - __main__ - main.py:6150 - [9867b677-eca1-4306-8f1c-c921b41318b0] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 14:38:55,259] WARNING - __main__ - main.py:6195 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 14:38:55,260] WARNING - __main__ - main.py:6196 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Phase: diagnostic_start_probe
[2025-06-25 14:38:55,260] WARNING - __main__ - main.py:6197 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Probing Level: 5
[2025-06-25 14:38:55,260] WARNING - __main__ - main.py:6198 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Question Index: 1
[2025-06-25 14:38:55,261] WARNING - __main__ - main.py:6199 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Diagnostic Complete: False
[2025-06-25 14:38:55,261] WARNING - __main__ - main.py:6206 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Quiz Questions Saved: 0
[2025-06-25 14:38:55,262] WARNING - __main__ - main.py:6207 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Quiz Answers Saved: 0
[2025-06-25 14:38:55,262] WARNING - __main__ - main.py:6208 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Quiz Started: False
[2025-06-25 14:38:56,292] INFO - __main__ - main.py:6268 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ Updated existing session document: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:38:56,292] WARNING - __main__ - main.py:6269 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 14:38:56,293] WARNING - __main__ - main.py:6270 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Session ID: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:38:56,293] WARNING - __main__ - main.py:6271 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:38:56,294] WARNING - __main__ - main.py:6272 - [9867b677-eca1-4306-8f1c-c921b41318b0] ✅   - Interaction logged successfully
[2025-06-25 14:38:56,297] INFO - __main__ - main.py:11317 - [9867b677-eca1-4306-8f1c-c921b41318b0] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 14:38:56,302] DEBUG - __main__ - main.py:2812 - [9867b677-eca1-4306-8f1c-c921b41318b0] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 14:38:56,303] DEBUG - __main__ - main.py:6318 - [9867b677-eca1-4306-8f1c-c921b41318b0] No final assessment data found in AI response
[2025-06-25 14:38:56,310] INFO - __main__ - main.py:6404 - [9867b677-eca1-4306-8f1c-c921b41318b0] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 14:38:56,336] WARNING - __main__ - main.py:680 - High response time detected: 5.44s for enhance_content_api
[2025-06-25 14:38:56,337] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:38:56] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 14:39:55,752] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 14:39:55,758] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "2baed504-032e-452f-b7eb-9c74154d497a", "timestamp": "2025-06-25T13:39:55.754642+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "content_to_enhance": "Talking to a computer.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm so excited to start learning about Artificial Intelligence with you today. We're going to explore the super cool world of \"Talking to AI\"!\n\nTo get us started, I have a few quick questions to see what you already know. No worries at all, it's just to help me understand how best to help you learn.\n\nSo, Andrea, when you hear \"Talking to AI,\" what's the first thing that pops into your mind? \ud83d\ude0a", "timestamp": "2025-06-25T13:38:56.370Z"}]}}
[2025-06-25 14:39:55,761] INFO - auth_decorator - auth_decorator.py:39 - [2baed504-032e-452f-b7eb-9c74154d497a][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 14:39:55,762] INFO - auth_decorator - auth_decorator.py:56 - [2baed504-032e-452f-b7eb-9c74154d497a][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:39:55,762] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 14:39:55,765] WARNING - __main__ - main.py:5262 - [2baed504-032e-452f-b7eb-9c74154d497a] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 14:39:55,766] INFO - __main__ - main.py:5315 - [2baed504-032e-452f-b7eb-9c74154d497a] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4', lesson_ref='P5-AI-002'
[2025-06-25 14:39:56,113] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 14:39:56,114] INFO - __main__ - main.py:5361 - [2baed504-032e-452f-b7eb-9c74154d497a] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:39:56,115] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:39:56,115] INFO - __main__ - main.py:5380 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 14:39:56,115] INFO - __main__ - main.py:5419 - [2baed504-032e-452f-b7eb-9c74154d497a] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-25 14:39:56,117] INFO - __main__ - main.py:2332 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'Talking to AI!'.
[2025-06-25 14:39:56,599] INFO - __main__ - main.py:2391 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-25 14:39:56,599] INFO - __main__ - main.py:2391 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-25 14:39:56,599] INFO - __main__ - main.py:2391 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-25 14:39:56,600] INFO - __main__ - main.py:2391 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-25 14:39:56,600] INFO - __main__ - main.py:2391 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-25 14:39:56,600] INFO - __main__ - main.py:2460 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-25 14:39:56,601] DEBUG - __main__ - main.py:2474 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-25 14:39:56,601] DEBUG - __main__ - main.py:2477 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ev...
[2025-06-25 14:39:56,601] DEBUG - __main__ - main.py:2478 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference Lesson Summary (first 300 chars): Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ever...
[2025-06-25 14:39:56,602] DEBUG - __main__ - main.py:2479 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-25 14:39:56,602] INFO - __main__ - main.py:2483 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Calling Gemini API for module inference...
[2025-06-25 14:39:57,072] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-25 14:39:57,073] INFO - __main__ - main.py:2493 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Gemini API call completed in 0.47s. Raw response: 'ai_tools_and_applications'
[2025-06-25 14:39:57,073] DEBUG - __main__ - main.py:2515 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-25 14:39:57,073] INFO - __main__ - main.py:2520 - [2baed504-032e-452f-b7eb-9c74154d497a] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-25 14:39:57,074] INFO - __main__ - main.py:5452 - [2baed504-032e-452f-b7eb-9c74154d497a] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-25 14:39:57,074] INFO - __main__ - main.py:5478 - [2baed504-032e-452f-b7eb-9c74154d497a] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-25 14:39:57,376] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-25 14:39:57,893] WARNING - __main__ - main.py:5502 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍 SESSION STATE DEBUG:
[2025-06-25 14:39:57,893] WARNING - __main__ - main.py:5503 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   - Session exists: True
[2025-06-25 14:39:57,893] WARNING - __main__ - main.py:5504 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 14:39:57,894] WARNING - __main__ - main.py:5505 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 14:39:57,896] WARNING - __main__ - main.py:5525 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 14:39:57,897] INFO - __main__ - main.py:5561 - [2baed504-032e-452f-b7eb-9c74154d497a] State protection not triggered
[2025-06-25 14:39:57,897] INFO - __main__ - main.py:5596 - [2baed504-032e-452f-b7eb-9c74154d497a] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 14:39:57,898] INFO - __main__ - main.py:5597 - [2baed504-032e-452f-b7eb-9c74154d497a]   assigned_level_for_teaching (session): None
[2025-06-25 14:39:57,898] INFO - __main__ - main.py:5598 - [2baed504-032e-452f-b7eb-9c74154d497a]   latest_assessed_level (profile): None
[2025-06-25 14:39:57,898] INFO - __main__ - main.py:5599 - [2baed504-032e-452f-b7eb-9c74154d497a]   teaching_level_for_returning_student: None
[2025-06-25 14:39:57,898] INFO - __main__ - main.py:5600 - [2baed504-032e-452f-b7eb-9c74154d497a]   has_completed_diagnostic_before: False
[2025-06-25 14:39:57,899] INFO - __main__ - main.py:5601 - [2baed504-032e-452f-b7eb-9c74154d497a]   is_first_encounter_for_module: True
[2025-06-25 14:39:57,899] WARNING - __main__ - main.py:5606 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 14:39:57,899] INFO - __main__ - main.py:5612 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍 PHASE INVESTIGATION:
[2025-06-25 14:39:57,899] INFO - __main__ - main.py:5613 - [2baed504-032e-452f-b7eb-9c74154d497a]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:39:57,900] INFO - __main__ - main.py:5614 - [2baed504-032e-452f-b7eb-9c74154d497a]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 14:39:57,900] INFO - __main__ - main.py:5615 - [2baed504-032e-452f-b7eb-9c74154d497a]   Is first encounter: True
[2025-06-25 14:39:57,900] INFO - __main__ - main.py:5616 - [2baed504-032e-452f-b7eb-9c74154d497a]   Diagnostic completed: False
[2025-06-25 14:39:57,900] INFO - __main__ - main.py:5622 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:39:57,900] INFO - __main__ - main.py:5636 - [2baed504-032e-452f-b7eb-9c74154d497a] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 14:39:57,901] INFO - __main__ - main.py:5638 - [2baed504-032e-452f-b7eb-9c74154d497a] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 14:39:57,901] INFO - __main__ - main.py:5658 - [2baed504-032e-452f-b7eb-9c74154d497a] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 14:39:57,901] INFO - __main__ - main.py:3813 - [2baed504-032e-452f-b7eb-9c74154d497a] Diagnostic context validation passed
[2025-06-25 14:39:57,901] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 14:39:57,902] WARNING - __main__ - main.py:5746 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 14:39:57,902] INFO - __main__ - main.py:3923 - [2baed504-032e-452f-b7eb-9c74154d497a] Enhanced diagnostic context with 45 fields
[2025-06-25 14:39:57,902] INFO - __main__ - main.py:5765 - [2baed504-032e-452f-b7eb-9c74154d497a] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 14:39:57,902] INFO - __main__ - main.py:5778 - [2baed504-032e-452f-b7eb-9c74154d497a] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 14:39:57,903] DEBUG - __main__ - main.py:5779 - [2baed504-032e-452f-b7eb-9c74154d497a] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 14:39:57,903] WARNING - __main__ - main.py:5980 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 14:39:57,903] WARNING - __main__ - main.py:5981 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 14:39:57,904] WARNING - __main__ - main.py:5982 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖   - Student query: Talking to a computer....
[2025-06-25 14:39:57,905] INFO - __main__ - main.py:10405 - [2baed504-032e-452f-b7eb-9c74154d497a] 👤 STUDENT NAME DEBUG:
[2025-06-25 14:39:57,906] INFO - __main__ - main.py:10406 - [2baed504-032e-452f-b7eb-9c74154d497a] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:39:57,907] INFO - __main__ - main.py:10407 - [2baed504-032e-452f-b7eb-9c74154d497a] 👤   context.student_name: NOT_FOUND
[2025-06-25 14:39:57,907] INFO - __main__ - main.py:10408 - [2baed504-032e-452f-b7eb-9c74154d497a] 👤   final student_name: Andrea
[2025-06-25 14:39:57,908] INFO - __main__ - main.py:10488 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖 Generating natural AI response for Andrea...
[2025-06-25 14:39:59,200] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 330 characters
[2025-06-25 14:39:59,201] INFO - __main__ - main.py:10509 - [2baed504-032e-452f-b7eb-9c74154d497a] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 14:39:59,202] INFO - __main__ - main.py:10540 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ Generated natural response for Andrea: 330 chars
[2025-06-25 14:39:59,202] INFO - __main__ - main.py:10541 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:39:59,204] WARNING - __main__ - main.py:6004 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 14:39:59,205] WARNING - __main__ - main.py:6005 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖   - Content length: 330 chars
[2025-06-25 14:39:59,206] WARNING - __main__ - main.py:6006 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:39:59,207] WARNING - __main__ - main.py:6007 - [2baed504-032e-452f-b7eb-9c74154d497a] 🤖   - Raw state block: None...
[2025-06-25 14:39:59,209] INFO - __main__ - main.py:6032 - [2baed504-032e-452f-b7eb-9c74154d497a] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 14:39:59,209] INFO - __main__ - main.py:6033 - [2baed504-032e-452f-b7eb-9c74154d497a] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 14:39:59,535] INFO - __main__ - main.py:6082 - [2baed504-032e-452f-b7eb-9c74154d497a] AI processing completed in 1.63s
[2025-06-25 14:39:59,536] WARNING - __main__ - main.py:6093 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 14:39:59,536] INFO - __main__ - main.py:4018 - [2baed504-032e-452f-b7eb-9c74154d497a] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:39:59,537] WARNING - __main__ - main.py:6102 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 14:39:59,537] WARNING - __main__ - main.py:6109 - [2baed504-032e-452f-b7eb-9c74154d497a] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 14:39:59,537] WARNING - __main__ - main.py:6116 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 14:39:59,538] WARNING - __main__ - main.py:6117 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 14:39:59,539] WARNING - __main__ - main.py:6118 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 14:39:59,540] WARNING - __main__ - main.py:6119 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:39:59,541] WARNING - __main__ - main.py:6120 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:39:59,541] WARNING - __main__ - main.py:6123 - [2baed504-032e-452f-b7eb-9c74154d497a] 💾 FINAL STATE APPLICATION:
[2025-06-25 14:39:59,542] WARNING - __main__ - main.py:6124 - [2baed504-032e-452f-b7eb-9c74154d497a] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 14:39:59,542] WARNING - __main__ - main.py:6125 - [2baed504-032e-452f-b7eb-9c74154d497a] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:39:59,543] WARNING - __main__ - main.py:6126 - [2baed504-032e-452f-b7eb-9c74154d497a] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:39:59,543] WARNING - __main__ - main.py:6127 - [2baed504-032e-452f-b7eb-9c74154d497a] 💾   - Phase change: False
[2025-06-25 14:39:59,544] INFO - __main__ - main.py:4050 - [2baed504-032e-452f-b7eb-9c74154d497a] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 14:39:59,544] INFO - __main__ - main.py:4051 - [2baed504-032e-452f-b7eb-9c74154d497a]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 14:39:59,544] INFO - __main__ - main.py:4052 - [2baed504-032e-452f-b7eb-9c74154d497a]   Current level: 5
[2025-06-25 14:39:59,544] INFO - __main__ - main.py:4053 - [2baed504-032e-452f-b7eb-9c74154d497a]   Question index: 0
[2025-06-25 14:39:59,545] INFO - __main__ - main.py:4054 - [2baed504-032e-452f-b7eb-9c74154d497a]   First encounter: True
[2025-06-25 14:39:59,545] INFO - __main__ - main.py:4059 - [2baed504-032e-452f-b7eb-9c74154d497a]   Answers collected: 0
[2025-06-25 14:39:59,545] INFO - __main__ - main.py:4060 - [2baed504-032e-452f-b7eb-9c74154d497a]   Levels failed: 0
[2025-06-25 14:39:59,546] INFO - __main__ - main.py:4018 - [2baed504-032e-452f-b7eb-9c74154d497a] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:39:59,546] INFO - __main__ - main.py:4064 - [2baed504-032e-452f-b7eb-9c74154d497a]   State update valid: True
[2025-06-25 14:39:59,546] INFO - __main__ - main.py:4071 - [2baed504-032e-452f-b7eb-9c74154d497a]   Diagnostic complete: False
[2025-06-25 14:39:59,547] WARNING - __main__ - main.py:6140 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 14:39:59,547] INFO - __main__ - main.py:6149 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 14:39:59,548] INFO - __main__ - main.py:6150 - [2baed504-032e-452f-b7eb-9c74154d497a] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 14:40:00,086] WARNING - __main__ - main.py:6195 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 14:40:00,086] WARNING - __main__ - main.py:6196 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Phase: diagnostic_start_probe
[2025-06-25 14:40:00,087] WARNING - __main__ - main.py:6197 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Probing Level: 5
[2025-06-25 14:40:00,087] WARNING - __main__ - main.py:6198 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Question Index: 1
[2025-06-25 14:40:00,088] WARNING - __main__ - main.py:6199 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Diagnostic Complete: False
[2025-06-25 14:40:00,089] WARNING - __main__ - main.py:6206 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Quiz Questions Saved: 0
[2025-06-25 14:40:00,090] WARNING - __main__ - main.py:6207 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Quiz Answers Saved: 0
[2025-06-25 14:40:00,090] WARNING - __main__ - main.py:6208 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Quiz Started: False
[2025-06-25 14:40:01,051] INFO - __main__ - main.py:6268 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ Updated existing session document: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:40:01,052] WARNING - __main__ - main.py:6269 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 14:40:01,052] WARNING - __main__ - main.py:6270 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Session ID: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:40:01,053] WARNING - __main__ - main.py:6271 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:40:01,054] WARNING - __main__ - main.py:6272 - [2baed504-032e-452f-b7eb-9c74154d497a] ✅   - Interaction logged successfully
[2025-06-25 14:40:01,055] INFO - __main__ - main.py:11317 - [2baed504-032e-452f-b7eb-9c74154d497a] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 14:40:01,056] DEBUG - __main__ - main.py:2812 - [2baed504-032e-452f-b7eb-9c74154d497a] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 14:40:01,057] DEBUG - __main__ - main.py:6318 - [2baed504-032e-452f-b7eb-9c74154d497a] No final assessment data found in AI response
[2025-06-25 14:40:01,061] INFO - __main__ - main.py:6404 - [2baed504-032e-452f-b7eb-9c74154d497a] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 14:40:01,063] WARNING - __main__ - main.py:680 - High response time detected: 5.30s for enhance_content_api
[2025-06-25 14:40:01,064] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:40:01] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 14:40:23,889] INFO - __main__ - main.py:5041 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 14:40:23,892] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "2bcf6cd9-3607-474d-a07d-8f33e86584c0", "timestamp": "2025-06-25T13:40:23.890761+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "axios/1.9.0", "user_id": "anonymous", "role": "unknown", "body": {"student_id": "andrea_ugono_33305", "lesson_ref": "P5-AI-002", "content_to_enhance": "No.", "country": "Nigeria", "curriculum": "National Curriculum", "grade": "Primary 5", "level": "P5", "subject": "Artificial Intelligence", "session_id": "session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4", "chat_history": [{"role": "assistant", "content": "Hi Andrea! I'm so excited to start learning about Artificial Intelligence with you today. We're going to explore the super cool world of \"Talking to AI\"!\n\nTo get us started, I have a few quick questions to see what you already know. No worries at all, it's just to help me understand how best to help you learn.\n\nSo, Andrea, when you hear \"Talking to AI,\" what's the first thing that pops into your mind? \ud83d\ude0a", "timestamp": "2025-06-25T13:38:56.370Z"}, {"role": "user", "content": "Talking to a computer.", "timestamp": "2025-06-25T13:39:55.592Z"}, {"role": "assistant", "content": "That's a fantastic start, Andrea! \"Talking to a computer\" is exactly right! We're going to learn all about how we can have conversations with computers, almost like talking to a friend.\n\nSo, thinking about that, Andrea, have you ever talked to a computer or a device before? Maybe asked it a question or told it to do something? \ud83d\ude0a", "timestamp": "2025-06-25T13:40:01.092Z"}]}}
[2025-06-25 14:40:23,895] INFO - auth_decorator - auth_decorator.py:39 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 14:40:23,896] INFO - auth_decorator - auth_decorator.py:56 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0][require_auth] Development mode detected - bypassing authentication
[2025-06-25 14:40:23,896] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 14:40:23,898] WARNING - __main__ - main.py:5262 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 14:40:23,900] INFO - __main__ - main.py:5315 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Parsed Params: student_id='andrea_ugono_33305', session_id='session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4', lesson_ref='P5-AI-002'
[2025-06-25 14:40:24,239] INFO - __main__ - main.py:4634 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-25 14:40:24,240] INFO - __main__ - main.py:5361 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 👤 Final student profile: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:40:24,241] DEBUG - __main__ - main.py:651 - Cache hit for fetch_lesson_data
[2025-06-25 14:40:24,241] INFO - __main__ - main.py:5380 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 14:40:24,241] INFO - __main__ - main.py:5419 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Attempting to infer module. GS Subject Slug: 'artificial_intelligence'.
[2025-06-25 14:40:24,242] INFO - __main__ - main.py:2332 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Inferring module for subject 'artificial_intelligence', lesson 'Talking to AI!'.
[2025-06-25 14:40:24,726] INFO - __main__ - main.py:2391 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Loaded metadata for module 'ai_awareness_and_foundations' ('AI Awareness & Foundations')
[2025-06-25 14:40:24,727] INFO - __main__ - main.py:2391 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Loaded metadata for module 'ai_tools_and_applications' ('AI Tools & Applications')
[2025-06-25 14:40:24,727] INFO - __main__ - main.py:2391 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Loaded metadata for module 'computational_thinking_and_coding' ('Computational Thinking & Coding')
[2025-06-25 14:40:24,727] INFO - __main__ - main.py:2391 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Loaded metadata for module 'data_literacy_and_machine_learning' ('Data Literacy & Machine Learning')
[2025-06-25 14:40:24,728] INFO - __main__ - main.py:2391 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Loaded metadata for module 'ethics_and_societal_impact' ('Ethics & Societal Impact')
[2025-06-25 14:40:24,728] INFO - __main__ - main.py:2460 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Starting module inference for subject 'artificial_intelligence' with 5 module options
[2025-06-25 14:40:24,729] DEBUG - __main__ - main.py:2474 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Sample modules available (first 3):
- ai_awareness_and_foundations: AI Awareness & Foundations
- ai_tools_and_applications: AI Tools & Applications
- computational_thinking_and_coding: Computational Thinking & Coding
[2025-06-25 14:40:24,729] DEBUG - __main__ - main.py:2477 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ev...
[2025-06-25 14:40:24,730] DEBUG - __main__ - main.py:2478 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference Lesson Summary (first 300 chars): Lesson Title: Talking to AI!. Topic: Talking to AI. Learning Objectives: Explore how chatbots and voice assistants work.; Discuss AI’s role in breaking language barriers.. Key Concepts: Explore; chatbots; voice; assistants; work; Discuss; role; breaking; language; barriers. Introduction: Hello, ever...
[2025-06-25 14:40:24,731] DEBUG - __main__ - main.py:2479 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference Module Options (first 200 chars): 1. Slug: "ai_awareness_and_foundations", Name: "AI Awareness & Foundations", Description: "Conceptual journey from recognising smart helpers to understanding core AI paradigms (symbolic, statistical, ...
[2025-06-25 14:40:24,731] INFO - __main__ - main.py:2483 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Calling Gemini API for module inference...
[2025-06-25 14:40:25,109] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 25 characters
[2025-06-25 14:40:25,110] INFO - __main__ - main.py:2493 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Gemini API call completed in 0.38s. Raw response: 'ai_tools_and_applications'
[2025-06-25 14:40:25,110] DEBUG - __main__ - main.py:2515 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Cleaned slug: 'ai_tools_and_applications'
[2025-06-25 14:40:25,111] INFO - __main__ - main.py:2520 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI Inference: Successfully matched module by slug. Chosen: 'ai_tools_and_applications' (AI Tools & Applications)
[2025-06-25 14:40:25,111] INFO - __main__ - main.py:5452 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Successfully inferred module ID via AI: ai_tools_and_applications
[2025-06-25 14:40:25,111] INFO - __main__ - main.py:5478 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Effective module name for prompt context: 'AI Tools & Applications' (Module ID: ai_tools_and_applications)
[2025-06-25 14:40:25,437] INFO - __main__ - main.py:2050 - No prior student performance document found for Topic: artificial_intelligence_Primary 5_artificial_intelligence_ai_tools_and_applications
[2025-06-25 14:40:25,909] WARNING - __main__ - main.py:5502 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍 SESSION STATE DEBUG:
[2025-06-25 14:40:25,910] WARNING - __main__ - main.py:5503 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   - Session exists: True
[2025-06-25 14:40:25,910] WARNING - __main__ - main.py:5504 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   - Current phase: diagnostic_start_probe
[2025-06-25 14:40:25,911] WARNING - __main__ - main.py:5505 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'current_phase', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'student_name', 'is_first_encounter_for_module', 'current_lesson_phase', 'current_quiz_question', 'quiz_answers', 'last_modified', 'latest_assessed_level_for_module', 'teaching_interactions', 'session_id', 'quiz_questions_generated', 'teaching_complete', 'lesson_start_time', 'diagnostic_completed_this_session', 'assigned_level_for_teaching', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-25 14:40:25,913] WARNING - __main__ - main.py:5525 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔒 STATE PROTECTION: phase='diagnostic_start_probe', diagnostic_done=False, level=None
[2025-06-25 14:40:25,914] INFO - __main__ - main.py:5561 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] State protection not triggered
[2025-06-25 14:40:25,914] INFO - __main__ - main.py:5596 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 14:40:25,915] INFO - __main__ - main.py:5597 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   assigned_level_for_teaching (session): None
[2025-06-25 14:40:25,915] INFO - __main__ - main.py:5598 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   latest_assessed_level (profile): None
[2025-06-25 14:40:25,916] INFO - __main__ - main.py:5599 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   teaching_level_for_returning_student: None
[2025-06-25 14:40:25,916] INFO - __main__ - main.py:5600 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   has_completed_diagnostic_before: False
[2025-06-25 14:40:25,917] INFO - __main__ - main.py:5601 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   is_first_encounter_for_module: True
[2025-06-25 14:40:25,917] WARNING - __main__ - main.py:5606 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 14:40:25,917] INFO - __main__ - main.py:5612 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍 PHASE INVESTIGATION:
[2025-06-25 14:40:25,918] INFO - __main__ - main.py:5613 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Retrieved from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:40:25,918] INFO - __main__ - main.py:5614 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 14:40:25,919] INFO - __main__ - main.py:5615 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Is first encounter: True
[2025-06-25 14:40:25,919] INFO - __main__ - main.py:5616 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Diagnostic completed: False
[2025-06-25 14:40:25,919] INFO - __main__ - main.py:5622 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ Using stored phase from Firestore: 'diagnostic_start_probe'
[2025-06-25 14:40:25,920] INFO - __main__ - main.py:5636 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 14:40:25,920] INFO - __main__ - main.py:5638 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 14:40:25,921] INFO - __main__ - main.py:5658 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] NEW SESSION: Forcing question_index to 0 (was: 1)
[2025-06-25 14:40:25,922] INFO - __main__ - main.py:3813 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Diagnostic context validation passed
[2025-06-25 14:40:25,922] INFO - __main__ - main.py:3840 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 14:40:25,923] WARNING - __main__ - main.py:5746 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 14:40:25,924] INFO - __main__ - main.py:3923 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Enhanced diagnostic context with 45 fields
[2025-06-25 14:40:25,924] INFO - __main__ - main.py:5765 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 14:40:25,925] INFO - __main__ - main.py:5778 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 14:40:25,925] DEBUG - __main__ - main.py:5779 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 14:40:25,926] WARNING - __main__ - main.py:5980 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 14:40:25,926] WARNING - __main__ - main.py:5981 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 14:40:25,927] WARNING - __main__ - main.py:5982 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖   - Student query: No....
[2025-06-25 14:40:25,928] INFO - __main__ - main.py:10405 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 👤 STUDENT NAME DEBUG:
[2025-06-25 14:40:25,928] INFO - __main__ - main.py:10406 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 👤   student_info: {'age': 9, 'studentId': 'andrea_ugono_33305', 'lastLogin': None, 'dateOfBirth': '2015-06-19', 'enrolledSubjects': ['Mathematics', 'English', 'Science'], 'parent_id': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'updatedAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'accountStatus': 'active', 'name': 'Andrea Ugono', 'gradeLevelLabel': 'Primary 5', 'gradeLevel': 'primary-5', 'status': 'active', 'password': '$2b$10$Z6VuKBYtcdqOcjBvanJ0KO4o1KceGqbi0Dby/3zZjSLlPGr.ZGvLC', 'userId': 'andrea_ugono_33305', 'country': 'Nigeria', 'emailVerified': False, 'createdAt': DatetimeWithNanoseconds(2025, 3, 10, 14, 36, 57, 891000, tzinfo=datetime.timezone.utc), 'role': 'student', 'subjects': ['Christian Religious Knowledge', 'Financial Literacy', 'Art and Design', 'National Values'], 'parentId': 'bXL5OyuwhmNeUMEh2wXJbOgpvNm2', 'first_name': 'Andrea', 'last_name': 'Ugono'}
[2025-06-25 14:40:25,929] INFO - __main__ - main.py:10407 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 👤   context.student_name: NOT_FOUND
[2025-06-25 14:40:25,930] INFO - __main__ - main.py:10408 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 👤   final student_name: Andrea
[2025-06-25 14:40:25,930] INFO - __main__ - main.py:10488 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖 Generating natural AI response for Andrea...
[2025-06-25 14:40:26,492] DEBUG - __main__ - main.py:2255 - extract_gemini_text: Successfully extracted 315 characters
[2025-06-25 14:40:26,493] INFO - __main__ - main.py:10509 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 📈 Staying in diagnostic, advancing question index to 1
[2025-06-25 14:40:26,493] INFO - __main__ - main.py:10540 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ Generated natural response for Andrea: 315 chars
[2025-06-25 14:40:26,493] INFO - __main__ - main.py:10541 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔄 State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:40:26,493] WARNING - __main__ - main.py:6004 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 14:40:26,494] WARNING - __main__ - main.py:6005 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖   - Content length: 315 chars
[2025-06-25 14:40:26,494] WARNING - __main__ - main.py:6006 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖   - State updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:40:26,494] WARNING - __main__ - main.py:6007 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🤖   - Raw state block: None...
[2025-06-25 14:40:26,494] INFO - __main__ - main.py:6032 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-25 14:40:26,495] INFO - __main__ - main.py:6033 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] CURRENT PHASE DETERMINATION: AI=diagnostic_start_probe, Session=diagnostic_start_probe, Final=diagnostic_start_probe
[2025-06-25 14:40:26,798] INFO - __main__ - main.py:6082 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI processing completed in 0.87s
[2025-06-25 14:40:26,798] WARNING - __main__ - main.py:6093 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 14:40:26,799] INFO - __main__ - main.py:4018 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:40:26,799] WARNING - __main__ - main.py:6102 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 14:40:26,799] WARNING - __main__ - main.py:6109 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 14:40:26,800] WARNING - __main__ - main.py:6116 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 14:40:26,800] WARNING - __main__ - main.py:6117 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 14:40:26,801] WARNING - __main__ - main.py:6118 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 14:40:26,802] WARNING - __main__ - main.py:6119 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   3. AI state updates: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:40:26,803] WARNING - __main__ - main.py:6120 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:40:26,804] WARNING - __main__ - main.py:6123 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 💾 FINAL STATE APPLICATION:
[2025-06-25 14:40:26,805] WARNING - __main__ - main.py:6124 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 14:40:26,807] WARNING - __main__ - main.py:6125 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 💾   - State updates from AI: {'interaction_count': 1, 'current_question_index': 1, 'new_phase': 'diagnostic_start_probe'}
[2025-06-25 14:40:26,808] WARNING - __main__ - main.py:6126 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 14:40:26,808] WARNING - __main__ - main.py:6127 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 💾   - Phase change: False
[2025-06-25 14:40:26,809] INFO - __main__ - main.py:4050 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 14:40:26,810] INFO - __main__ - main.py:4051 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 14:40:26,810] INFO - __main__ - main.py:4052 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Current level: 5
[2025-06-25 14:40:26,811] INFO - __main__ - main.py:4053 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Question index: 0
[2025-06-25 14:40:26,811] INFO - __main__ - main.py:4054 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   First encounter: True
[2025-06-25 14:40:26,811] INFO - __main__ - main.py:4059 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Answers collected: 0
[2025-06-25 14:40:26,812] INFO - __main__ - main.py:4060 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Levels failed: 0
[2025-06-25 14:40:26,812] INFO - __main__ - main.py:4018 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:40:26,812] INFO - __main__ - main.py:4064 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   State update valid: True
[2025-06-25 14:40:26,813] INFO - __main__ - main.py:4071 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0]   Diagnostic complete: False
[2025-06-25 14:40:26,813] WARNING - __main__ - main.py:6140 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 1, current_q_index_for_prompt = 0
[2025-06-25 14:40:26,813] INFO - __main__ - main.py:6149 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 14:40:26,814] INFO - __main__ - main.py:6150 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 14:40:27,308] WARNING - __main__ - main.py:6195 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 14:40:27,309] WARNING - __main__ - main.py:6196 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Phase: diagnostic_start_probe
[2025-06-25 14:40:27,309] WARNING - __main__ - main.py:6197 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Probing Level: 5
[2025-06-25 14:40:27,310] WARNING - __main__ - main.py:6198 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Question Index: 1
[2025-06-25 14:40:27,310] WARNING - __main__ - main.py:6199 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Diagnostic Complete: False
[2025-06-25 14:40:27,310] WARNING - __main__ - main.py:6206 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Quiz Questions Saved: 0
[2025-06-25 14:40:27,311] WARNING - __main__ - main.py:6207 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Quiz Answers Saved: 0
[2025-06-25 14:40:27,312] WARNING - __main__ - main.py:6208 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Quiz Started: False
[2025-06-25 14:40:28,135] INFO - __main__ - main.py:6268 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ Updated existing session document: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:40:28,135] WARNING - __main__ - main.py:6269 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅ SESSION UPDATE COMPLETE:
[2025-06-25 14:40:28,135] WARNING - __main__ - main.py:6270 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Session ID: session_ef7cec30-ad93-434e-b6cb-d2ee4b3397e4
[2025-06-25 14:40:28,135] WARNING - __main__ - main.py:6271 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Phase transition: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 14:40:28,136] WARNING - __main__ - main.py:6272 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] ✅   - Interaction logged successfully
[2025-06-25 14:40:28,136] INFO - __main__ - main.py:11317 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 14:40:28,136] DEBUG - __main__ - main.py:2812 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 14:40:28,137] DEBUG - __main__ - main.py:6318 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] No final assessment data found in AI response
[2025-06-25 14:40:28,139] INFO - __main__ - main.py:6404 - [2bcf6cd9-3607-474d-a07d-8f33e86584c0] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 14:40:28,143] WARNING - __main__ - main.py:680 - High response time detected: 4.25s for enhance_content_api
[2025-06-25 14:40:28,144] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:40:28] "POST /api/enhance-content HTTP/1.1" 200 -
[2025-06-25 14:51:28,271] INFO - __main__ - main.py:5041 - REQUEST: GET http://127.0.0.1:5000/health -> endpoint: None
[2025-06-25 14:51:28,277] INFO - __main__ - main.py:5084 - Incoming request: {"request_id": "289be9e7-3f52-4ba0-bbcb-2ed91ee3666e", "timestamp": "2025-06-25T13:51:28.276429+00:00", "method": "GET", "path": "/health", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown"}
[2025-06-25 14:51:28,306] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 14:51:28] "[33mGET /health HTTP/1.1[0m" 404 -
