{"test_metadata": {"test_name": "Comprehensive End-to-End Lesson System Test", "student_id": "andrea_ugono_33305", "student_collection": "testing", "session_id": "e2e_test_1750857122", "started_at": "2025-06-25T14:12:02.964027", "completed_at": "2025-06-25T14:13:48.534370", "test_duration_minutes": 1.7595057166666666}, "success_criteria": {}, "performance_metrics": {"total_requests": 14, "avg_response_time": 6.267777749470302, "max_response_time": 9.321223735809326, "min_response_time": 4.67038893699646, "requests_under_2s": 0, "performance_threshold_met": false}, "ai_quality_analysis": {"total_assessments": 14, "avg_ai_quality": 61.642857142857146, "max_ai_quality": 67.25, "min_ai_quality": 52.0, "scores_above_70": 0, "quality_threshold_met": false}, "phase_coverage_analysis": {"expected_phases": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phases_completed": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phases_tracked": ["diagnostic_start_probe"], "coverage_percentage": 100.0, "missing_phases": [], "coverage_threshold_met": true}, "diagnostic_accuracy_validation": {"questions_asked": 5, "level_adjustments": [], "scoring_accuracy": false, "final_level": 1}, "state_update_compliance": {"total_blocks_found": 0, "blocks_found_in": [], "compliance_rate": 0.0}, "bug_analysis": {"total_bugs": 23, "bugs_by_severity": {"high": 14, "medium": 9}, "bugs_by_type": {"ai_instruction_compliance": 5, "performance": 9, "ai_quality": 9}, "critical_bugs": [], "high_priority_bugs": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 61.7% below 70.0% threshold in diagnostic"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.0% below 70.0% threshold in teaching_start"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 61.7% below 70.0% threshold in teaching"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 52.0% below 70.0% threshold in quiz_initiate"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 56.0% below 70.0% threshold in quiz_questions"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 59.8% below 70.0% threshold in quiz_results"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 67.2% below 70.0% threshold in conclusion_summary"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 56.0% below 70.0% threshold in final_assessment_pending"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.5% below 70.0% threshold in completed"}], "all_bugs": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "performance", "severity": "medium", "description": "Response time 5.49s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 61.7% below 70.0% threshold in diagnostic"}, {"type": "performance", "severity": "medium", "description": "Response time 4.67s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.0% below 70.0% threshold in teaching_start"}, {"type": "performance", "severity": "medium", "description": "Response time 5.12s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 61.7% below 70.0% threshold in teaching"}, {"type": "performance", "severity": "medium", "description": "Response time 5.18s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 52.0% below 70.0% threshold in quiz_initiate"}, {"type": "performance", "severity": "medium", "description": "Response time 4.81s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 56.0% below 70.0% threshold in quiz_questions"}, {"type": "performance", "severity": "medium", "description": "Response time 8.97s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 59.8% below 70.0% threshold in quiz_results"}, {"type": "performance", "severity": "medium", "description": "Response time 6.47s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 67.2% below 70.0% threshold in conclusion_summary"}, {"type": "performance", "severity": "medium", "description": "Response time 5.75s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 56.0% below 70.0% threshold in final_assessment_pending"}, {"type": "performance", "severity": "medium", "description": "Response time 6.30s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.5% below 70.0% threshold in completed"}]}, "errors_encountered": [], "raw_test_data": {"started_at": "2025-06-25T14:12:02.964027", "firebase_auth": true, "phase_coverage": 100.0, "phases_completed": ["diagnostic", "teaching_start", "teaching", "quiz_initiate", "quiz_questions", "quiz_results", "conclusion_summary", "final_assessment_pending", "completed"], "phase_transitions": ["diagnostic_start_probe"], "ai_quality_scores": [64.75, 64.75, 66.75, 61.0, 64.75, 61.74999999999999, 63.0, 61.74999999999999, 52.0, 56.00000000000001, 59.75, 67.25, 56.00000000000001, 63.5], "response_times": [9.321223735809326, 5.963460922241211, 5.352088928222656, 5.8392956256866455, 8.513173818588257, 5.487818956375122, 4.67038893699646, 5.11580753326416, 5.183655738830566, 4.806358337402344, 8.968931436538696, 6.4712207317352295, 5.754795551300049, 6.300668239593506], "diagnostic_completion": false, "diagnostic_scores": [], "state_update_blocks": [], "session_persistence": false, "errors": [], "bugs_found": [{"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 1"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 2"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 3"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 4"}, {"type": "ai_instruction_compliance", "severity": "high", "description": "Missing mandatory state update block in diagnostic question 5"}, {"type": "performance", "severity": "medium", "description": "Response time 5.49s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 61.7% below 70.0% threshold in diagnostic"}, {"type": "performance", "severity": "medium", "description": "Response time 4.67s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.0% below 70.0% threshold in teaching_start"}, {"type": "performance", "severity": "medium", "description": "Response time 5.12s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 61.7% below 70.0% threshold in teaching"}, {"type": "performance", "severity": "medium", "description": "Response time 5.18s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 52.0% below 70.0% threshold in quiz_initiate"}, {"type": "performance", "severity": "medium", "description": "Response time 4.81s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 56.0% below 70.0% threshold in quiz_questions"}, {"type": "performance", "severity": "medium", "description": "Response time 8.97s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 59.8% below 70.0% threshold in quiz_results"}, {"type": "performance", "severity": "medium", "description": "Response time 6.47s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 67.2% below 70.0% threshold in conclusion_summary"}, {"type": "performance", "severity": "medium", "description": "Response time 5.75s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 56.0% below 70.0% threshold in final_assessment_pending"}, {"type": "performance", "severity": "medium", "description": "Response time 6.30s exceeds 2.0s threshold"}, {"type": "ai_quality", "severity": "high", "description": "AI quality 63.5% below 70.0% threshold in completed"}], "success_criteria_met": {"phase_coverage_100": true, "diagnostic_completion": false, "avg_response_time_under_2s": false, "avg_ai_quality_over_70": false, "no_critical_bugs": true}, "performance_metrics": {}, "ai_quality_analysis": {}, "diagnostic_accuracy": {"questions_asked": 5, "level_adjustments": [], "scoring_accuracy": false, "final_level": 1}, "completed_at": "2025-06-25T14:13:48.534370"}, "recommendations": [{"priority": "HIGH", "category": "Performance", "issue": "Average response time 6.27s exceeds 2.0s threshold", "recommendation": "Optimize API response times through caching, database indexing, or code optimization"}, {"priority": "HIGH", "category": "AI Quality", "issue": "Average AI quality 61.6% below 70% threshold", "recommendation": "Improve AI instructor prompts, add personalization, and enhance educational effectiveness"}, {"priority": "MEDIUM", "category": "AI Compliance", "issue": "Missing mandatory AI state update blocks detected", "recommendation": "Ensure AI instructor generates proper state update blocks in required format"}]}