"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/classroom/page",{

/***/ "(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx":
/*!************************************************!*\
  !*** ./src/app/classroom/ClassroomContent.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_lesson_components_TutorChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/lesson-components/TutorChat */ \"(app-pages-browser)/./src/components/lesson-components/TutorChat.tsx\");\n/* harmony import */ var _hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useLessonTimer */ \"(app-pages-browser)/./src/hooks/useLessonTimer.ts\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(app-pages-browser)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-interaction-logger */ \"(app-pages-browser)/./src/hooks/use-interaction-logger.ts\");\n/* harmony import */ var _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/shadcn/card */ \"(app-pages-browser)/./src/components/shadcn/card.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/ErrorDisplay */ \"(app-pages-browser)/./src/components/ui/ErrorDisplay.tsx\");\n/* harmony import */ var _components_shadcn_LessonHeader__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/shadcn/LessonHeader */ \"(app-pages-browser)/./src/components/shadcn/LessonHeader.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_lesson_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/lesson-components/ErrorBoundary */ \"(app-pages-browser)/./src/components/lesson-components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_lesson_components_LevelAdjustmentHistory__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/lesson-components/LevelAdjustmentHistory */ \"(app-pages-browser)/./src/components/lesson-components/LevelAdjustmentHistory.tsx\");\n/* harmony import */ var _components_DiagnosticProgress__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/DiagnosticProgress */ \"(app-pages-browser)/./src/components/DiagnosticProgress.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n // Using simplified session hook\n\n\n\n\n\n\n\n\n\n\n\n // Import AxiosError\nconst AI_INTERACTION_ENDPOINT = '/api/enhance-content'; // Next.js API proxy for Flask's /api/enhance-content\nconst LESSON_PHASE_COMPLETED = \"completed\";\nconst LESSON_DURATION_MINUTES = 45; // Total lesson duration in minutes\n// Fallback Component for ErrorBoundary\nconst ErrorBoundaryFallback = (param)=>{\n    let { error, resetErrorBoundary } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n            className: \"border-destructive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                        className: \"text-destructive\",\n                        children: \"Rendering Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"An unexpected error occurred while rendering this part of the lesson.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 p-2 bg-red-50 text-red-700 rounded text-xs overflow-auto\",\n                            children: [\n                                error.message,\n                                \"\\\\n\",\n                                error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                            onClick: resetErrorBoundary,\n                            className: \"mt-4\",\n                            children: \"Try to Recover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ErrorBoundaryFallback;\nconst ClassroomContent = (param)=>{\n    let { sessionIdFromUrlProp, lessonRefProp, studentIdProp, countryProp, curriculumProp, gradeProp, levelProp, subjectProp } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const logInteraction = (0,_hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { toast } = (0,_app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const { user, isReady, getAuthHeaders, backendSessionId// THE Firestore-backed lesson session ID from context\n     } = (0,_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const chatBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const initialAiInteractionSentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false); // Tracks if the first system message has been sent\n    const handleAiInteractionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Ref to store handleAiInteraction\n    const [currentLessonPhase, setCurrentLessonPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isAiLoading, setIsAiLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // For AI response loading\n    const [isPageLoading, setIsPageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Overall page/initial setup loading\n    const [uiError, setUiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // For displaying errors in the UI\n    // Level tracking state for real-time adjustments\n    const [currentTeachingLevel, setCurrentTeachingLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [levelAdjustmentHistory, setLevelAdjustmentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Diagnostic progress tracking state\n    const [diagnosticProgress, setDiagnosticProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentQuestionIndex: 0,\n        totalQuestions: 5,\n        currentProbingLevel: 5,\n        questionsCompleted: 0,\n        isComplete: false\n    });\n    // Timer state\n    const [lessonStartTime, setLessonStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Timer logic moved to custom hook\n    const { timeRemaining, isTimerActive, setIsTimerActive, formatTime, startTimer, getTimerStatus } = (0,_hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__.useLessonTimer)({\n        lessonStartTime,\n        currentLessonPhase,\n        sessionIdFromUrlProp,\n        backendSessionId: backendSessionId || undefined,\n        lessonRef: lessonRefProp,\n        onTimeUp: {\n            \"ClassroomContent.useLessonTimer\": ()=>{\n                // This will be called when time is up\n                const sessionId = sessionIdFromUrlProp || backendSessionId;\n                if (sessionId) {\n                    handleAiInteraction('[System: Time is up! Completing lesson...]', true, sessionId);\n                }\n            }\n        }[\"ClassroomContent.useLessonTimer\"],\n        onQuizTransition: {\n            \"ClassroomContent.useLessonTimer\": ()=>{\n                // This will be called when forced quiz transition is triggered at 37.5 minutes\n                const sessionId = sessionIdFromUrlProp || backendSessionId;\n                if (sessionId) {\n                    logInteraction('forced_quiz_transition_triggered', {\n                        lessonRef: lessonRefProp,\n                        sessionId: sessionId,\n                        currentPhase: currentLessonPhase || 'teaching',\n                        timeRemaining: timeRemaining,\n                        uncoveredContent: 'Material not covered due to time constraints will be added to homework'\n                    });\n                    // Send system message to AI to trigger quiz and capture uncovered content as homework\n                    handleAiInteraction('[System: Time limit approaching - transition to quiz phase and capture any uncovered teaching material as homework assignments]', true, sessionId);\n                }\n            }\n        }[\"ClassroomContent.useLessonTimer\"]\n    });\n    const [lessonDetails, setLessonDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Chat and lesson state\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lessonEnded, setLessonEnded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Derive core identifiers from props\n    const lessonRef = lessonRefProp;\n    const studentId = studentIdProp; // This should be the Firebase UID of the student\n    // Handle lesson phase updates from server responses\n    const handleLessonPhaseUpdates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (response)=>{\n            try {\n                var _response_data, _response_data1, _response_data2, _response_data3, _response_data4, _response_data5, _response_data6;\n                console.log('[handleLessonPhaseUpdates] ==> ENHANCED DEBUGGING - PROCESSING RESPONSE FROM BACKEND');\n                console.log('[handleLessonPhaseUpdates] ==> FULL RAW RESPONSE:', JSON.stringify(response, null, 2));\n                console.log('[handleLessonPhaseUpdates] Raw response keys:', Object.keys(response || {}));\n                console.log('[handleLessonPhaseUpdates] Response.data keys:', Object.keys((response === null || response === void 0 ? void 0 : response.data) || {}));\n                // FORCE CONSOLE OUTPUT FOR REAL-TIME DEBUGGING\n                console.warn('🔥 FRONTEND PHASE UPDATE DEBUG:', {\n                    responseType: typeof response,\n                    hasData: !!(response === null || response === void 0 ? void 0 : response.data),\n                    dataKeys: (response === null || response === void 0 ? void 0 : response.data) ? Object.keys(response.data) : 'no data',\n                    currentFrontendPhase: currentLessonPhase\n                });\n                // CRITICAL FIX: Frontend defensive parsing to handle multiple backend field names\n                // Check multiple possible field locations for state updates\n                const serverStateUpdates = (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.state_updates) || (response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.parsed_state) || (response === null || response === void 0 ? void 0 : response.state_updates) || (response === null || response === void 0 ? void 0 : response.parsed_state);\n                let phaseFromServer = (response === null || response === void 0 ? void 0 : (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.current_phase) || (response === null || response === void 0 ? void 0 : response.current_phase); // From the main response body\n                let diagCompleteFromServer = response === null || response === void 0 ? void 0 : (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.diagnostic_complete;\n                let assessedLevelFromServer = response === null || response === void 0 ? void 0 : (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.assessed_level;\n                // ENHANCED: Check if this is a wrapped response (Flask returns {success: true, data: {...}})\n                if ((response === null || response === void 0 ? void 0 : response.success) && (response === null || response === void 0 ? void 0 : response.data)) {\n                    var _innerData_state_updates, _innerData_state_updates1, _innerData_state_updates2, _innerData_state_updates3;\n                    console.log('[handleLessonPhaseUpdates] 🎯 DETECTED WRAPPED RESPONSE from Flask');\n                    const innerData = response.data;\n                    // CRITICAL FIX: Backend returns current_phase at top level of data, not just in state_updates\n                    // Priority order: state_updates.new_phase > data.current_phase > data.new_phase\n                    phaseFromServer = ((_innerData_state_updates = innerData.state_updates) === null || _innerData_state_updates === void 0 ? void 0 : _innerData_state_updates.new_phase) || innerData.current_phase || innerData.new_phase || phaseFromServer;\n                    diagCompleteFromServer = innerData.diagnostic_complete || innerData.diagnostic_completed_this_session || ((_innerData_state_updates1 = innerData.state_updates) === null || _innerData_state_updates1 === void 0 ? void 0 : _innerData_state_updates1.diagnostic_completed_this_session) || diagCompleteFromServer;\n                    assessedLevelFromServer = innerData.assessed_level || innerData.assigned_level_for_teaching || ((_innerData_state_updates2 = innerData.state_updates) === null || _innerData_state_updates2 === void 0 ? void 0 : _innerData_state_updates2.assigned_level_for_teaching) || assessedLevelFromServer;\n                    console.log('[handleLessonPhaseUpdates] 🎯 EXTRACTED FROM WRAPPED RESPONSE:', {\n                        phaseFromServer,\n                        diagCompleteFromServer,\n                        assessedLevelFromServer,\n                        hasStateUpdates: !!innerData.state_updates,\n                        innerDataKeys: Object.keys(innerData)\n                    });\n                    // ENHANCED: Force console output to show what we found\n                    console.warn('🔥 PHASE EXTRACTION DEBUG:', {\n                        'innerData.current_phase': innerData.current_phase,\n                        'innerData.new_phase': innerData.new_phase,\n                        'innerData.state_updates?.new_phase': (_innerData_state_updates3 = innerData.state_updates) === null || _innerData_state_updates3 === void 0 ? void 0 : _innerData_state_updates3.new_phase,\n                        'final_phaseFromServer': phaseFromServer\n                    });\n                }\n                // Debug logging to track field locations\n                console.log('[handleLessonPhaseUpdates] DEBUG: Checking state update fields:', {\n                    'response.data.state_updates': !!(response === null || response === void 0 ? void 0 : (_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : _response_data5.state_updates),\n                    'response.data.parsed_state': !!(response === null || response === void 0 ? void 0 : (_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : _response_data6.parsed_state),\n                    'response.state_updates': !!(response === null || response === void 0 ? void 0 : response.state_updates),\n                    'response.parsed_state': !!(response === null || response === void 0 ? void 0 : response.parsed_state),\n                    'final_serverStateUpdates': !!serverStateUpdates,\n                    'phaseFromServer': phaseFromServer,\n                    'currentFrontendPhase': currentLessonPhase\n                });\n                if (serverStateUpdates && typeof serverStateUpdates === 'object') {\n                    console.log('[handleLessonPhaseUpdates] Found state updates:', Object.keys(serverStateUpdates));\n                    console.log('[handleLessonPhaseUpdates] FULL state updates object:', JSON.stringify(serverStateUpdates, null, 2));\n                    if (serverStateUpdates.new_phase) {\n                        phaseFromServer = serverStateUpdates.new_phase;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 PHASE TRANSITION DETECTED: \".concat(phaseFromServer));\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 Previous phase: \".concat(currentLessonPhase));\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 New phase: \".concat(phaseFromServer));\n                        // FORCE IMMEDIATE CONSOLE ALERT\n                        console.warn(\"\\uD83D\\uDE80 CRITICAL: PHASE CHANGING FROM \".concat(currentLessonPhase, \" TO \").concat(phaseFromServer));\n                    }\n                    if (serverStateUpdates.diagnostic_completed_this_session !== undefined) {\n                        diagCompleteFromServer = serverStateUpdates.diagnostic_completed_this_session;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDCCA Diagnostic completion status: \".concat(diagCompleteFromServer));\n                    }\n                    if (serverStateUpdates.assigned_level_for_teaching !== undefined) {\n                        assessedLevelFromServer = serverStateUpdates.assigned_level_for_teaching;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDFAF Teaching level assigned: \".concat(assessedLevelFromServer));\n                    }\n                    // Extract diagnostic-specific information for UI display\n                    if (serverStateUpdates.current_probing_level_number) {\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDCCB Current probing level: \".concat(serverStateUpdates.current_probing_level_number));\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>({\n                                    ...prev,\n                                    currentProbingLevel: serverStateUpdates.current_probing_level_number\n                                })\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.current_question_index !== undefined) {\n                        console.log(\"[handleLessonPhaseUpdates] ❓ Current question index: \".concat(serverStateUpdates.current_question_index));\n                        console.log(\"[handleLessonPhaseUpdates] ❓ Updating diagnostic progress with question index: \".concat(serverStateUpdates.current_question_index));\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    currentQuestionIndex: serverStateUpdates.current_question_index,\n                                    questionsCompleted: serverStateUpdates.current_question_index // Update both for consistency\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] ❓ Diagnostic progress after question index update:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.diagnostic_questions_completed) {\n                        console.log(\"[handleLessonPhaseUpdates] ✅ Questions completed: \".concat(serverStateUpdates.diagnostic_questions_completed));\n                        console.log(\"[handleLessonPhaseUpdates] ✅ Updating diagnostic progress with completed questions\");\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    questionsCompleted: serverStateUpdates.diagnostic_questions_completed\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] ✅ Diagnostic progress after completion update:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.diagnostic_completed_this_session) {\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Diagnostic phase completed!\");\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Marking diagnostic as complete in UI state\");\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    isComplete: true\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Final diagnostic progress state:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                }\n                if (phaseFromServer) {\n                    console.log('[handleLessonPhaseUpdates] 🎯 UPDATING LESSON PHASE TO:', phaseFromServer);\n                    console.log('[handleLessonPhaseUpdates] 🎯 Previous lesson phase was:', currentLessonPhase);\n                    // FORCE IMMEDIATE STATE UPDATE WITH DEBUGGING\n                    setCurrentLessonPhase({\n                        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prevPhase)=>{\n                            console.warn(\"\\uD83D\\uDD25 STATE UPDATE: PHASE CHANGING \".concat(prevPhase, \" → \").concat(phaseFromServer));\n                            return phaseFromServer;\n                        }\n                    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    // CRITICAL FIX: Handle diagnostic_start_probe phase properly\n                    if (phaseFromServer === 'diagnostic_start_probe') {\n                        console.log('[handleLessonPhaseUpdates] 🔍 DIAGNOSTIC START PROBE: Setting question index to 0');\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>({\n                                    ...prev,\n                                    currentQuestionIndex: 0,\n                                    questionsCompleted: 0,\n                                    isComplete: false\n                                })\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    console.log('[handleLessonPhaseUpdates] 🎯 Phase update completed');\n                    // ADDITIONAL: Force a re-render by updating a dummy state\n                    setTimeout({\n                        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": ()=>{\n                            console.warn(\"\\uD83D\\uDD25 POST-UPDATE CHECK: currentLessonPhase should now be \".concat(phaseFromServer));\n                        }\n                    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"], 100);\n                } else {\n                    console.warn('[handleLessonPhaseUpdates] ⚠️ NO PHASE UPDATE FOUND IN RESPONSE');\n                    console.warn('[handleLessonPhaseUpdates] ⚠️ Response structure might be different than expected');\n                }\n                if (diagCompleteFromServer !== undefined) {\n                    console.log('[handleLessonPhaseUpdates] Diagnostic complete status:', diagCompleteFromServer);\n                // Potentially set a local state like setIsDiagnosticComplete(diagCompleteFromServer);\n                }\n                if (assessedLevelFromServer !== undefined) {\n                    console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);\n                // Potentially set a local state like setTeachingLevel(assessedLevelFromServer);\n                }\n                if (phaseFromServer === LESSON_PHASE_COMPLETED) {\n                    var _response_data7;\n                    console.log('Lesson completed successfully (from phase update)');\n                    setLessonEnded(true);\n                    // Potentially show download link for notes if response.data.notes_download_url exists\n                    if (response === null || response === void 0 ? void 0 : (_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : _response_data7.notes_download_url) {\n                    // Show a button or link\n                    }\n                }\n                // ... other phase-specific UI logic ...\n                // COMPREHENSIVE SUMMARY LOGGING\n                console.log('[handleLessonPhaseUpdates] ==> SUMMARY OF STATE UPDATES:');\n                console.log('[handleLessonPhaseUpdates] Phase changed:', phaseFromServer ? \"\".concat(currentLessonPhase, \" → \").concat(phaseFromServer) : 'No change');\n                console.log('[handleLessonPhaseUpdates] Diagnostic complete:', diagCompleteFromServer);\n                console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);\n                console.log('[handleLessonPhaseUpdates] Current diagnostic progress:', diagnosticProgress);\n                console.log('[handleLessonPhaseUpdates] ==> END OF PROCESSING');\n            } catch (error) {\n                console.error('Error handling lesson phase update:', error);\n                console.error('Error details:', error);\n                setUiError('Failed to synchronize lesson state. Please try again.');\n            }\n        }\n    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"], [\n        setCurrentLessonPhase,\n        setLessonEnded,\n        setUiError,\n        currentLessonPhase,\n        diagnosticProgress\n    ]);\n    // Helper function to extract enhanced content from AI responses\n    const extractEnhancedContent = (response)=>{\n        var _response_data, _response_data_data, _response_data1;\n        if (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.enhanced_content) {\n            return response.data.enhanced_content;\n        }\n        if (response === null || response === void 0 ? void 0 : response.enhanced_content) {\n            return response.enhanced_content;\n        }\n        if (response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.enhanced_content) {\n            return response.data.data.enhanced_content;\n        }\n        const findContent = (obj)=>{\n            if (typeof obj === 'string') return obj;\n            if (Array.isArray(obj)) {\n                for (const item of obj){\n                    const found = findContent(item);\n                    if (found) return found;\n                }\n            } else if (obj && typeof obj === 'object') {\n                for(const key in obj){\n                    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                        const found = findContent(obj[key]);\n                        if (found) return found;\n                    }\n                }\n            }\n            return null;\n        };\n        return findContent(response) || \"I'm sorry, I couldn't process that response. Please try again.\";\n    };\n    // Helper function to get user-friendly error messages\n    const getErrorMessage = (error)=>{\n        if (axios__WEBPACK_IMPORTED_MODULE_17__[\"default\"].isAxiosError(error)) {\n            const axiosError = error;\n            if (axiosError.response) {\n                var _axiosError_response_data;\n                // Server responded with a status code outside 2xx\n                if (axiosError.response.status === 401) {\n                    return \"Your session has expired. Please log in again.\";\n                }\n                if (axiosError.response.status === 403) {\n                    return \"You don't have permission to perform this action.\";\n                }\n                if (axiosError.response.status === 404) {\n                    return \"The requested resource was not found.\";\n                }\n                if (axiosError.response.status === 429) {\n                    return \"Too many requests. Please wait a moment and try again.\";\n                }\n                if (axiosError.response.status >= 500) {\n                    return \"Our servers are experiencing issues. Please try again later.\";\n                }\n                return ((_axiosError_response_data = axiosError.response.data) === null || _axiosError_response_data === void 0 ? void 0 : _axiosError_response_data.message) || axiosError.message;\n            }\n            if (axiosError.request) {\n                // Request was made but no response received\n                return \"No response from server. Please check your internet connection.\";\n            }\n        }\n        // Handle other error types\n        if (error instanceof Error) {\n            return error.message;\n        }\n        if (typeof error === 'string') {\n            return error;\n        }\n        return \"An unknown error occurred. Please try again.\";\n    };\n    // Define handleAiInteraction first to avoid initialization issues\n    const handleAiInteraction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ClassroomContent.useCallback[handleAiInteraction]\": async function(messageContent) {\n            let isSystemMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, currentSessionIdForCall = arguments.length > 2 ? arguments[2] : void 0;\n            // Priority: 1. Explicitly provided ID 2. URL session ID 3. Context session ID\n            const sessionIdToUse = currentSessionIdForCall || sessionIdFromUrlProp || backendSessionId;\n            const logContext = \"[ClassroomContent handleAiInteraction] isSystem: \".concat(isSystemMessage, \", SessionToUse: \").concat(sessionIdToUse, \", LessonRef: \").concat(lessonRef, \", StudentId: \").concat(studentId);\n            console.log(logContext);\n            // Input validation\n            if (typeof sessionIdToUse !== 'string' || !sessionIdToUse.trim()) {\n                const error = \"Session ID is invalid ('\".concat(sessionIdToUse, \"') from context/prop.\");\n                setUiError(error);\n                console.error(logContext, \"CRITICAL ERROR:\", error);\n                toast({\n                    title: \"Session Error\",\n                    description: \"A valid session ID is required to continue the lesson. Please try refreshing.\",\n                    variant: \"destructive\"\n                });\n                setIsAiLoading(false);\n                return false;\n            }\n            if (!lessonRef || !studentId) {\n                const error = \"Lesson Reference or Student ID prop missing.\";\n                setUiError(error);\n                console.error(logContext, \"CRITICAL ERROR:\", error);\n                toast({\n                    title: \"Configuration Error\",\n                    description: error,\n                    variant: \"destructive\"\n                });\n                setIsAiLoading(false);\n                return false;\n            }\n            setIsAiLoading(true);\n            setUiError(null);\n            logInteraction(isSystemMessage ? 'system_message_ai' : 'user_message_ai', {\n                lessonRef,\n                sessionId: sessionIdToUse,\n                message: messageContent.substring(0, 100)\n            });\n            // Add user message to chat history if not a system message\n            if (!isSystemMessage) {\n                const currentUserMessage = {\n                    role: 'user',\n                    content: messageContent,\n                    timestamp: new Date().toISOString()\n                };\n                console.log(\"[ClassroomContent] \\uD83D\\uDC64 Adding user message to chat:\", {\n                    messagePreview: messageContent.substring(0, 50) + '...',\n                    messageLength: messageContent.length,\n                    currentPhase: currentLessonPhase,\n                    chatHistoryLength: chatHistory.length\n                });\n                setChatHistory({\n                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                            ...prev,\n                            currentUserMessage\n                        ]\n                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n            }\n            try {\n                const authHeaders = getAuthHeaders(backendSessionId);\n                if (!authHeaders['Authorization']) {\n                    throw new Error(\"Authentication token unavailable for AI interaction.\");\n                }\n                const requestBody = {\n                    student_id: studentId,\n                    lesson_ref: lessonRef,\n                    content_to_enhance: messageContent,\n                    country: countryProp || 'Nigeria',\n                    curriculum: curriculumProp || 'National Curriculum',\n                    grade: gradeProp,\n                    level: levelProp,\n                    subject: subjectProp,\n                    session_id: sessionIdToUse,\n                    chat_history: isSystemMessage ? [] : chatHistory.slice(-8)\n                };\n                console.log(\"[ClassroomContent] AI Interaction Request:\", {\n                    endpoint: AI_INTERACTION_ENDPOINT,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: {\n                        ...requestBody,\n                        chat_history: \"[\".concat(requestBody.chat_history.length, \" messages]\")\n                    }\n                });\n                const MAX_RETRIES = 2;\n                let lastError;\n                for(let attempt = 0; attempt <= MAX_RETRIES; attempt++){\n                    try {\n                        var _axiosResponse_data;\n                        const axiosResponse = await axios__WEBPACK_IMPORTED_MODULE_17__[\"default\"].post(AI_INTERACTION_ENDPOINT, requestBody, {\n                            headers: {\n                                ...authHeaders,\n                                'Content-Type': 'application/json'\n                            },\n                            timeout: 90000,\n                            validateStatus: {\n                                \"ClassroomContent.useCallback[handleAiInteraction]\": ()=>true // Always resolve the promise\n                            }[\"ClassroomContent.useCallback[handleAiInteraction]\"]\n                        });\n                        if (axiosResponse.status >= 200 && axiosResponse.status < 300) {\n                            let result;\n                            try {\n                                var _result_data, _result_data1, _result_data_state_updates, _result_data2, _result_state_updates, _result_state_updates1, _result_state_updates2, _result_data3;\n                                result = typeof axiosResponse.data === 'string' ? JSON.parse(axiosResponse.data) : axiosResponse.data;\n                                console.log('[ClassroomContent] Successfully parsed response:', result);\n                                // CRITICAL DEBUG: Show exact response structure for phase sync debugging\n                                console.warn('🔥 FRONTEND API RESPONSE STRUCTURE DEBUG:');\n                                console.warn('🔥 Response keys:', Object.keys(result || {}));\n                                console.warn('🔥 Response.data keys:', Object.keys((result === null || result === void 0 ? void 0 : result.data) || {}));\n                                console.warn('🔥 Response.data.state_updates keys:', Object.keys((result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.state_updates) || {}));\n                                console.warn('🔥 Current phase in response.data:', result === null || result === void 0 ? void 0 : (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.current_phase);\n                                console.warn('🔥 New phase in state_updates:', result === null || result === void 0 ? void 0 : (_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : (_result_data_state_updates = _result_data2.state_updates) === null || _result_data_state_updates === void 0 ? void 0 : _result_data_state_updates.new_phase);\n                                console.warn('🔥 Full result structure (first 500 chars):', JSON.stringify(result, null, 2).substring(0, 500));\n                                const enhancedContent = extractEnhancedContent(result);\n                                // Check for level adjustment notifications\n                                if (result === null || result === void 0 ? void 0 : (_result_state_updates = result.state_updates) === null || _result_state_updates === void 0 ? void 0 : _result_state_updates.level_adjustment_made) {\n                                    const adjustment = result.state_updates.level_adjustment_made;\n                                    console.log('[ClassroomContent] Level adjustment detected:', adjustment);\n                                    // Update current teaching level\n                                    setCurrentTeachingLevel(adjustment.to_level);\n                                    // Add to adjustment history\n                                    setLevelAdjustmentHistory({\n                                        \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                                                ...prev,\n                                                {\n                                                    timestamp: adjustment.timestamp,\n                                                    direction: adjustment.direction,\n                                                    fromLevel: adjustment.from_level,\n                                                    toLevel: adjustment.to_level,\n                                                    confidence: adjustment.confidence_score\n                                                }\n                                            ].slice(-10)\n                                    }[\"ClassroomContent.useCallback[handleAiInteraction]\"]); // Keep only last 10 adjustments\n                                    // Show level adjustment notification\n                                    toast({\n                                        title: \"Teaching Level \".concat(adjustment.direction === 'up' ? 'Increased' : 'Decreased'),\n                                        description: \"I've adjusted the lesson difficulty from Level \".concat(adjustment.from_level, \" to Level \").concat(adjustment.to_level, \" to better match your learning pace.\"),\n                                        variant: adjustment.direction === 'up' ? 'default' : 'destructive',\n                                        duration: 8000\n                                    });\n                                    // Log the level adjustment\n                                    logInteraction('level_adjustment_notification', {\n                                        lessonRef,\n                                        sessionId: sessionIdToUse,\n                                        direction: adjustment.direction,\n                                        fromLevel: adjustment.from_level,\n                                        toLevel: adjustment.to_level,\n                                        confidence: adjustment.confidence_score,\n                                        reasoning: adjustment.reasoning,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                }\n                                // Check for initial or updated teaching level in state updates\n                                if ((result === null || result === void 0 ? void 0 : (_result_state_updates1 = result.state_updates) === null || _result_state_updates1 === void 0 ? void 0 : _result_state_updates1.assigned_level_for_teaching) && currentTeachingLevel === null) {\n                                    setCurrentTeachingLevel(result.state_updates.assigned_level_for_teaching);\n                                }\n                                const aiMessage = {\n                                    role: 'assistant',\n                                    content: enhancedContent,\n                                    timestamp: new Date().toISOString()\n                                };\n                                console.log(\"[ClassroomContent] \\uD83D\\uDCAC Adding AI message to chat history:\", {\n                                    contentPreview: enhancedContent.substring(0, 100) + '...',\n                                    messageLength: enhancedContent.length,\n                                    currentChatLength: chatHistory.length,\n                                    hasStateUpdates: !!(result === null || result === void 0 ? void 0 : result.state_updates),\n                                    hasPhaseUpdate: !!((result === null || result === void 0 ? void 0 : (_result_state_updates2 = result.state_updates) === null || _result_state_updates2 === void 0 ? void 0 : _result_state_updates2.new_phase) || (result === null || result === void 0 ? void 0 : (_result_data3 = result.data) === null || _result_data3 === void 0 ? void 0 : _result_data3.current_phase))\n                                });\n                                setChatHistory({\n                                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>{\n                                        const newHistory = [\n                                            ...prev,\n                                            aiMessage\n                                        ];\n                                        console.log(\"[ClassroomContent] \\uD83D\\uDCDD Chat history updated. Total messages: \".concat(newHistory.length));\n                                        return newHistory;\n                                    }\n                                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                                handleLessonPhaseUpdates(result);\n                                return true;\n                            } catch (parseError) {\n                                throw new Error(\"Failed to parse server response: \".concat(parseError.message));\n                            }\n                        }\n                        if (axiosResponse.status === 401) {\n                            throw new Error('Authentication failed. Please log in again.');\n                        } else if (axiosResponse.status === 429) {\n                            const retryAfter = axiosResponse.headers['retry-after'] || 5;\n                            if (attempt < MAX_RETRIES) {\n                                await new Promise({\n                                    \"ClassroomContent.useCallback[handleAiInteraction]\": (resolve)=>setTimeout(resolve, Number(retryAfter) * 1000)\n                                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                                continue;\n                            }\n                            throw new Error('Server is busy. Please try again later.');\n                        }\n                        const errorMessage = ((_axiosResponse_data = axiosResponse.data) === null || _axiosResponse_data === void 0 ? void 0 : _axiosResponse_data.message) || axiosResponse.statusText || \"Request failed with status \".concat(axiosResponse.status);\n                        throw new Error(errorMessage);\n                    } catch (error) {\n                        lastError = error;\n                        if (attempt < MAX_RETRIES) {\n                            const backoffTime = Math.pow(2, attempt) * 1000;\n                            console.warn(\"Attempt \".concat(attempt + 1, \" failed, retrying in \").concat(backoffTime, \"ms...\"), error);\n                            await new Promise({\n                                \"ClassroomContent.useCallback[handleAiInteraction]\": (resolve)=>setTimeout(resolve, backoffTime)\n                            }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                        }\n                    }\n                }\n                throw lastError || new Error('Request failed after multiple attempts');\n                // This code is unreachable due to the throw statement above\n                // Keeping it for reference but it won't be executed\n                console.warn('This code should not be reachable - check for unreachable code');\n                return false;\n            } catch (error) {\n                var _error_message, _error_message1;\n                const errorMessage = getErrorMessage(error);\n                console.error(\"[ClassroomContent] Error during AI interaction:\", errorMessage, \"\\nFull error:\", error);\n                // Update UI with error state\n                setUiError(errorMessage);\n                // Add error message to chat for better UX\n                const errorMessageObj = {\n                    role: 'assistant',\n                    content: \"I'm sorry, I encountered an error: \".concat(errorMessage),\n                    timestamp: new Date().toISOString(),\n                    status: 'error'\n                };\n                setChatHistory({\n                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                            ...prev,\n                            errorMessageObj\n                        ]\n                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                // Show toast for non-timeout errors to avoid duplicate messages\n                if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && !((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Network Error'))) {\n                    toast({\n                        title: \"AI Service Error\",\n                        description: errorMessage,\n                        variant: \"destructive\",\n                        duration: 10000\n                    });\n                }\n                // Log the error for debugging\n                logInteraction('ai_interaction_error', {\n                    lessonRef,\n                    sessionId: sessionIdToUse,\n                    error: errorMessage,\n                    stack: error.stack,\n                    timestamp: new Date().toISOString()\n                });\n                return false;\n            } finally{\n                // Always ensure loading state is reset\n                setIsAiLoading(false);\n            }\n        }\n    }[\"ClassroomContent.useCallback[handleAiInteraction]\"], [\n        sessionIdFromUrlProp,\n        backendSessionId || undefined,\n        lessonRef,\n        studentId,\n        countryProp,\n        curriculumProp,\n        gradeProp,\n        levelProp,\n        subjectProp,\n        getAuthHeaders,\n        toast,\n        logInteraction,\n        chatHistory,\n        timeRemaining // Add timeRemaining to dependencies\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            const effectRunId = Date.now(); // For unique logging per run\n            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Running. Deps state:\"), {\n                sessionIdFromUrlProp,\n                backendSessionId,\n                lessonRef,\n                studentId,\n                isReady,\n                initialAiSent: initialAiInteractionSentRef.current,\n                chatHistoryLength: chatHistory.length,\n                isPageLoading\n            });\n            if (initialAiInteractionSentRef.current) {\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial AI interaction already attempted/sent. Current page loading: \").concat(isPageLoading, \".\"));\n                // If setup was done and page is still loading, ensure it stops.\n                if (isPageLoading) setIsPageLoading(false);\n                return;\n            }\n            if (!isReady) {\n                console.warn(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Waiting for context (ready: \").concat(isReady, \").\"));\n                if (!isPageLoading) setIsPageLoading(true); // Keep loading screen if waiting for these\n                return;\n            }\n            // Once user session and context are ready, validate critical props and session ID\n            if (!lessonRef || !studentId || !(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {\n                let missingInfo = [];\n                if (!lessonRef) missingInfo.push(\"lessonRefProp\");\n                if (!studentId) missingInfo.push(\"studentIdProp\");\n                if (!(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {\n                    missingInfo.push(\"valid sessionIdFromUrlProp (received: '\".concat(sessionIdFromUrlProp, \"', type: \").concat(typeof sessionIdFromUrlProp, \")\"));\n                }\n                const errorMessage = \"Critical info missing for ClassroomContent init: \".concat(missingInfo.join(', '), \". Please ensure the lesson was started correctly.\");\n                console.error(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Prerequisite check failed:\"), errorMessage, {\n                    lessonRefFromProp: lessonRef,\n                    studentIdFromProp: studentId,\n                    contextSessionId: backendSessionId\n                });\n                setUiError(errorMessage);\n                toast({\n                    title: \"Lesson Load Error\",\n                    description: errorMessage,\n                    variant: \"destructive\",\n                    duration: 10000\n                });\n                setIsPageLoading(false);\n                initialAiInteractionSentRef.current = true; // Mark as \"attempted\" to prevent loops\n                return;\n            }\n            // All prerequisites are met: lessonRef, studentId from props, and sessionIdFromUrlProp are valid.\n            // The initialSetupTriggeredRef is primarily to ensure the initial AI message is sent only once.\n            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] All prerequisites met. Using sessionIdFromUrlProp: '\").concat(sessionIdFromUrlProp, \"', lessonRef: '\").concat(lessonRef, \"'\"));\n            // Ensure isPageLoading is true before we potentially make an async call or set lessonDetails\n            if (!isPageLoading) setIsPageLoading(true);\n            if (!lessonDetails) {\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Populating lessonDetails.\"));\n                setLessonDetails({\n                    title: \"Lesson: \".concat(lessonRef),\n                    subject: subjectProp || 'N/A',\n                    grade: gradeProp || 'N/A',\n                    level: levelProp || 'N/A',\n                    country: countryProp || 'N/A',\n                    curriculum: curriculumProp || 'N/A',\n                    lessonRef: lessonRef\n                });\n            }\n            // Send initial system message if chat is empty and it hasn't been sent yet\n            if (chatHistory.length === 0 && !initialAiInteractionSentRef.current) {\n                initialAiInteractionSentRef.current = true;\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Triggering initial AI interaction (Diagnostic Start Message) with sessionIdFromUrlProp: \").concat(sessionIdFromUrlProp));\n                // CRITICAL FIX: Send diagnostic-specific message instead of generic system message\n                handleAiInteraction(\"Start diagnostic assessment\", true, sessionIdFromUrlProp).then({\n                    \"ClassroomContent.useEffect\": (success)=>{\n                        if (success) {\n                            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial system message AI interaction completed successfully.\"));\n                        } else {\n                            console.warn(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial system message AI interaction reported failure.\"));\n                        }\n                    }\n                }[\"ClassroomContent.useEffect\"]).catch({\n                    \"ClassroomContent.useEffect\": (err)=>{\n                        console.error(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Promise rejected from initial AI interaction:\"), err.message);\n                    }\n                }[\"ClassroomContent.useEffect\"]).finally({\n                    \"ClassroomContent.useEffect\": ()=>{\n                        setIsPageLoading(false);\n                    }\n                }[\"ClassroomContent.useEffect\"]);\n            } else {\n                // Initial message already sent or chat not empty, just ensure loading is false\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial AI message condition not met (chatHistory: \").concat(chatHistory.length, \", initialSentRef: \").concat(initialAiInteractionSentRef.current, \"). Setting page loading false.\"));\n                setIsPageLoading(false);\n            }\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        // Key dependencies that trigger re-evaluation of initial setup:\n        sessionIdFromUrlProp,\n        backendSessionId || undefined,\n        lessonRef,\n        studentId,\n        isReady,\n        // Other dependencies that, if they change, might necessitate re-evaluation or are used:\n        subjectProp,\n        gradeProp,\n        levelProp,\n        countryProp,\n        curriculumProp,\n        chatHistory.length,\n        handleAiInteraction,\n        lessonDetails,\n        isPageLoading,\n        toast // Stable\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            var _chatBottomRef_current;\n            (_chatBottomRef_current = chatBottomRef.current) === null || _chatBottomRef_current === void 0 ? void 0 : _chatBottomRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        chatHistory\n    ]);\n    // DEBUG: Monitor phase changes to ensure state updates are working\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            console.warn('🎯 PHASE CHANGE DETECTED:', {\n                previousPhase: 'tracked separately',\n                currentPhase: currentLessonPhase,\n                timestamp: new Date().toISOString()\n            });\n            // Force console alert for phase changes\n            if (currentLessonPhase) {\n                console.warn(\"\\uD83D\\uDE80 FRONTEND PHASE IS NOW: \".concat(currentLessonPhase));\n            }\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        currentLessonPhase\n    ]);\n    // --- Render Logic ---\n    if (isPageLoading && !initialAiInteractionSentRef.current && !uiError) {\n        console.log(\"[ClassroomContent Render] Showing loading spinner:\", {\n            isPageLoading,\n            initialAiSent: initialAiInteractionSentRef.current,\n            uiError\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"large\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 75\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-2\",\n                    children: \"Initializing Classroom...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 106\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 839,\n            columnNumber: 16\n        }, undefined);\n    }\n    if (uiError) {\n        console.log(\"[ClassroomContent Render] Showing UI error display:\", uiError);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-center items-center h-screen p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    title: \"Lesson Error\",\n                    message: uiError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 846,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    onClick: ()=>router.push('/dashboard'),\n                    className: \"mt-4\",\n                    children: \"Go to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 845,\n            columnNumber: 13\n        }, undefined);\n    }\n    const currentSessionId = sessionIdFromUrlProp || backendSessionId;\n    if (!currentSessionId || !lessonRef || !studentId || !lessonDetails) {\n        const missingRenderData = {\n            sessionIdFromUrl: sessionIdFromUrlProp,\n            contextSessionId: backendSessionId,\n            propLessonRef: lessonRef,\n            propStudentId: studentId,\n            currentLessonDetails: lessonDetails\n        };\n        console.error(\"[ClassroomContent Render] Critical data missing just before render. This indicates a logic flow issue.\", missingRenderData);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-center items-center h-screen p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    title: \"Content Load Error\",\n                    message: \"Essential lesson data is still missing after initialization attempts. Please try returning to the dashboard and starting the lesson again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    onClick: ()=>router.push('/dashboard'),\n                    className: \"mt-4\",\n                    children: \"Go to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 863,\n            columnNumber: 13\n        }, undefined);\n    }\n    // All checks passed, render the main classroom UI\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundaryFallback, {\n            error: new Error('Component error'),\n            resetErrorBoundary: ()=>window.location.reload()\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 873,\n            columnNumber: 30\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_LessonHeader__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    lessonTitle: lessonDetails.title,\n                    subjectName: lessonDetails.subject,\n                    gradeLevel: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.formatGradeLevelForDisplay)(lessonDetails.grade),\n                    currentTeachingLevel: currentTeachingLevel,\n                    levelAdjustmentHistory: levelAdjustmentHistory,\n                    onEnd: ()=>{\n                        if (backendSessionId && typeof backendSessionId === 'string' && lessonRefProp && studentIdProp) {\n                            logInteraction('lesson_ended_by_user', {\n                                lessonRef: lessonRefProp,\n                                sessionId: backendSessionId,\n                                timeElapsed: LESSON_DURATION_MINUTES * 60 - timeRemaining,\n                                timeRemaining\n                            });\n                        }\n                        router.push('/dashboard');\n                    },\n                    connectionStatus: isAiLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 893,\n                        columnNumber: 35\n                    }, void 0) : currentLessonPhase === LESSON_PHASE_COMPLETED ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-green-500 text-xs font-semibold\",\n                        children: [\n                            \"Lesson Complete! \",\n                            formatTime(timeRemaining),\n                            \" remaining\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 25\n                    }, void 0) : uiError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 text-xs font-semibold\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 25\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-500 text-xs font-semibold\",\n                                        children: [\n                                            formatTime(timeRemaining),\n                                            \" remaining\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    (()=>{\n                                        const timerStatus = getTimerStatus();\n                                        if (timerStatus.isInQuizPhase) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-600 text-xs\",\n                                                children: \"Quiz Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 48\n                                            }, void 0);\n                                        } else if (timerStatus.timeUntilQuizTransition <= 300) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-500 text-xs\",\n                                                children: [\n                                                    \"Quiz in \",\n                                                    formatTime(timerStatus.timeUntilQuizTransition)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 48\n                                            }, void 0);\n                                        }\n                                        return null;\n                                    })()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 29\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-2 bg-gray-200 rounded-full overflow-hidden relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full \".concat(timeRemaining > LESSON_DURATION_MINUTES * 60 * 0.5 ? 'bg-green-500' : timeRemaining > LESSON_DURATION_MINUTES * 60 * 0.25 ? 'bg-yellow-500' : 'bg-red-500'),\n                                        style: {\n                                            width: \"\".concat(Math.max(5, timeRemaining / (LESSON_DURATION_MINUTES * 60) * 100), \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 w-0.5 h-full bg-amber-500 z-10\",\n                                        style: {\n                                            left: \"\".concat(37.5 / 45 * 100, \"%\")\n                                        },\n                                        title: \"Quiz transition point (37.5 min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 33\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 29\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 875,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"hidden md:block w-64 lg:w-72 xl:w-1/4 p-4 border-r overflow-y-auto bg-slate-50 dark:bg-slate-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"shadow-md mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                className: \"text-lg text-slate-700 dark:text-slate-200\",\n                                                children: \"Lesson Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                            className: \"space-y-1.5 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Topic:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Subject:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.subject\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Grade:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.grade\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Curriculum:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.curriculum\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-slate-200 dark:border-slate-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Phase:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-blue-600 dark:text-blue-400\",\n                                                            children: currentLessonPhase || 'Initializing...'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 56\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 953,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Session ID:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: sessionIdFromUrlProp || backendSessionId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 61\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiagnosticProgress__WEBPACK_IMPORTED_MODULE_16__.DiagnosticProgress, {\n                                    currentQuestionIndex: diagnosticProgress.currentQuestionIndex,\n                                    totalQuestions: diagnosticProgress.totalQuestions,\n                                    currentProbingLevel: diagnosticProgress.currentProbingLevel,\n                                    questionsCompleted: diagnosticProgress.questionsCompleted,\n                                    currentPhase: currentLessonPhase,\n                                    isComplete: diagnosticProgress.isComplete,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 21\n                                }, undefined),\n                                levelAdjustmentHistory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_LevelAdjustmentHistory__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    adjustments: levelAdjustmentHistory,\n                                    currentLevel: currentTeachingLevel,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 flex flex-col bg-white dark:bg-slate-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_TutorChat__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    lessonRef: lessonRef,\n                                    studentId: studentId,\n                                    sessionId: sessionIdFromUrlProp || backendSessionId || '',\n                                    chatMessages: chatHistory,\n                                    onSendMessage: handleAiInteraction,\n                                    isProcessing: isAiLoading,\n                                    error: uiError,\n                                    getAuthHeaders: getAuthHeaders,\n                                    lessonTitle: lessonDetails === null || lessonDetails === void 0 ? void 0 : lessonDetails.title,\n                                    role: \"instructor\" // or \"tutor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 980,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatBottomRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 979,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 874,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n        lineNumber: 873,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassroomContent, \"iSxiD6RNBz22xxxTSTnUfdo3bEA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession,\n        _hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__.useLessonTimer\n    ];\n});\n_c1 = ClassroomContent;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClassroomContent);\nvar _c, _c1;\n$RefreshReg$(_c, \"ErrorBoundaryFallback\");\n$RefreshReg$(_c1, \"ClassroomContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx\n"));

/***/ })

});