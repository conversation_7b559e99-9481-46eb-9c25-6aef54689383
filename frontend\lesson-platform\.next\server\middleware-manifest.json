{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7ImIN44J4Jd7rlCNO8WM59ucSAs4KztKRvmVS0Oz1GE="}}}, "functions": {}, "sortedMiddleware": ["/"]}