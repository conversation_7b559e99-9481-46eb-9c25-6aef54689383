/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUF5SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/ClientOnly.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/ClientOnly.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientOnly)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// This component is used to render content only on the client side\n// to avoid hydration errors with components that use browser-specific APIs\nfunction ClientOnly(param) {\n    let { children } = param;\n    _s();\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientOnly.useEffect\": ()=>{\n            setHasMounted(true);\n        }\n    }[\"ClientOnly.useEffect\"], []);\n    if (!hasMounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ClientOnly, \"aiSd/DQPOnbbLLZZL0Xv/KtPBDg=\");\n_c = ClientOnly;\nvar _c;\n$RefreshReg$(_c, \"ClientOnly\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29tcG9uZW50cy9DbGllbnRPbmx5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFdUQ7QUFNdkQsbUVBQW1FO0FBQ25FLDJFQUEyRTtBQUM1RCxTQUFTRSxXQUFXLEtBQTZCO1FBQTdCLEVBQUVDLFFBQVEsRUFBbUIsR0FBN0I7O0lBQ2pDLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHSiwrQ0FBUUEsQ0FBQztJQUU3Q0QsZ0RBQVNBO2dDQUFDO1lBQ1JLLGNBQWM7UUFDaEI7K0JBQUcsRUFBRTtJQUVMLElBQUksQ0FBQ0QsWUFBWTtRQUNmLE9BQU87SUFDVDtJQUVBLHFCQUFPO2tCQUFHRDs7QUFDWjtHQVp3QkQ7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxjb21wb25lbnRzXFxDbGllbnRPbmx5LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIENsaWVudE9ubHlQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbi8vIFRoaXMgY29tcG9uZW50IGlzIHVzZWQgdG8gcmVuZGVyIGNvbnRlbnQgb25seSBvbiB0aGUgY2xpZW50IHNpZGVcbi8vIHRvIGF2b2lkIGh5ZHJhdGlvbiBlcnJvcnMgd2l0aCBjb21wb25lbnRzIHRoYXQgdXNlIGJyb3dzZXItc3BlY2lmaWMgQVBJc1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2xpZW50T25seSh7IGNoaWxkcmVuIH06IENsaWVudE9ubHlQcm9wcykge1xuICBjb25zdCBbaGFzTW91bnRlZCwgc2V0SGFzTW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRIYXNNb3VudGVkKHRydWUpO1xuICB9LCBbXSk7XG5cbiAgaWYgKCFoYXNNb3VudGVkKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiQ2xpZW50T25seSIsImNoaWxkcmVuIiwiaGFzTW91bnRlZCIsInNldEhhc01vdW50ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/ClientOnly.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/LastActivityDisplay.tsx":
/*!****************************************************!*\
  !*** ./src/app/components/LastActivityDisplay.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LastActivityDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Clock!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LastActivityDisplay(param) {\n    let { lastActivity } = param;\n    // Calculate the date based on days ago\n    const date = new Date();\n    date.setDate(date.getDate() - lastActivity);\n    // Format the date\n    const formattedDate = date.toLocaleDateString();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\LastActivityDisplay.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: [\n                    \"Last activity: \",\n                    formattedDate\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\LastActivityDisplay.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\LastActivityDisplay.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c = LastActivityDisplay;\nvar _c;\n$RefreshReg$(_c, \"LastActivityDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29tcG9uZW50cy9MYXN0QWN0aXZpdHlEaXNwbGF5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFMEI7QUFDVztBQU10QixTQUFTRSxvQkFBb0IsS0FBMEM7UUFBMUMsRUFBRUMsWUFBWSxFQUE0QixHQUExQztJQUMxQyx1Q0FBdUM7SUFDdkMsTUFBTUMsT0FBTyxJQUFJQztJQUNqQkQsS0FBS0UsT0FBTyxDQUFDRixLQUFLRyxPQUFPLEtBQUtKO0lBRTlCLGtCQUFrQjtJQUNsQixNQUFNSyxnQkFBZ0JKLEtBQUtLLGtCQUFrQjtJQUU3QyxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNWLGlGQUFLQTtnQkFBQ1UsV0FBVTs7Ozs7OzBCQUNqQiw4REFBQ0M7Z0JBQUtELFdBQVU7O29CQUFVO29CQUFnQkg7Ozs7Ozs7Ozs7Ozs7QUFHaEQ7S0Fkd0JOIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGFwcFxcY29tcG9uZW50c1xcTGFzdEFjdGl2aXR5RGlzcGxheS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ2xvY2sgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbnRlcmZhY2UgTGFzdEFjdGl2aXR5RGlzcGxheVByb3BzIHtcbiAgbGFzdEFjdGl2aXR5OiBudW1iZXI7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExhc3RBY3Rpdml0eURpc3BsYXkoeyBsYXN0QWN0aXZpdHkgfTogTGFzdEFjdGl2aXR5RGlzcGxheVByb3BzKSB7XG4gIC8vIENhbGN1bGF0ZSB0aGUgZGF0ZSBiYXNlZCBvbiBkYXlzIGFnb1xuICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTtcbiAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpIC0gbGFzdEFjdGl2aXR5KTtcbiAgXG4gIC8vIEZvcm1hdCB0aGUgZGF0ZVxuICBjb25zdCBmb3JtYXR0ZWREYXRlID0gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoKTtcbiAgXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkxhc3QgYWN0aXZpdHk6IHtmb3JtYXR0ZWREYXRlfTwvc3Bhbj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNsb2NrIiwiTGFzdEFjdGl2aXR5RGlzcGxheSIsImxhc3RBY3Rpdml0eSIsImRhdGUiLCJEYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJmb3JtYXR0ZWREYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/LastActivityDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/ParentDashboard.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/ParentDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../providers/AuthProvider */ \"(app-pages-browser)/./src/app/providers/AuthProvider.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,LogOut,Trash2,UserPlus!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,LogOut,Trash2,UserPlus!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,LogOut,Trash2,UserPlus!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,LogOut,Trash2,UserPlus!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Clock,LogOut,Trash2,UserPlus!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/ClientOnly */ \"(app-pages-browser)/./src/app/components/ClientOnly.tsx\");\n/* harmony import */ var _components_LastActivityDisplay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/LastActivityDisplay */ \"(app-pages-browser)/./src/app/components/LastActivityDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst ParentDashboard = (param)=>{\n    let { user, userData } = param;\n    var _auth_user;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { childrenData, refreshChildrenData } = (0,_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [children, setChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dataFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const auth = (0,_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const parentId = ((_auth_user = auth.user) === null || _auth_user === void 0 ? void 0 : _auth_user.uid) || localStorage.getItem('parent_id') || 'unknown_parent';\n    const navigateToChildDashboard = (childId)=>{\n        localStorage.setItem('viewing_as_child', 'true');\n        localStorage.setItem('current_student_id', childId);\n        localStorage.setItem('student_id', childId);\n        localStorage.setItem('parent_role', 'true');\n        localStorage.setItem('temp_student_session', childId);\n        window.location.href = \"/student-dashboard/\".concat(childId);\n    };\n    const fetchChildrenFromFirestore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ParentDashboard.useCallback[fetchChildrenFromFirestore]\": async ()=>{\n            setLoading(true);\n            try {\n                var _data_data;\n                const response = await fetch(\"/api/parent/dashboard?parentId=\".concat(parentId));\n                if (!response.ok) throw new Error(\"API request failed with status \".concat(response.status));\n                const data = await response.json();\n                if (data.success && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.children)) {\n                    const childrenData = data.data.children.map({\n                        \"ParentDashboard.useCallback[fetchChildrenFromFirestore].childrenData\": (child)=>({\n                                id: child.childId,\n                                name: child.name || 'Unknown Student',\n                                studentId: child.studentId || child.childId,\n                                progress: child.progress || 0,\n                                upcomingLessons: child.upcomingLessons || 0,\n                                completedLessons: child.completedLessons || 0,\n                                lastActivity: child.lastActivity || 0\n                            })\n                    }[\"ParentDashboard.useCallback[fetchChildrenFromFirestore].childrenData\"]);\n                    setChildren(childrenData);\n                    localStorage.setItem('children_data', JSON.stringify(childrenData));\n                }\n            } catch (error) {\n                console.error(\"Error fetching children data:\", error);\n                setError(\"Failed to load children data\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"ParentDashboard.useCallback[fetchChildrenFromFirestore]\"], [\n        parentId\n    ]);\n    const handleRemoveChild = async (childId)=>{\n        try {\n            const response = await fetch('/api/parent/remove-child', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    parentId,\n                    childId\n                })\n            });\n            if (!response.ok) throw new Error('Failed to remove child');\n            const result = await response.json();\n            if (result.success) {\n                setChildren((prev)=>prev.filter((c)=>c.id !== childId));\n                localStorage.removeItem('children_data');\n            } else {\n                throw new Error(result.error);\n            }\n        } catch (error) {\n            console.error('Error removing child:', error);\n        }\n    };\n    const handleForceRefresh = ()=>{\n        localStorage.removeItem('children_data');\n        setChildren([]);\n        setError(null);\n        fetchChildrenFromFirestore();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentDashboard.useEffect\": ()=>{\n            if (!dataFetched.current) {\n                dataFetched.current = true;\n                fetchChildrenFromFirestore();\n            }\n        }\n    }[\"ParentDashboard.useEffect\"], [\n        fetchChildrenFromFirestore\n    ]);\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-500\",\n                        children: \"Not authenticated\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push('/login'),\n                        className: \"mt-4\",\n                        children: \"Go to Login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"Parent Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleForceRefresh,\n                                    children: \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push('/logout'),\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Logout\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Your Children\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, undefined),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                1,\n                                2\n                            ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-8 w-3/4 mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-4 w-1/2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-4 w-full mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-4 w-3/4 mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                                    className: \"h-4 w-1/2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, \"skeleton-\".concat(item), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, undefined) : children.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: child.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            children: [\n                                                child.progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                            children: \"Overall Progress\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                                            value: child.progress\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        child.completedLessons,\n                                                                        \" lessons completed\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        child.upcomingLessons,\n                                                                        \" upcoming lessons\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LastActivityDisplay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            lastActivity: child.lastActivity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between pt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleRemoveChild(child.id),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Remove\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            onClick: ()=>navigateToChildDashboard(child.id),\n                                                            children: \"View Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, child.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"No children linked to your account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push('/add-child'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Clock_LogOut_Trash2_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" Add Child\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\ParentDashboard.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ParentDashboard, \"fPfsu37O0Gmzd3RrcRYuYMkQyeI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = ParentDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ParentDashboard);\nvar _c;\n$RefreshReg$(_c, \"ParentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/ParentDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/providers/AuthProvider */ \"(app-pages-browser)/./src/app/providers/AuthProvider.tsx\");\n/* harmony import */ var _ParentDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ParentDashboard */ \"(app-pages-browser)/./src/app/dashboard/ParentDashboard.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_LoadingState__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LoadingState */ \"(app-pages-browser)/./src/components/LoadingState.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Styles can remain if needed\nconst pageStyles = {\n    container: 'max-w-7xl mx-auto p-8',\n    header: 'mb-8',\n    title: 'text-3xl font-bold mb-2 text-gray-800',\n    subtitle: 'text-lg text-gray-600',\n    errorContainer: 'bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded',\n    errorTitle: 'font-medium',\n    errorMessage: 'text-sm',\n    logoutButton: 'flex items-center text-gray-600 hover:text-gray-900',\n    logoutIcon: 'mr-2 h-4 w-4'\n};\nfunction DashboardPage() {\n    _s();\n    const { isAuthenticated, loading, user, userData, studentSession, userRole, error, logout } = (0,_app_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            message: \"Loading your dashboard...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 40,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: pageStyles.errorContainer,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: pageStyles.errorTitle,\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: pageStyles.errorMessage,\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: ()=>router.push('/login?reset=true'),\n                        className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                        children: \"Return to Login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        console.warn('DashboardPage: Reached while unauthenticated. AuthProvider should have redirected.');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            message: \"Redirecting to login...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 62,\n            columnNumber: 12\n        }, this);\n    }\n    console.log('DashboardPage: Rendering for role:', userRole);\n    if (userRole === 'parent') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ParentDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            user: user,\n            userData: userData\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 12\n        }, this);\n    }\n    if (userRole === 'student') {\n        // If studentSession exists, the useEffect should handle redirection. Show loading.\n        if (studentSession) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                message: \"Redirecting to your dashboard...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 14\n            }, this);\n        }\n        // If studentSession doesn't exist, show fallback message.\n        console.warn(\"DashboardPage: Student role detected but no studentSession available.\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: pageStyles.container,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: pageStyles.title,\n                    children: \"Student Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: pageStyles.subtitle,\n                    children: \"We couldn't find your student ID. Please try logging out and back in.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    onClick: logout,\n                    className: \"mt-4\",\n                    children: \"Logout\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (userRole) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: pageStyles.container,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: pageStyles.title,\n                    children: \"Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: pageStyles.subtitle,\n                    children: [\n                        \"Welcome! Role: \",\n                        userRole\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    onClick: logout,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: pageStyles.logoutIcon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        \" Logout\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    console.warn('DashboardPage: Authenticated but no role determined.');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: pageStyles.container,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Could not determine your user role. Please try logging out and back in.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                onClick: logout,\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pageStyles.logoutIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    \" Logout\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"69t2D5s3d93Zn04YTA3WsY4LMYY=\", false, function() {\n    return [\n        _app_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);