/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/classroom/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclassroom%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclassroom%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/classroom/page.tsx */ \"(app-pages-browser)/./src/app/classroom/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjbGFzc3Jvb20lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUF5SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxjbGFzc3Jvb21cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclassroom%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx":
/*!************************************************!*\
  !*** ./src/app/classroom/ClassroomContent.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_lesson_components_TutorChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/lesson-components/TutorChat */ \"(app-pages-browser)/./src/components/lesson-components/TutorChat.tsx\");\n/* harmony import */ var _hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useLessonTimer */ \"(app-pages-browser)/./src/hooks/useLessonTimer.ts\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(app-pages-browser)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-interaction-logger */ \"(app-pages-browser)/./src/hooks/use-interaction-logger.ts\");\n/* harmony import */ var _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/shadcn/card */ \"(app-pages-browser)/./src/components/shadcn/card.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/ErrorDisplay */ \"(app-pages-browser)/./src/components/ui/ErrorDisplay.tsx\");\n/* harmony import */ var _components_shadcn_LessonHeader__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/shadcn/LessonHeader */ \"(app-pages-browser)/./src/components/shadcn/LessonHeader.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_lesson_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/lesson-components/ErrorBoundary */ \"(app-pages-browser)/./src/components/lesson-components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_lesson_components_LevelAdjustmentHistory__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/lesson-components/LevelAdjustmentHistory */ \"(app-pages-browser)/./src/components/lesson-components/LevelAdjustmentHistory.tsx\");\n/* harmony import */ var _components_DiagnosticProgress__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/DiagnosticProgress */ \"(app-pages-browser)/./src/components/DiagnosticProgress.tsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n // Using simplified session hook\n\n\n\n\n\n\n\n\n\n\n\n // Import AxiosError\nconst AI_INTERACTION_ENDPOINT = '/api/enhance-content'; // Next.js API proxy for Flask's /api/enhance-content\nconst LESSON_PHASE_COMPLETED = \"completed\";\nconst LESSON_DURATION_MINUTES = 45; // Total lesson duration in minutes\n// Fallback Component for ErrorBoundary\nconst ErrorBoundaryFallback = (param)=>{\n    let { error, resetErrorBoundary } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n            className: \"border-destructive\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                        className: \"text-destructive\",\n                        children: \"Rendering Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"An unexpected error occurred while rendering this part of the lesson.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 p-2 bg-red-50 text-red-700 rounded text-xs overflow-auto\",\n                            children: [\n                                error.message,\n                                \"\\\\n\",\n                                error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                            onClick: resetErrorBoundary,\n                            className: \"mt-4\",\n                            children: \"Try to Recover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ErrorBoundaryFallback;\nconst ClassroomContent = (param)=>{\n    let { sessionIdFromUrlProp, lessonRefProp, studentIdProp, countryProp, curriculumProp, gradeProp, levelProp, subjectProp } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const logInteraction = (0,_hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { toast } = (0,_app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const { user, isReady, getAuthHeaders, backendSessionId// THE Firestore-backed lesson session ID from context\n     } = (0,_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const chatBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const initialAiInteractionSentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false); // Tracks if the first system message has been sent\n    const handleAiInteractionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // Ref to store handleAiInteraction\n    const [currentLessonPhase, setCurrentLessonPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isAiLoading, setIsAiLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // For AI response loading\n    const [isPageLoading, setIsPageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Overall page/initial setup loading\n    const [uiError, setUiError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // For displaying errors in the UI\n    // Level tracking state for real-time adjustments\n    const [currentTeachingLevel, setCurrentTeachingLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [levelAdjustmentHistory, setLevelAdjustmentHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Diagnostic progress tracking state\n    const [diagnosticProgress, setDiagnosticProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentQuestionIndex: 0,\n        totalQuestions: 5,\n        currentProbingLevel: 5,\n        questionsCompleted: 0,\n        isComplete: false\n    });\n    // Timer state\n    const [lessonStartTime, setLessonStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Timer logic moved to custom hook\n    const { timeRemaining, isTimerActive, setIsTimerActive, formatTime, startTimer, getTimerStatus } = (0,_hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__.useLessonTimer)({\n        lessonStartTime,\n        currentLessonPhase,\n        sessionIdFromUrlProp,\n        backendSessionId: backendSessionId || undefined,\n        lessonRef: lessonRefProp,\n        onTimeUp: {\n            \"ClassroomContent.useLessonTimer\": ()=>{\n                // This will be called when time is up\n                const sessionId = sessionIdFromUrlProp || backendSessionId;\n                if (sessionId) {\n                    handleAiInteraction('[System: Time is up! Completing lesson...]', true, sessionId);\n                }\n            }\n        }[\"ClassroomContent.useLessonTimer\"],\n        onQuizTransition: {\n            \"ClassroomContent.useLessonTimer\": ()=>{\n                // This will be called when forced quiz transition is triggered at 37.5 minutes\n                const sessionId = sessionIdFromUrlProp || backendSessionId;\n                if (sessionId) {\n                    logInteraction('forced_quiz_transition_triggered', {\n                        lessonRef: lessonRefProp,\n                        sessionId: sessionId,\n                        currentPhase: currentLessonPhase || 'teaching',\n                        timeRemaining: timeRemaining,\n                        uncoveredContent: 'Material not covered due to time constraints will be added to homework'\n                    });\n                    // Send system message to AI to trigger quiz and capture uncovered content as homework\n                    handleAiInteraction('[System: Time limit approaching - transition to quiz phase and capture any uncovered teaching material as homework assignments]', true, sessionId);\n                }\n            }\n        }[\"ClassroomContent.useLessonTimer\"]\n    });\n    const [lessonDetails, setLessonDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Chat and lesson state\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lessonEnded, setLessonEnded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Derive core identifiers from props\n    const lessonRef = lessonRefProp;\n    const studentId = studentIdProp; // This should be the Firebase UID of the student\n    // Handle lesson phase updates from server responses\n    const handleLessonPhaseUpdates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (response)=>{\n            try {\n                var _response_data, _response_data1, _response_data2, _response_data3, _response_data4, _response_data5, _response_data6;\n                console.log('[handleLessonPhaseUpdates] ==> ENHANCED DEBUGGING - PROCESSING RESPONSE FROM BACKEND');\n                console.log('[handleLessonPhaseUpdates] ==> FULL RAW RESPONSE:', JSON.stringify(response, null, 2));\n                console.log('[handleLessonPhaseUpdates] Raw response keys:', Object.keys(response || {}));\n                console.log('[handleLessonPhaseUpdates] Response.data keys:', Object.keys((response === null || response === void 0 ? void 0 : response.data) || {}));\n                // FORCE CONSOLE OUTPUT FOR REAL-TIME DEBUGGING\n                console.warn('🔥 FRONTEND PHASE UPDATE DEBUG:', {\n                    responseType: typeof response,\n                    hasData: !!(response === null || response === void 0 ? void 0 : response.data),\n                    dataKeys: (response === null || response === void 0 ? void 0 : response.data) ? Object.keys(response.data) : 'no data',\n                    currentFrontendPhase: currentLessonPhase\n                });\n                // CRITICAL FIX: Frontend defensive parsing to handle multiple backend field names\n                // Check multiple possible field locations for state updates\n                const serverStateUpdates = (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.state_updates) || (response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.parsed_state) || (response === null || response === void 0 ? void 0 : response.state_updates) || (response === null || response === void 0 ? void 0 : response.parsed_state);\n                let phaseFromServer = (response === null || response === void 0 ? void 0 : (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.current_phase) || (response === null || response === void 0 ? void 0 : response.current_phase); // From the main response body\n                let diagCompleteFromServer = response === null || response === void 0 ? void 0 : (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.diagnostic_complete;\n                let assessedLevelFromServer = response === null || response === void 0 ? void 0 : (_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : _response_data4.assessed_level;\n                // ENHANCED: Check if this is a wrapped response (Flask returns {success: true, data: {...}})\n                if ((response === null || response === void 0 ? void 0 : response.success) && (response === null || response === void 0 ? void 0 : response.data)) {\n                    var _innerData_state_updates, _innerData_state_updates1, _innerData_state_updates2, _innerData_state_updates3;\n                    console.log('[handleLessonPhaseUpdates] 🎯 DETECTED WRAPPED RESPONSE from Flask');\n                    const innerData = response.data;\n                    // CRITICAL FIX: Backend returns current_phase at top level of data, not just in state_updates\n                    // Priority order: state_updates.new_phase > data.current_phase > data.new_phase\n                    phaseFromServer = ((_innerData_state_updates = innerData.state_updates) === null || _innerData_state_updates === void 0 ? void 0 : _innerData_state_updates.new_phase) || innerData.current_phase || innerData.new_phase || phaseFromServer;\n                    diagCompleteFromServer = innerData.diagnostic_complete || innerData.diagnostic_completed_this_session || ((_innerData_state_updates1 = innerData.state_updates) === null || _innerData_state_updates1 === void 0 ? void 0 : _innerData_state_updates1.diagnostic_completed_this_session) || diagCompleteFromServer;\n                    assessedLevelFromServer = innerData.assessed_level || innerData.assigned_level_for_teaching || ((_innerData_state_updates2 = innerData.state_updates) === null || _innerData_state_updates2 === void 0 ? void 0 : _innerData_state_updates2.assigned_level_for_teaching) || assessedLevelFromServer;\n                    console.log('[handleLessonPhaseUpdates] 🎯 EXTRACTED FROM WRAPPED RESPONSE:', {\n                        phaseFromServer,\n                        diagCompleteFromServer,\n                        assessedLevelFromServer,\n                        hasStateUpdates: !!innerData.state_updates,\n                        innerDataKeys: Object.keys(innerData)\n                    });\n                    // ENHANCED: Force console output to show what we found\n                    console.warn('🔥 PHASE EXTRACTION DEBUG:', {\n                        'innerData.current_phase': innerData.current_phase,\n                        'innerData.new_phase': innerData.new_phase,\n                        'innerData.state_updates?.new_phase': (_innerData_state_updates3 = innerData.state_updates) === null || _innerData_state_updates3 === void 0 ? void 0 : _innerData_state_updates3.new_phase,\n                        'final_phaseFromServer': phaseFromServer\n                    });\n                }\n                // Debug logging to track field locations\n                console.log('[handleLessonPhaseUpdates] DEBUG: Checking state update fields:', {\n                    'response.data.state_updates': !!(response === null || response === void 0 ? void 0 : (_response_data5 = response.data) === null || _response_data5 === void 0 ? void 0 : _response_data5.state_updates),\n                    'response.data.parsed_state': !!(response === null || response === void 0 ? void 0 : (_response_data6 = response.data) === null || _response_data6 === void 0 ? void 0 : _response_data6.parsed_state),\n                    'response.state_updates': !!(response === null || response === void 0 ? void 0 : response.state_updates),\n                    'response.parsed_state': !!(response === null || response === void 0 ? void 0 : response.parsed_state),\n                    'final_serverStateUpdates': !!serverStateUpdates,\n                    'phaseFromServer': phaseFromServer,\n                    'currentFrontendPhase': currentLessonPhase\n                });\n                if (serverStateUpdates && typeof serverStateUpdates === 'object') {\n                    console.log('[handleLessonPhaseUpdates] Found state updates:', Object.keys(serverStateUpdates));\n                    console.log('[handleLessonPhaseUpdates] FULL state updates object:', JSON.stringify(serverStateUpdates, null, 2));\n                    if (serverStateUpdates.new_phase) {\n                        phaseFromServer = serverStateUpdates.new_phase;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 PHASE TRANSITION DETECTED: \".concat(phaseFromServer));\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 Previous phase: \".concat(currentLessonPhase));\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDD04 New phase: \".concat(phaseFromServer));\n                        // FORCE IMMEDIATE CONSOLE ALERT\n                        console.warn(\"\\uD83D\\uDE80 CRITICAL: PHASE CHANGING FROM \".concat(currentLessonPhase, \" TO \").concat(phaseFromServer));\n                    }\n                    if (serverStateUpdates.diagnostic_completed_this_session !== undefined) {\n                        diagCompleteFromServer = serverStateUpdates.diagnostic_completed_this_session;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDCCA Diagnostic completion status: \".concat(diagCompleteFromServer));\n                    }\n                    if (serverStateUpdates.assigned_level_for_teaching !== undefined) {\n                        assessedLevelFromServer = serverStateUpdates.assigned_level_for_teaching;\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDFAF Teaching level assigned: \".concat(assessedLevelFromServer));\n                    }\n                    // Extract diagnostic-specific information for UI display\n                    if (serverStateUpdates.current_probing_level_number) {\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83D\\uDCCB Current probing level: \".concat(serverStateUpdates.current_probing_level_number));\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>({\n                                    ...prev,\n                                    currentProbingLevel: serverStateUpdates.current_probing_level_number\n                                })\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.current_question_index !== undefined) {\n                        console.log(\"[handleLessonPhaseUpdates] ❓ Current question index: \".concat(serverStateUpdates.current_question_index));\n                        console.log(\"[handleLessonPhaseUpdates] ❓ Updating diagnostic progress with question index: \".concat(serverStateUpdates.current_question_index));\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    currentQuestionIndex: serverStateUpdates.current_question_index,\n                                    questionsCompleted: serverStateUpdates.current_question_index // Update both for consistency\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] ❓ Diagnostic progress after question index update:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.diagnostic_questions_completed) {\n                        console.log(\"[handleLessonPhaseUpdates] ✅ Questions completed: \".concat(serverStateUpdates.diagnostic_questions_completed));\n                        console.log(\"[handleLessonPhaseUpdates] ✅ Updating diagnostic progress with completed questions\");\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    questionsCompleted: serverStateUpdates.diagnostic_questions_completed\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] ✅ Diagnostic progress after completion update:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    if (serverStateUpdates.diagnostic_completed_this_session) {\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Diagnostic phase completed!\");\n                        console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Marking diagnostic as complete in UI state\");\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>{\n                                const updated = {\n                                    ...prev,\n                                    isComplete: true\n                                };\n                                console.log(\"[handleLessonPhaseUpdates] \\uD83C\\uDF89 Final diagnostic progress state:\", updated);\n                                return updated;\n                            }\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                }\n                if (phaseFromServer) {\n                    console.log('[handleLessonPhaseUpdates] 🎯 UPDATING LESSON PHASE TO:', phaseFromServer);\n                    console.log('[handleLessonPhaseUpdates] 🎯 Previous lesson phase was:', currentLessonPhase);\n                    // FORCE IMMEDIATE STATE UPDATE WITH DEBUGGING\n                    setCurrentLessonPhase({\n                        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prevPhase)=>{\n                            console.warn(\"\\uD83D\\uDD25 STATE UPDATE: PHASE CHANGING \".concat(prevPhase, \" → \").concat(phaseFromServer));\n                            return phaseFromServer;\n                        }\n                    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    // CRITICAL FIX: Handle diagnostic_start_probe phase properly\n                    if (phaseFromServer === 'diagnostic_start_probe') {\n                        console.log('[handleLessonPhaseUpdates] 🔍 DIAGNOSTIC START PROBE: Setting question index to 0');\n                        setDiagnosticProgress({\n                            \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": (prev)=>({\n                                    ...prev,\n                                    currentQuestionIndex: 0,\n                                    questionsCompleted: 0,\n                                    isComplete: false\n                                })\n                        }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"]);\n                    }\n                    console.log('[handleLessonPhaseUpdates] 🎯 Phase update completed');\n                    // ADDITIONAL: Force a re-render by updating a dummy state\n                    setTimeout({\n                        \"ClassroomContent.useCallback[handleLessonPhaseUpdates]\": ()=>{\n                            console.warn(\"\\uD83D\\uDD25 POST-UPDATE CHECK: currentLessonPhase should now be \".concat(phaseFromServer));\n                        }\n                    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"], 100);\n                } else {\n                    console.warn('[handleLessonPhaseUpdates] ⚠️ NO PHASE UPDATE FOUND IN RESPONSE');\n                    console.warn('[handleLessonPhaseUpdates] ⚠️ Response structure might be different than expected');\n                }\n                if (diagCompleteFromServer !== undefined) {\n                    console.log('[handleLessonPhaseUpdates] Diagnostic complete status:', diagCompleteFromServer);\n                // Potentially set a local state like setIsDiagnosticComplete(diagCompleteFromServer);\n                }\n                if (assessedLevelFromServer !== undefined) {\n                    console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);\n                // Potentially set a local state like setTeachingLevel(assessedLevelFromServer);\n                }\n                if (phaseFromServer === LESSON_PHASE_COMPLETED) {\n                    var _response_data7;\n                    console.log('Lesson completed successfully (from phase update)');\n                    setLessonEnded(true);\n                    // Potentially show download link for notes if response.data.notes_download_url exists\n                    if (response === null || response === void 0 ? void 0 : (_response_data7 = response.data) === null || _response_data7 === void 0 ? void 0 : _response_data7.notes_download_url) {\n                    // Show a button or link\n                    }\n                }\n                // ... other phase-specific UI logic ...\n                // COMPREHENSIVE SUMMARY LOGGING\n                console.log('[handleLessonPhaseUpdates] ==> SUMMARY OF STATE UPDATES:');\n                console.log('[handleLessonPhaseUpdates] Phase changed:', phaseFromServer ? \"\".concat(currentLessonPhase, \" → \").concat(phaseFromServer) : 'No change');\n                console.log('[handleLessonPhaseUpdates] Diagnostic complete:', diagCompleteFromServer);\n                console.log('[handleLessonPhaseUpdates] Assessed level:', assessedLevelFromServer);\n                console.log('[handleLessonPhaseUpdates] Current diagnostic progress:', diagnosticProgress);\n                console.log('[handleLessonPhaseUpdates] ==> END OF PROCESSING');\n            } catch (error) {\n                console.error('Error handling lesson phase update:', error);\n                console.error('Error details:', error);\n                setUiError('Failed to synchronize lesson state. Please try again.');\n            }\n        }\n    }[\"ClassroomContent.useCallback[handleLessonPhaseUpdates]\"], [\n        setCurrentLessonPhase,\n        setLessonEnded,\n        setUiError,\n        currentLessonPhase,\n        diagnosticProgress\n    ]);\n    // Helper function to extract enhanced content from AI responses\n    const extractEnhancedContent = (response)=>{\n        var _response_data, _response_data_data, _response_data1;\n        if (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.enhanced_content) {\n            return response.data.enhanced_content;\n        }\n        if (response === null || response === void 0 ? void 0 : response.enhanced_content) {\n            return response.enhanced_content;\n        }\n        if (response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.enhanced_content) {\n            return response.data.data.enhanced_content;\n        }\n        const findContent = (obj)=>{\n            if (typeof obj === 'string') return obj;\n            if (Array.isArray(obj)) {\n                for (const item of obj){\n                    const found = findContent(item);\n                    if (found) return found;\n                }\n            } else if (obj && typeof obj === 'object') {\n                for(const key in obj){\n                    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                        const found = findContent(obj[key]);\n                        if (found) return found;\n                    }\n                }\n            }\n            return null;\n        };\n        return findContent(response) || \"I'm sorry, I couldn't process that response. Please try again.\";\n    };\n    // Helper function to get user-friendly error messages\n    const getErrorMessage = (error)=>{\n        if (axios__WEBPACK_IMPORTED_MODULE_17__[\"default\"].isAxiosError(error)) {\n            const axiosError = error;\n            if (axiosError.response) {\n                var _axiosError_response_data;\n                // Server responded with a status code outside 2xx\n                if (axiosError.response.status === 401) {\n                    return \"Your session has expired. Please log in again.\";\n                }\n                if (axiosError.response.status === 403) {\n                    return \"You don't have permission to perform this action.\";\n                }\n                if (axiosError.response.status === 404) {\n                    return \"The requested resource was not found.\";\n                }\n                if (axiosError.response.status === 429) {\n                    return \"Too many requests. Please wait a moment and try again.\";\n                }\n                if (axiosError.response.status >= 500) {\n                    return \"Our servers are experiencing issues. Please try again later.\";\n                }\n                return ((_axiosError_response_data = axiosError.response.data) === null || _axiosError_response_data === void 0 ? void 0 : _axiosError_response_data.message) || axiosError.message;\n            }\n            if (axiosError.request) {\n                // Request was made but no response received\n                return \"No response from server. Please check your internet connection.\";\n            }\n        }\n        // Handle other error types\n        if (error instanceof Error) {\n            return error.message;\n        }\n        if (typeof error === 'string') {\n            return error;\n        }\n        return \"An unknown error occurred. Please try again.\";\n    };\n    // Define handleAiInteraction first to avoid initialization issues\n    const handleAiInteraction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ClassroomContent.useCallback[handleAiInteraction]\": async function(messageContent) {\n            let isSystemMessage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, currentSessionIdForCall = arguments.length > 2 ? arguments[2] : void 0;\n            // Priority: 1. Explicitly provided ID 2. URL session ID 3. Context session ID\n            const sessionIdToUse = currentSessionIdForCall || sessionIdFromUrlProp || backendSessionId;\n            const logContext = \"[ClassroomContent handleAiInteraction] isSystem: \".concat(isSystemMessage, \", SessionToUse: \").concat(sessionIdToUse, \", LessonRef: \").concat(lessonRef, \", StudentId: \").concat(studentId);\n            console.log(logContext);\n            // Input validation\n            if (typeof sessionIdToUse !== 'string' || !sessionIdToUse.trim()) {\n                const error = \"Session ID is invalid ('\".concat(sessionIdToUse, \"') from context/prop.\");\n                setUiError(error);\n                console.error(logContext, \"CRITICAL ERROR:\", error);\n                toast({\n                    title: \"Session Error\",\n                    description: \"A valid session ID is required to continue the lesson. Please try refreshing.\",\n                    variant: \"destructive\"\n                });\n                setIsAiLoading(false);\n                return false;\n            }\n            if (!lessonRef || !studentId) {\n                const error = \"Lesson Reference or Student ID prop missing.\";\n                setUiError(error);\n                console.error(logContext, \"CRITICAL ERROR:\", error);\n                toast({\n                    title: \"Configuration Error\",\n                    description: error,\n                    variant: \"destructive\"\n                });\n                setIsAiLoading(false);\n                return false;\n            }\n            setIsAiLoading(true);\n            setUiError(null);\n            logInteraction(isSystemMessage ? 'system_message_ai' : 'user_message_ai', {\n                lessonRef,\n                sessionId: sessionIdToUse,\n                message: messageContent.substring(0, 100)\n            });\n            // Add user message to chat history if not a system message\n            if (!isSystemMessage) {\n                const currentUserMessage = {\n                    role: 'user',\n                    content: messageContent,\n                    timestamp: new Date().toISOString()\n                };\n                console.log(\"[ClassroomContent] \\uD83D\\uDC64 Adding user message to chat:\", {\n                    messagePreview: messageContent.substring(0, 50) + '...',\n                    messageLength: messageContent.length,\n                    currentPhase: currentLessonPhase,\n                    chatHistoryLength: chatHistory.length\n                });\n                setChatHistory({\n                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                            ...prev,\n                            currentUserMessage\n                        ]\n                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n            }\n            try {\n                const authHeaders = getAuthHeaders(backendSessionId);\n                if (!authHeaders['Authorization']) {\n                    throw new Error(\"Authentication token unavailable for AI interaction.\");\n                }\n                const requestBody = {\n                    student_id: studentId,\n                    lesson_ref: lessonRef,\n                    content_to_enhance: messageContent,\n                    country: countryProp || 'Nigeria',\n                    curriculum: curriculumProp || 'National Curriculum',\n                    grade: gradeProp,\n                    level: levelProp,\n                    subject: subjectProp,\n                    session_id: sessionIdToUse,\n                    chat_history: isSystemMessage ? [] : chatHistory.slice(-8)\n                };\n                console.log(\"[ClassroomContent] AI Interaction Request:\", {\n                    endpoint: AI_INTERACTION_ENDPOINT,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: {\n                        ...requestBody,\n                        chat_history: \"[\".concat(requestBody.chat_history.length, \" messages]\")\n                    }\n                });\n                const MAX_RETRIES = 2;\n                let lastError;\n                for(let attempt = 0; attempt <= MAX_RETRIES; attempt++){\n                    try {\n                        var _axiosResponse_data;\n                        const axiosResponse = await axios__WEBPACK_IMPORTED_MODULE_17__[\"default\"].post(AI_INTERACTION_ENDPOINT, requestBody, {\n                            headers: {\n                                ...authHeaders,\n                                'Content-Type': 'application/json'\n                            },\n                            timeout: 90000,\n                            validateStatus: {\n                                \"ClassroomContent.useCallback[handleAiInteraction]\": ()=>true // Always resolve the promise\n                            }[\"ClassroomContent.useCallback[handleAiInteraction]\"]\n                        });\n                        if (axiosResponse.status >= 200 && axiosResponse.status < 300) {\n                            let result;\n                            try {\n                                var _result_data, _result_data1, _result_data_state_updates, _result_data2, _result_state_updates, _result_state_updates1, _result_state_updates2, _result_data3;\n                                result = typeof axiosResponse.data === 'string' ? JSON.parse(axiosResponse.data) : axiosResponse.data;\n                                console.log('[ClassroomContent] Successfully parsed response:', result);\n                                // CRITICAL DEBUG: Show exact response structure for phase sync debugging\n                                console.warn('🔥 FRONTEND API RESPONSE STRUCTURE DEBUG:');\n                                console.warn('🔥 Response keys:', Object.keys(result || {}));\n                                console.warn('🔥 Response.data keys:', Object.keys((result === null || result === void 0 ? void 0 : result.data) || {}));\n                                console.warn('🔥 Response.data.state_updates keys:', Object.keys((result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.state_updates) || {}));\n                                console.warn('🔥 Current phase in response.data:', result === null || result === void 0 ? void 0 : (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.current_phase);\n                                console.warn('🔥 New phase in state_updates:', result === null || result === void 0 ? void 0 : (_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : (_result_data_state_updates = _result_data2.state_updates) === null || _result_data_state_updates === void 0 ? void 0 : _result_data_state_updates.new_phase);\n                                console.warn('🔥 Full result structure (first 500 chars):', JSON.stringify(result, null, 2).substring(0, 500));\n                                const enhancedContent = extractEnhancedContent(result);\n                                // Check for level adjustment notifications\n                                if (result === null || result === void 0 ? void 0 : (_result_state_updates = result.state_updates) === null || _result_state_updates === void 0 ? void 0 : _result_state_updates.level_adjustment_made) {\n                                    const adjustment = result.state_updates.level_adjustment_made;\n                                    console.log('[ClassroomContent] Level adjustment detected:', adjustment);\n                                    // Update current teaching level\n                                    setCurrentTeachingLevel(adjustment.to_level);\n                                    // Add to adjustment history\n                                    setLevelAdjustmentHistory({\n                                        \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                                                ...prev,\n                                                {\n                                                    timestamp: adjustment.timestamp,\n                                                    direction: adjustment.direction,\n                                                    fromLevel: adjustment.from_level,\n                                                    toLevel: adjustment.to_level,\n                                                    confidence: adjustment.confidence_score\n                                                }\n                                            ].slice(-10)\n                                    }[\"ClassroomContent.useCallback[handleAiInteraction]\"]); // Keep only last 10 adjustments\n                                    // Show level adjustment notification\n                                    toast({\n                                        title: \"Teaching Level \".concat(adjustment.direction === 'up' ? 'Increased' : 'Decreased'),\n                                        description: \"I've adjusted the lesson difficulty from Level \".concat(adjustment.from_level, \" to Level \").concat(adjustment.to_level, \" to better match your learning pace.\"),\n                                        variant: adjustment.direction === 'up' ? 'default' : 'destructive',\n                                        duration: 8000\n                                    });\n                                    // Log the level adjustment\n                                    logInteraction('level_adjustment_notification', {\n                                        lessonRef,\n                                        sessionId: sessionIdToUse,\n                                        direction: adjustment.direction,\n                                        fromLevel: adjustment.from_level,\n                                        toLevel: adjustment.to_level,\n                                        confidence: adjustment.confidence_score,\n                                        reasoning: adjustment.reasoning,\n                                        timestamp: new Date().toISOString()\n                                    });\n                                }\n                                // Check for initial or updated teaching level in state updates\n                                if ((result === null || result === void 0 ? void 0 : (_result_state_updates1 = result.state_updates) === null || _result_state_updates1 === void 0 ? void 0 : _result_state_updates1.assigned_level_for_teaching) && currentTeachingLevel === null) {\n                                    setCurrentTeachingLevel(result.state_updates.assigned_level_for_teaching);\n                                }\n                                const aiMessage = {\n                                    role: 'assistant',\n                                    content: enhancedContent,\n                                    timestamp: new Date().toISOString()\n                                };\n                                console.log(\"[ClassroomContent] \\uD83D\\uDCAC Adding AI message to chat history:\", {\n                                    contentPreview: enhancedContent.substring(0, 100) + '...',\n                                    messageLength: enhancedContent.length,\n                                    currentChatLength: chatHistory.length,\n                                    hasStateUpdates: !!(result === null || result === void 0 ? void 0 : result.state_updates),\n                                    hasPhaseUpdate: !!((result === null || result === void 0 ? void 0 : (_result_state_updates2 = result.state_updates) === null || _result_state_updates2 === void 0 ? void 0 : _result_state_updates2.new_phase) || (result === null || result === void 0 ? void 0 : (_result_data3 = result.data) === null || _result_data3 === void 0 ? void 0 : _result_data3.current_phase))\n                                });\n                                setChatHistory({\n                                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>{\n                                        const newHistory = [\n                                            ...prev,\n                                            aiMessage\n                                        ];\n                                        console.log(\"[ClassroomContent] \\uD83D\\uDCDD Chat history updated. Total messages: \".concat(newHistory.length));\n                                        return newHistory;\n                                    }\n                                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                                handleLessonPhaseUpdates(result);\n                                return true;\n                            } catch (parseError) {\n                                throw new Error(\"Failed to parse server response: \".concat(parseError.message));\n                            }\n                        }\n                        if (axiosResponse.status === 401) {\n                            throw new Error('Authentication failed. Please log in again.');\n                        } else if (axiosResponse.status === 429) {\n                            const retryAfter = axiosResponse.headers['retry-after'] || 5;\n                            if (attempt < MAX_RETRIES) {\n                                await new Promise({\n                                    \"ClassroomContent.useCallback[handleAiInteraction]\": (resolve)=>setTimeout(resolve, Number(retryAfter) * 1000)\n                                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                                continue;\n                            }\n                            throw new Error('Server is busy. Please try again later.');\n                        }\n                        const errorMessage = ((_axiosResponse_data = axiosResponse.data) === null || _axiosResponse_data === void 0 ? void 0 : _axiosResponse_data.message) || axiosResponse.statusText || \"Request failed with status \".concat(axiosResponse.status);\n                        throw new Error(errorMessage);\n                    } catch (error) {\n                        lastError = error;\n                        if (attempt < MAX_RETRIES) {\n                            const backoffTime = Math.pow(2, attempt) * 1000;\n                            console.warn(\"Attempt \".concat(attempt + 1, \" failed, retrying in \").concat(backoffTime, \"ms...\"), error);\n                            await new Promise({\n                                \"ClassroomContent.useCallback[handleAiInteraction]\": (resolve)=>setTimeout(resolve, backoffTime)\n                            }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                        }\n                    }\n                }\n                throw lastError || new Error('Request failed after multiple attempts');\n                // This code is unreachable due to the throw statement above\n                // Keeping it for reference but it won't be executed\n                console.warn('This code should not be reachable - check for unreachable code');\n                return false;\n            } catch (error) {\n                var _error_message, _error_message1;\n                const errorMessage = getErrorMessage(error);\n                console.error(\"[ClassroomContent] Error during AI interaction:\", errorMessage, \"\\nFull error:\", error);\n                // Update UI with error state\n                setUiError(errorMessage);\n                // Add error message to chat for better UX\n                const errorMessageObj = {\n                    role: 'assistant',\n                    content: \"I'm sorry, I encountered an error: \".concat(errorMessage),\n                    timestamp: new Date().toISOString(),\n                    status: 'error'\n                };\n                setChatHistory({\n                    \"ClassroomContent.useCallback[handleAiInteraction]\": (prev)=>[\n                            ...prev,\n                            errorMessageObj\n                        ]\n                }[\"ClassroomContent.useCallback[handleAiInteraction]\"]);\n                // Show toast for non-timeout errors to avoid duplicate messages\n                if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && !((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('Network Error'))) {\n                    toast({\n                        title: \"AI Service Error\",\n                        description: errorMessage,\n                        variant: \"destructive\",\n                        duration: 10000\n                    });\n                }\n                // Log the error for debugging\n                logInteraction('ai_interaction_error', {\n                    lessonRef,\n                    sessionId: sessionIdToUse,\n                    error: errorMessage,\n                    stack: error.stack,\n                    timestamp: new Date().toISOString()\n                });\n                return false;\n            } finally{\n                // Always ensure loading state is reset\n                setIsAiLoading(false);\n            }\n        }\n    }[\"ClassroomContent.useCallback[handleAiInteraction]\"], [\n        sessionIdFromUrlProp,\n        backendSessionId || undefined,\n        lessonRef,\n        studentId,\n        countryProp,\n        curriculumProp,\n        gradeProp,\n        levelProp,\n        subjectProp,\n        getAuthHeaders,\n        toast,\n        logInteraction,\n        chatHistory,\n        timeRemaining // Add timeRemaining to dependencies\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            const effectRunId = Date.now(); // For unique logging per run\n            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Running. Deps state:\"), {\n                sessionIdFromUrlProp,\n                backendSessionId,\n                lessonRef,\n                studentId,\n                isReady,\n                initialAiSent: initialAiInteractionSentRef.current,\n                chatHistoryLength: chatHistory.length,\n                isPageLoading\n            });\n            if (initialAiInteractionSentRef.current) {\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial AI interaction already attempted/sent. Current page loading: \").concat(isPageLoading, \".\"));\n                // If setup was done and page is still loading, ensure it stops.\n                if (isPageLoading) setIsPageLoading(false);\n                return;\n            }\n            if (!isReady) {\n                console.warn(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Waiting for context (ready: \").concat(isReady, \").\"));\n                if (!isPageLoading) setIsPageLoading(true); // Keep loading screen if waiting for these\n                return;\n            }\n            // Once user session and context are ready, validate critical props and session ID\n            if (!lessonRef || !studentId || !(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {\n                let missingInfo = [];\n                if (!lessonRef) missingInfo.push(\"lessonRefProp\");\n                if (!studentId) missingInfo.push(\"studentIdProp\");\n                if (!(sessionIdFromUrlProp && typeof sessionIdFromUrlProp === 'string' && sessionIdFromUrlProp.trim() !== '')) {\n                    missingInfo.push(\"valid sessionIdFromUrlProp (received: '\".concat(sessionIdFromUrlProp, \"', type: \").concat(typeof sessionIdFromUrlProp, \")\"));\n                }\n                const errorMessage = \"Critical info missing for ClassroomContent init: \".concat(missingInfo.join(', '), \". Please ensure the lesson was started correctly.\");\n                console.error(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Prerequisite check failed:\"), errorMessage, {\n                    lessonRefFromProp: lessonRef,\n                    studentIdFromProp: studentId,\n                    contextSessionId: backendSessionId\n                });\n                setUiError(errorMessage);\n                toast({\n                    title: \"Lesson Load Error\",\n                    description: errorMessage,\n                    variant: \"destructive\",\n                    duration: 10000\n                });\n                setIsPageLoading(false);\n                initialAiInteractionSentRef.current = true; // Mark as \"attempted\" to prevent loops\n                return;\n            }\n            // All prerequisites are met: lessonRef, studentId from props, and sessionIdFromUrlProp are valid.\n            // The initialSetupTriggeredRef is primarily to ensure the initial AI message is sent only once.\n            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] All prerequisites met. Using sessionIdFromUrlProp: '\").concat(sessionIdFromUrlProp, \"', lessonRef: '\").concat(lessonRef, \"'\"));\n            // Ensure isPageLoading is true before we potentially make an async call or set lessonDetails\n            if (!isPageLoading) setIsPageLoading(true);\n            if (!lessonDetails) {\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Populating lessonDetails.\"));\n                setLessonDetails({\n                    title: \"Lesson: \".concat(lessonRef),\n                    subject: subjectProp || 'N/A',\n                    grade: gradeProp || 'N/A',\n                    level: levelProp || 'N/A',\n                    country: countryProp || 'N/A',\n                    curriculum: curriculumProp || 'N/A',\n                    lessonRef: lessonRef\n                });\n            }\n            // Send initial system message if chat is empty and it hasn't been sent yet\n            if (chatHistory.length === 0 && !initialAiInteractionSentRef.current) {\n                initialAiInteractionSentRef.current = true;\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Triggering initial AI interaction (Diagnostic Start Message) with sessionIdFromUrlProp: \").concat(sessionIdFromUrlProp));\n                // CRITICAL FIX: Send diagnostic-specific message instead of generic system message\n                handleAiInteraction(\"Start diagnostic assessment\", true, sessionIdFromUrlProp).then({\n                    \"ClassroomContent.useEffect\": (success)=>{\n                        if (success) {\n                            console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial system message AI interaction completed successfully.\"));\n                        } else {\n                            console.warn(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial system message AI interaction reported failure.\"));\n                        }\n                    }\n                }[\"ClassroomContent.useEffect\"]).catch({\n                    \"ClassroomContent.useEffect\": (err)=>{\n                        console.error(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Promise rejected from initial AI interaction:\"), err.message);\n                    }\n                }[\"ClassroomContent.useEffect\"]).finally({\n                    \"ClassroomContent.useEffect\": ()=>{\n                        setIsPageLoading(false);\n                    }\n                }[\"ClassroomContent.useEffect\"]);\n            } else {\n                // Initial message already sent or chat not empty, just ensure loading is false\n                console.log(\"[ClassroomContent Effect #\".concat(effectRunId, \"] Initial AI message condition not met (chatHistory: \").concat(chatHistory.length, \", initialSentRef: \").concat(initialAiInteractionSentRef.current, \"). Setting page loading false.\"));\n                setIsPageLoading(false);\n            }\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        // Key dependencies that trigger re-evaluation of initial setup:\n        sessionIdFromUrlProp,\n        backendSessionId || undefined,\n        lessonRef,\n        studentId,\n        isReady,\n        // Other dependencies that, if they change, might necessitate re-evaluation or are used:\n        subjectProp,\n        gradeProp,\n        levelProp,\n        countryProp,\n        curriculumProp,\n        chatHistory.length,\n        handleAiInteraction,\n        lessonDetails,\n        isPageLoading,\n        toast // Stable\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            var _chatBottomRef_current;\n            (_chatBottomRef_current = chatBottomRef.current) === null || _chatBottomRef_current === void 0 ? void 0 : _chatBottomRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        chatHistory\n    ]);\n    // DEBUG: Monitor phase changes to ensure state updates are working\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomContent.useEffect\": ()=>{\n            console.warn('🎯 PHASE CHANGE DETECTED:', {\n                previousPhase: 'tracked separately',\n                currentPhase: currentLessonPhase,\n                timestamp: new Date().toISOString()\n            });\n            // Force console alert for phase changes\n            if (currentLessonPhase) {\n                console.warn(\"\\uD83D\\uDE80 FRONTEND PHASE IS NOW: \".concat(currentLessonPhase));\n            }\n        }\n    }[\"ClassroomContent.useEffect\"], [\n        currentLessonPhase\n    ]);\n    // --- Render Logic ---\n    if (isPageLoading && !initialAiInteractionSentRef.current && !uiError) {\n        console.log(\"[ClassroomContent Render] Showing loading spinner:\", {\n            isPageLoading,\n            initialAiSent: initialAiInteractionSentRef.current,\n            uiError\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"large\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 75\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-2\",\n                    children: \"Initializing Classroom...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 106\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 839,\n            columnNumber: 16\n        }, undefined);\n    }\n    if (uiError) {\n        console.log(\"[ClassroomContent Render] Showing UI error display:\", uiError);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-center items-center h-screen p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    title: \"Lesson Error\",\n                    message: uiError\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 846,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    onClick: ()=>router.push('/dashboard'),\n                    className: \"mt-4\",\n                    children: \"Go to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 845,\n            columnNumber: 13\n        }, undefined);\n    }\n    const currentSessionId = sessionIdFromUrlProp || backendSessionId;\n    if (!currentSessionId || !lessonRef || !studentId || !lessonDetails) {\n        const missingRenderData = {\n            sessionIdFromUrl: sessionIdFromUrlProp,\n            contextSessionId: backendSessionId,\n            propLessonRef: lessonRef,\n            propStudentId: studentId,\n            currentLessonDetails: lessonDetails\n        };\n        console.error(\"[ClassroomContent Render] Critical data missing just before render. This indicates a logic flow issue.\", missingRenderData);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col justify-center items-center h-screen p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    title: \"Content Load Error\",\n                    message: \"Essential lesson data is still missing after initialization attempts. Please try returning to the dashboard and starting the lesson again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                    onClick: ()=>router.push('/dashboard'),\n                    className: \"mt-4\",\n                    children: \"Go to Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 863,\n            columnNumber: 13\n        }, undefined);\n    }\n    // All checks passed, render the main classroom UI\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundaryFallback, {\n            error: new Error('Component error'),\n            resetErrorBoundary: ()=>window.location.reload()\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 873,\n            columnNumber: 30\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-screen bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_LessonHeader__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    lessonTitle: lessonDetails.title,\n                    subjectName: lessonDetails.subject,\n                    gradeLevel: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_13__.formatGradeLevelForDisplay)(lessonDetails.grade),\n                    currentTeachingLevel: currentTeachingLevel,\n                    levelAdjustmentHistory: levelAdjustmentHistory,\n                    onEnd: ()=>{\n                        if (backendSessionId && typeof backendSessionId === 'string' && lessonRefProp && studentIdProp) {\n                            logInteraction('lesson_ended_by_user', {\n                                lessonRef: lessonRefProp,\n                                sessionId: backendSessionId,\n                                timeElapsed: LESSON_DURATION_MINUTES * 60 - timeRemaining,\n                                timeRemaining\n                            });\n                        }\n                        router.push('/dashboard');\n                    },\n                    connectionStatus: isAiLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 893,\n                        columnNumber: 35\n                    }, void 0) : currentLessonPhase === LESSON_PHASE_COMPLETED ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-green-500 text-xs font-semibold\",\n                        children: [\n                            \"Lesson Complete! \",\n                            formatTime(timeRemaining),\n                            \" remaining\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 25\n                    }, void 0) : uiError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 text-xs font-semibold\",\n                        children: \"Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 25\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-500 text-xs font-semibold\",\n                                        children: [\n                                            formatTime(timeRemaining),\n                                            \" remaining\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    (()=>{\n                                        const timerStatus = getTimerStatus();\n                                        if (timerStatus.isInQuizPhase) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-600 text-xs\",\n                                                children: \"Quiz Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 48\n                                            }, void 0);\n                                        } else if (timerStatus.timeUntilQuizTransition <= 300) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-500 text-xs\",\n                                                children: [\n                                                    \"Quiz in \",\n                                                    formatTime(timerStatus.timeUntilQuizTransition)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 48\n                                            }, void 0);\n                                        }\n                                        return null;\n                                    })()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 29\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-2 bg-gray-200 rounded-full overflow-hidden relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full \".concat(timeRemaining > LESSON_DURATION_MINUTES * 60 * 0.5 ? 'bg-green-500' : timeRemaining > LESSON_DURATION_MINUTES * 60 * 0.25 ? 'bg-yellow-500' : 'bg-red-500'),\n                                        style: {\n                                            width: \"\".concat(Math.max(5, timeRemaining / (LESSON_DURATION_MINUTES * 60) * 100), \"%\")\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 w-0.5 h-full bg-amber-500 z-10\",\n                                        style: {\n                                            left: \"\".concat(37.5 / 45 * 100, \"%\")\n                                        },\n                                        title: \"Quiz transition point (37.5 min)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 33\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 29\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 875,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \"hidden md:block w-64 lg:w-72 xl:w-1/4 p-4 border-r overflow-y-auto bg-slate-50 dark:bg-slate-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"shadow-md mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                className: \"text-lg text-slate-700 dark:text-slate-200\",\n                                                children: \"Lesson Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                            className: \"space-y-1.5 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Topic:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Subject:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.subject\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Grade:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.grade\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Curriculum:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        lessonDetails.curriculum\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-slate-200 dark:border-slate-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Phase:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-blue-600 dark:text-blue-400\",\n                                                            children: currentLessonPhase || 'Initializing...'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 56\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 953,\n                                                    columnNumber: 29\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Session ID:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 32\n                                                        }, undefined),\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: sessionIdFromUrlProp || backendSessionId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 61\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiagnosticProgress__WEBPACK_IMPORTED_MODULE_16__.DiagnosticProgress, {\n                                    currentQuestionIndex: diagnosticProgress.currentQuestionIndex,\n                                    totalQuestions: diagnosticProgress.totalQuestions,\n                                    currentProbingLevel: diagnosticProgress.currentProbingLevel,\n                                    questionsCompleted: diagnosticProgress.questionsCompleted,\n                                    currentPhase: currentLessonPhase,\n                                    isComplete: diagnosticProgress.isComplete,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 21\n                                }, undefined),\n                                levelAdjustmentHistory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_LevelAdjustmentHistory__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    adjustments: levelAdjustmentHistory,\n                                    currentLevel: currentTeachingLevel,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 flex flex-col bg-white dark:bg-slate-900\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_lesson_components_TutorChat__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    lessonRef: lessonRef,\n                                    studentId: studentId,\n                                    sessionId: sessionIdFromUrlProp || backendSessionId || '',\n                                    chatMessages: chatHistory,\n                                    onSendMessage: handleAiInteraction,\n                                    isProcessing: isAiLoading,\n                                    error: uiError,\n                                    getAuthHeaders: getAuthHeaders,\n                                    lessonTitle: lessonDetails === null || lessonDetails === void 0 ? void 0 : lessonDetails.title,\n                                    role: \"instructor\" // or \"tutor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 980,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: chatBottomRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                            lineNumber: 979,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n            lineNumber: 874,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\ClassroomContent.tsx\",\n        lineNumber: 873,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClassroomContent, \"iSxiD6RNBz22xxxTSTnUfdo3bEA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_interaction_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession,\n        _hooks_useLessonTimer__WEBPACK_IMPORTED_MODULE_4__.useLessonTimer\n    ];\n});\n_c1 = ClassroomContent;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClassroomContent);\nvar _c, _c1;\n$RefreshReg$(_c, \"ErrorBoundaryFallback\");\n$RefreshReg$(_c1, \"ClassroomContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/classroom/page.tsx":
/*!************************************!*\
  !*** ./src/app/classroom/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClassroomPage),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* harmony import */ var _ClassroomContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClassroomContent */ \"(app-pages-browser)/./src/app/classroom/ClassroomContent.tsx\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(app-pages-browser)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ dynamic,default auto */ \nvar _s = $RefreshSig$();\nconst dynamic = 'force-dynamic';\n\n\n\n\n // Using simplified session hook\n\n\n// This is the actual page component\nfunction ClassroomPageContentInternal() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoading: isUserSessionLoading, isReady: isUserSessionContextReady, setBackendSessionId, backendSessionId// Getter for the lesson session ID\n     } = (0,_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    // Get parameters that ClassroomContent will need.\n    // These are directly from the URL, set by start-lesson/page.tsx's redirect.\n    const sessionIdFromUrl = searchParams.get('session_id'); // Key used in redirect\n    const lessonRefFromUrl = searchParams.get('lessonRef');\n    const studentIdFromUrl = searchParams.get('studentId');\n    const countryFromUrl = searchParams.get('country');\n    const curriculumFromUrl = searchParams.get('curriculum');\n    const gradeFromUrl = searchParams.get('grade');\n    const levelFromUrl = searchParams.get('level');\n    const subjectFromUrl = searchParams.get('subject');\n    // Effect to sync backendSessionId between URL and context\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClassroomPageContentInternal.useEffect\": ()=>{\n            // Only attempt to set context if session context is ready AND sessionIdFromUrl is present\n            if (isUserSessionContextReady && sessionIdFromUrl) {\n                if (sessionIdFromUrl !== backendSessionId) {\n                    console.log(\"[ClassroomPage] Context ready. Updating backendSessionId in context from URL: '\".concat(sessionIdFromUrl, \"' (was: '\").concat(backendSessionId, \"')\"));\n                    setBackendSessionId(sessionIdFromUrl);\n                } else {\n                    console.log(\"[ClassroomPage] Context backendSessionId ('\".concat(backendSessionId, \"') already matches URL sessionId ('\").concat(sessionIdFromUrl, \"'). No update needed.\"));\n                }\n            } else if (isUserSessionContextReady && !sessionIdFromUrl) {\n                console.warn(\"[ClassroomPage] Context ready, but no session_id in URL. Current context backendSessionId is:\", backendSessionId);\n            // Uncomment the following line if you want to clear the backendSessionId when it's not in URL\n            // if (backendSessionId) setBackendSessionId(null);\n            }\n        }\n    }[\"ClassroomPageContentInternal.useEffect\"], [\n        sessionIdFromUrl,\n        setBackendSessionId,\n        backendSessionId,\n        isUserSessionContextReady\n    ]);\n    // Loading state: wait for NextAuth session, context to be ready, and sessionId from URL\n    if (isUserSessionLoading || !isUserSessionContextReady || !sessionIdFromUrl) {\n        console.log(\"[ClassroomPage] Loading: Waiting for user session, context, or sessionIdFromUrl.\", {\n            isUserSessionLoading,\n            isUserSessionContextReady,\n            sessionIdFromUrl\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    size: \"large\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-4 text-gray-600\",\n                    children: \"Preparing Classroom...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 13\n        }, this);\n    }\n    // Parameter validation for rendering ClassroomContent\n    if (!lessonRefFromUrl || !studentIdFromUrl || !subjectFromUrl || !gradeFromUrl || !levelFromUrl || !countryFromUrl || !curriculumFromUrl) {\n        const missing = [\n            !lessonRefFromUrl ? 'lessonRef' : null,\n            !studentIdFromUrl ? 'studentId' : null,\n            !subjectFromUrl ? 'subject' : null,\n            !gradeFromUrl ? 'grade' : null,\n            !levelFromUrl ? 'level' : null,\n            !countryFromUrl ? 'country' : null,\n            !curriculumFromUrl ? 'curriculum' : null\n        ].filter(Boolean).join(', ');\n        console.error(\"[ClassroomPage] Critical props missing for ClassroomContent:\", missing);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-xl mx-auto p-6 bg-white rounded shadow-md text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-8 w-8 text-red-500 mx-auto mb-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-red-700 mb-2\",\n                        children: \"Cannot Load Lesson\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-6 text-gray-600\",\n                        children: [\n                            \"Essential information to load the lesson is missing from the URL (Details: \",\n                            missing,\n                            \"). Please try starting the lesson again from your dashboard.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/dashboard\",\n                        className: \"inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                        children: \"Return to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n            lineNumber: 80,\n            columnNumber: 13\n        }, this);\n    }\n    // Now we are sure sessionIdFromUrl and other props are available\n    console.log(\"[ClassroomPage] Rendering ClassroomContent. Passing sessionIdFromUrl:\", sessionIdFromUrl);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassroomContent__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sessionIdFromUrlProp: sessionIdFromUrl,\n        lessonRefProp: lessonRefFromUrl,\n        studentIdProp: studentIdFromUrl,\n        countryProp: countryFromUrl,\n        curriculumProp: curriculumFromUrl,\n        gradeProp: gradeFromUrl,\n        levelProp: levelFromUrl,\n        subjectProp: subjectFromUrl\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 9\n    }, this);\n}\n_s(ClassroomPageContentInternal, \"iL0QyhsfJKleX5dqUw3fWoATqNQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_5__.useSession\n    ];\n});\n_c = ClassroomPageContentInternal;\nfunction ClassroomPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    size: \"large\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 91\n                }, void 0),\n                \"Loading Classroom Page...\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 28\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClassroomPageContentInternal, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 12\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\classroom\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 8\n    }, this);\n}\n_c1 = ClassroomPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ClassroomPageContentInternal\");\n$RefreshReg$(_c1, \"ClassroomPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/classroom/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DiagnosticProgress.tsx":
/*!***********************************************!*\
  !*** ./src/components/DiagnosticProgress.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagnosticProgress: () => (/* binding */ DiagnosticProgress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/shadcn/card */ \"(app-pages-browser)/./src/components/shadcn/card.tsx\");\n/* harmony import */ var _components_shadcn_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shadcn/progress */ \"(app-pages-browser)/./src/components/shadcn/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,CheckCircle,Circle!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,CheckCircle,Circle!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,CheckCircle,Circle!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle.js\");\n\n\n\n\n\nconst DiagnosticProgress = (param)=>{\n    let { currentQuestionIndex, totalQuestions, currentProbingLevel, questionsCompleted, currentPhase, isComplete, className = '' } = param;\n    const progressPercentage = questionsCompleted / totalQuestions * 100;\n    // Determine if we're in a diagnostic phase\n    const isDiagnosticPhase = (currentPhase === null || currentPhase === void 0 ? void 0 : currentPhase.includes('diagnostic')) || false;\n    if (!isDiagnosticPhase && !isComplete) {\n        return null; // Don't show during non-diagnostic phases\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"\".concat(className, \" border-blue-200 bg-blue-50\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-blue-900\",\n                            children: isComplete ? 'Diagnostic Complete!' : 'Diagnostic Assessment'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined),\n                !isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-700\",\n                                            children: [\n                                                \"Question \",\n                                                currentQuestionIndex + 1,\n                                                \" of \",\n                                                totalQuestions\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600 font-medium\",\n                                            children: [\n                                                \"Level \",\n                                                currentProbingLevel\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_progress__WEBPACK_IMPORTED_MODULE_3__.Progress, {\n                                    value: progressPercentage,\n                                    className: \"h-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm text-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Progress:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: Array.from({\n                                        length: totalQuestions\n                                    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: i < questionsCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 23\n                                            }, undefined) : i === currentQuestionIndex ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-500 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 23\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-xs text-blue-600\",\n                            children: [\n                                \"Phase: \",\n                                currentPhase || 'Initializing...'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true),\n                isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-8 w-8 text-green-500 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-700 font-medium\",\n                            children: \"Assessment completed! Moving to lesson content...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\DiagnosticProgress.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n_c = DiagnosticProgress;\nvar _c;\n$RefreshReg$(_c, \"DiagnosticProgress\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DiagnosticProgress.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/lesson-components/ErrorBoundary.tsx":
/*!************************************************************!*\
  !*** ./src/components/lesson-components/ErrorBoundary.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_shadcn_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/shadcn/alert */ \"(app-pages-browser)/./src/components/shadcn/alert.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error', error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            var _this_state_error;\n            return this.props.fallback ? this.props.fallback : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_alert__WEBPACK_IMPORTED_MODULE_2__.AlertDescription, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: (_this_state_error = this.state.error) === null || _this_state_error === void 0 ? void 0 : _this_state_error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>this.setState({\n                                    hasError: false,\n                                    error: undefined\n                                }),\n                            className: \"mt-4\",\n                            children: \"Try again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\ErrorBoundary.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lesson-components/ErrorBoundary.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/lesson-components/LevelAdjustmentHistory.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/lesson-components/LevelAdjustmentHistory.tsx ***!
  \*********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/shadcn/card */ \"(app-pages-browser)/./src/components/shadcn/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Clock,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst LevelAdjustmentHistory = (param)=>{\n    let { adjustments, currentLevel, className = \"\" } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (adjustments.length === 0) {\n        return null;\n    }\n    const formatTimeAgo = (timestamp)=>{\n        const now = Date.now();\n        const diffMinutes = Math.floor((now - timestamp) / (1000 * 60));\n        if (diffMinutes < 1) return \"Just now\";\n        if (diffMinutes < 60) return \"\".concat(diffMinutes, \"m ago\");\n        const diffHours = Math.floor(diffMinutes / 60);\n        if (diffHours < 24) return \"\".concat(diffHours, \"h ago\");\n        const diffDays = Math.floor(diffHours / 24);\n        return \"\".concat(diffDays, \"d ago\");\n    };\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 0.8) return \"text-green-600\";\n        if (confidence >= 0.6) return \"text-yellow-600\";\n        return \"text-orange-600\";\n    };\n    const recentAdjustments = adjustments.slice(-3); // Show last 3 adjustments\n    const olderAdjustments = adjustments.slice(0, -3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    className: \"flex items-center justify-between text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Level Adjustments\",\n                                currentLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"ml-2\",\n                                    children: [\n                                        \"Current: Level \",\n                                        currentLevel\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        adjustments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"h-6 px-2\",\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-3 h-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        recentAdjustments.map((adjustment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            adjustment.direction === 'up' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    \"Level \",\n                                                    adjustment.fromLevel,\n                                                    \" → \",\n                                                    adjustment.toLevel\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: adjustment.direction === 'up' ? 'default' : 'outline',\n                                                className: \"text-xs\",\n                                                children: adjustment.direction === 'up' ? 'Increased' : 'Decreased'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: getConfidenceColor(adjustment.confidence),\n                                                children: [\n                                                    Math.round(adjustment.confidence * 100),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatTimeAgo(adjustment.timestamp)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, adjustment.timestamp, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)),\n                        isExpanded && olderAdjustments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-2 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500 mb-2 block\",\n                                        children: \"Earlier adjustments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    olderAdjustments.map((adjustment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-2 bg-gray-25 rounded-lg mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        adjustment.direction === 'up' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-3 h-3 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Clock_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-3 h-3 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs\",\n                                                            children: [\n                                                                \"Level \",\n                                                                adjustment.fromLevel,\n                                                                \" → \",\n                                                                adjustment.toLevel\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-xs text-gray-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: getConfidenceColor(adjustment.confidence),\n                                                            children: [\n                                                                Math.round(adjustment.confidence * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: formatTimeAgo(adjustment.timestamp)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, adjustment.timestamp, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false),\n                        adjustments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t pt-2 mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-xs text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Total adjustments: \",\n                                            adjustments.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"↑\",\n                                            adjustments.filter((a)=>a.direction === 'up').length,\n                                            \"↓\",\n                                            adjustments.filter((a)=>a.direction === 'down').length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\LevelAdjustmentHistory.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LevelAdjustmentHistory, \"FPNvbbHVlWWR4LKxxNntSxiIS38=\");\n_c = LevelAdjustmentHistory;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LevelAdjustmentHistory);\nvar _c;\n$RefreshReg$(_c, \"LevelAdjustmentHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lesson-components/LevelAdjustmentHistory.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/lesson-components/TutorChat.tsx":
/*!********************************************************!*\
  !*** ./src/components/lesson-components/TutorChat.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_shadcn_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/shadcn/input */ \"(app-pages-browser)/./src/components/shadcn/input.tsx\");\n/* harmony import */ var _components_shadcn_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/shadcn/button */ \"(app-pages-browser)/./src/components/shadcn/button.tsx\");\n/* harmony import */ var _components_shadcn_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shadcn/alert */ \"(app-pages-browser)/./src/components/shadcn/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,MessageCircle!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,MessageCircle!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* eslint-env browser */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n // Removed ArrowRight\n// Use the Next.js API routes instead of direct backend connections\n// This avoids Content Security Policy (CSP) issues\nconst API_BASE = window.location.origin; // Use same origin to avoid CSP issues\n// Log the API base for debugging\nconsole.log('[TutorChat] Using API_BASE:', API_BASE);\n// The TutorChat component\nconst TutorChat = (param)=>{\n    let { chatMessages, lessonRef, sessionId, studentId, onSendMessage, isProcessing, error, // Destructure other needed props\n    lessonTitle, compact = false, className = '', role = 'instructor', getAuthHeaders } = param;\n    _s();\n    console.log('[TutorChat] Rendering with chatMessages count:', chatMessages.length); // Log received messages\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Remove internal state for processing and error, rely on props\n    // const [isProcessing, setIsProcessingInternal] = useState(false);\n    // const [error, setErrorInternal] = useState<string | null>(null);\n    // Remove local interactions state if managed by parent\n    // const [interactionsLeft, setInteractionsLeft] = useState(maxInteractions);\n    // Remove local segment state if managed by parent\n    // const [currentSegmentIndex, setCurrentSegmentIndex] = useState<number>(-1);\n    // Remove local AI introduction state/logic, parent handles initial message\n    // const [hasIntroducedAI, setHasIntroducedAI] = useState(false);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Scroll when the messages prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TutorChat.useEffect\": ()=>{\n            console.log('[TutorChat] chatMessages prop updated, scrolling to bottom.');\n            scrollToBottom();\n        }\n    }[\"TutorChat.useEffect\"], [\n        chatMessages\n    ]);\n    // Simplified handleSendMessage - calls parent's handler\n    const handleSendMessage = ()=>{\n        if (!inputMessage.trim() || isProcessing) {\n            return;\n        }\n        const messageToSend = inputMessage;\n        setInputMessage(''); // Clear input immediately\n        // Call the parent's function to handle the actual API call and state update\n        onSendMessage(messageToSend).then((success)=>{\n            if (!success) {\n                // Optionally restore input message if sending failed\n                // setInputMessage(messageToSend); \n                console.error(\"[TutorChat] Parent onSendMessage indicated failure.\");\n            }\n        }).catch((err)=>{\n            console.error(\"[TutorChat] Error calling parent onSendMessage:\", err);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 h-full flex flex-col \".concat(className),\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto space-y-4 p-2\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-gray-400 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-8 w-8 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: role === 'instructor' ? 'Your AI instructor will guide you through this lesson' : 'Ask a question about the lesson...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 14\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg max-w-[80%] break-words \".concat(item.role === 'user' ? 'bg-blue-100 ml-auto' : 'bg-gray-100 mr-auto'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-800 whitespace-pre-wrap\",\n                                    children: item.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                    className: \"text-xs text-gray-500 mt-1 block\",\n                                    children: item.timestamp ? new Date(item.timestamp).toLocaleTimeString([], {\n                                        hour: '2-digit',\n                                        minute: '2-digit'\n                                    }) : ''\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, item.id || \"msg-\".concat(index), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            value: inputMessage,\n                            onChange: (e)=>setInputMessage(e.target.value),\n                            placeholder: \"Ask a question about the lesson...\",\n                            disabled: isProcessing,\n                            onKeyDown: (e)=>{\n                                if (e.key === 'Enter' && !e.shiftKey) {\n                                    e.preventDefault();\n                                    handleSendMessage();\n                                }\n                            },\n                            className: \"flex-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shadcn_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSendMessage,\n                            disabled: isProcessing,\n                            className: \"shrink-0\",\n                            children: isProcessing ? 'Sending...' : 'Send'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\lesson-components\\\\TutorChat.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TutorChat, \"9KREkdI0YzIJ1Ot4XwJhWgGhtwo=\");\n_c = TutorChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TutorChat);\nvar _c;\n$RefreshReg$(_c, \"TutorChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/lesson-components/TutorChat.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/shadcn/LessonHeader.tsx":
/*!************************************************!*\
  !*** ./src/components/shadcn/LessonHeader.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n// src/components/shadcn/LessonHeader.tsx\n\n\n\n\nconst LessonHeader = (param)=>{\n    let { // New props\n    title, progress, currentTeachingLevel, levelAdjustmentHistory = [], // Legacy props\n    lessonTitle, subjectName, gradeLevel, onEnd, connectionStatus } = param;\n    // Use new props if available, otherwise fall back to legacy props\n    const displayTitle = title || lessonTitle || 'Lesson';\n    const isLegacyMode = !title && (lessonTitle || subjectName || gradeLevel);\n    // Get the most recent adjustment for trend indicator\n    const recentAdjustment = levelAdjustmentHistory.length > 0 ? levelAdjustmentHistory[levelAdjustmentHistory.length - 1] : null;\n    // Show trend icon only if adjustment was recent (within last 10 minutes)\n    const showTrendIcon = recentAdjustment && Date.now() - recentAdjustment.timestamp < 10 * 60 * 1000;\n    const getTrendIcon = ()=>{\n        if (!showTrendIcon || !recentAdjustment) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-3 h-3\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n            lineNumber: 58,\n            columnNumber: 53\n        }, undefined);\n        if (recentAdjustment.direction === 'up') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-3 h-3 text-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                lineNumber: 61,\n                columnNumber: 14\n            }, undefined);\n        } else if (recentAdjustment.direction === 'down') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-3 h-3 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                lineNumber: 63,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-3 h-3\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n            lineNumber: 65,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getLevelBadgeVariant = ()=>{\n        if (!showTrendIcon || !recentAdjustment) return \"secondary\";\n        return recentAdjustment.direction === 'up' ? \"default\" : \"outline\";\n    };\n    // Legacy mode: render the original header structure\n    if (isLegacyMode) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"flex justify-between items-center p-4 bg-background border-b\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold\",\n                            children: displayTitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        subjectName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: [\n                                subjectName,\n                                \" \",\n                                gradeLevel && \"• \".concat(gradeLevel)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined),\n                        currentTeachingLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: getLevelBadgeVariant(),\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    getTrendIcon(),\n                                    \"Level \",\n                                    currentTeachingLevel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        connectionStatus,\n                        onEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onEnd,\n                            className: \"px-3 py-1 text-sm bg-red-100 text-red-600 rounded hover:bg-red-200 transition-colors\",\n                            children: \"End Lesson\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined);\n    }\n    // New mode: render the enhanced header with progress\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold\",\n                        children: displayTitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined),\n                    currentTeachingLevel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                            variant: getLevelBadgeVariant(),\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                getTrendIcon(),\n                                \"Level \",\n                                currentTeachingLevel\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2 mt-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-500 h-2 rounded-full\",\n                    style: {\n                        width: \"\".concat(progress, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\LessonHeader.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LessonHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LessonHeader);\nvar _c;\n$RefreshReg$(_c, \"LessonHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/LessonHeader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-interaction-logger.ts":
/*!*********************************************!*\
  !*** ./src/hooks/use-interaction-logger.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Queue for tracking pending interactions that need to be retried\nconst pendingInteractions = [];\nlet isRetrying = false;\nconst RETRY_DELAY = 5000; // 5 seconds\n// Add a function to retry pending interactions\nasync function retryPendingInteractions() {\n    if (isRetrying || pendingInteractions.length === 0) return;\n    isRetrying = true;\n    console.log(\"Retrying \".concat(pendingInteractions.length, \" pending interactions\"));\n    // Take a copy of the current interactions\n    const interactionsToRetry = [\n        ...pendingInteractions\n    ];\n    pendingInteractions.length = 0; // Clear the array\n    for (const interaction of interactionsToRetry){\n        try {\n            // Get student ID from localStorage with extended fallbacks\n            const getStudentId = ()=>{\n                const fromInteraction = interaction.studentId;\n                const fromLocalStorage = localStorage.getItem('current_student_id');\n                const fromAlternateStorage = localStorage.getItem('student_id');\n                const fromSessionStorage = sessionStorage.getItem('student_id');\n                // Log all possible sources for debugging\n                console.log('[useInteractionLogger/retry] Student ID sources:', {\n                    fromInteraction,\n                    fromLocalStorage,\n                    fromAlternateStorage,\n                    fromSessionStorage\n                });\n                // Use the first valid ID found, in priority order\n                return fromInteraction || fromLocalStorage || fromAlternateStorage || fromSessionStorage;\n            };\n            const studentId = getStudentId();\n            if (!studentId) {\n                console.warn('[useInteractionLogger/retry] No student ID found, interaction may fail');\n            }\n            const sessionId = interaction.sessionId || localStorage.getItem('current_session');\n            console.log(\"[useInteractionLogger/retry] Sending interaction with studentId: \".concat(studentId, \", sessionId: \").concat(sessionId));\n            const response = await fetch(\"/api/lesson-interaction\", {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Session-ID': sessionId,\n                    'X-Student-ID': studentId // Add student ID to headers\n                },\n                body: JSON.stringify({\n                    sessionId: sessionId,\n                    studentId: studentId,\n                    interactionType: interaction.type,\n                    data: interaction.data,\n                    timestamp: interaction.timestamp\n                })\n            });\n            if (!response.ok) {\n                console.warn(\"Failed to retry interaction: \".concat(interaction.type, \" (\").concat(response.status, \")\"));\n                pendingInteractions.push(interaction);\n            } else {\n                console.log(\"Successfully retried interaction: \".concat(interaction.type));\n            }\n        } catch (error) {\n            console.warn(\"Error retrying interaction: \".concat(interaction.type), error);\n            pendingInteractions.push(interaction);\n        }\n    }\n    isRetrying = false;\n    // If we still have pending interactions, schedule another retry\n    if (pendingInteractions.length > 0) {\n        setTimeout(retryPendingInteractions, RETRY_DELAY);\n    }\n}\nconst useInteractionLogger = ()=>{\n    // Use a ref to track the rate limit\n    const lastInteractionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const RATE_LIMIT = 300; // 300ms between interactions\n    const logInteraction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInteractionLogger.useCallback[logInteraction]\": async function(interactionType) {\n            let engagementData = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n            // Get the session ID with fallbacks\n            const sessionId = localStorage.getItem('current_session') || localStorage.getItem('sessionId');\n            // Get the student ID with improved fallback chain\n            const getStudentId = {\n                \"useInteractionLogger.useCallback[logInteraction].getStudentId\": ()=>{\n                    // Try multiple sources in order of preference\n                    const fromLocalStorage = localStorage.getItem('current_student_id');\n                    const fromAlternateStorage = localStorage.getItem('student_id');\n                    const fromSessionStorage = sessionStorage.getItem('student_id');\n                    const fromURL = new URLSearchParams(window.location.search).get('studentId') || new URLSearchParams(window.location.search).get('student_id');\n                    // Log all possible sources for debugging\n                    console.log('[useInteractionLogger] Student ID sources:', {\n                        fromLocalStorage,\n                        fromAlternateStorage,\n                        fromSessionStorage,\n                        fromURL\n                    });\n                    // Use the first valid ID found, in priority order\n                    return fromURL || fromLocalStorage || fromAlternateStorage || fromSessionStorage;\n                }\n            }[\"useInteractionLogger.useCallback[logInteraction].getStudentId\"];\n            const studentId = getStudentId();\n            if (!sessionId) {\n                console.warn('[useInteractionLogger] No session ID found, skipping interaction logging');\n                return;\n            }\n            // Log warning if no student ID, but continue anyway\n            if (!studentId) {\n                console.warn('[useInteractionLogger] No student ID found, interaction may fail');\n            } else {\n                console.log(\"[useInteractionLogger] Using student ID: \".concat(studentId));\n            }\n            // Double-check that current_student_id is set in localStorage\n            if (studentId && (!localStorage.getItem('current_student_id') || localStorage.getItem('current_student_id') !== studentId)) {\n                console.log(\"[useInteractionLogger] Updating current_student_id in localStorage to: \".concat(studentId));\n                localStorage.setItem('current_student_id', studentId);\n            }\n            // Implement rate limiting\n            const now = Date.now();\n            const sinceLastInteraction = now - lastInteractionRef.current;\n            if (sinceLastInteraction < RATE_LIMIT) {\n                console.log(\"Rate limiting interaction: \".concat(interactionType, \" (too soon after previous)\"));\n                // Queue this interaction for retry\n                pendingInteractions.push({\n                    type: interactionType,\n                    data: engagementData,\n                    sessionId,\n                    studentId,\n                    timestamp: new Date().toISOString()\n                });\n                // Schedule retry if not already scheduled\n                if (!isRetrying && pendingInteractions.length === 1) {\n                    setTimeout(retryPendingInteractions, RETRY_DELAY);\n                }\n                return;\n            }\n            // Update last interaction time\n            lastInteractionRef.current = now;\n            // Validate data to prevent 400 errors\n            const validatedData = engagementData && typeof engagementData === 'object' ? engagementData : {};\n            try {\n                // Skip the actual API call in development mode\n                if (true) {\n                    console.log('Development mode: Skipping interaction API call');\n                    console.log(\"Would log: \".concat(interactionType), validatedData);\n                    return;\n                }\n                // Add URL parameters to help with debugging\n                const url = new URL(\"\".concat(window.location.origin, \"/api/lesson-interaction\"));\n                if (studentId) {\n                    url.searchParams.append('studentId', studentId);\n                }\n                console.log(\"[useInteractionLogger] Sending interaction to: \".concat(url.toString()));\n                console.log(\"[useInteractionLogger] With payload:\", {\n                    sessionId,\n                    studentId,\n                    interactionType,\n                    data: validatedData\n                });\n                const response = await fetch(url.toString(), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Session-ID': sessionId,\n                        'X-Student-ID': studentId || '' // Add student ID to headers\n                    },\n                    body: JSON.stringify({\n                        sessionId,\n                        studentId,\n                        interactionType,\n                        data: validatedData,\n                        timestamp: new Date().toISOString()\n                    })\n                });\n                if (!response.ok) {\n                    const errorText = await response.text().catch({\n                        \"useInteractionLogger.useCallback[logInteraction]\": ()=>null\n                    }[\"useInteractionLogger.useCallback[logInteraction]\"]);\n                    console.warn(\"Error logging interaction: API returned \".concat(response.status), errorText);\n                    throw new Error(\"API returned \".concat(response.status, \": \").concat(errorText));\n                } else {\n                    console.log(\"[useInteractionLogger] Successfully logged interaction: \".concat(interactionType));\n                }\n            } catch (error) {\n                console.warn('Error logging interaction:', error);\n                // Add to the retry queue\n                pendingInteractions.push({\n                    type: interactionType,\n                    data: validatedData,\n                    sessionId,\n                    studentId,\n                    timestamp: new Date().toISOString()\n                });\n                // Schedule retry if not already scheduled\n                if (!isRetrying && pendingInteractions.length === 1) {\n                    setTimeout(retryPendingInteractions, RETRY_DELAY);\n                }\n                // Don't throw in development mode\n                if (true) {\n                    console.log('Development mode: Ignoring interaction logging error');\n                }\n            }\n        }\n    }[\"useInteractionLogger.useCallback[logInteraction]\"], []);\n    return logInteraction;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useInteractionLogger);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-interaction-logger.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useLessonTimer.ts":
/*!*************************************!*\
  !*** ./src/hooks/useLessonTimer.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLessonTimer: () => (/* binding */ useLessonTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _use_interaction_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-interaction-logger */ \"(app-pages-browser)/./src/hooks/use-interaction-logger.ts\");\n\n\n // Corrected import\nconst LESSON_DURATION_MINUTES = 45;\nconst QUIZ_TRANSITION_MINUTES = 37.5; // Force quiz transition at 37.5 minutes\nconst LESSON_PHASE_COMPLETED = \"completed\";\nconst useLessonTimer = (param)=>{\n    let { lessonStartTime, currentLessonPhase, sessionIdFromUrlProp, backendSessionId, lessonRef, onTimeUp, onQuizTransition } = param;\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(LESSON_DURATION_MINUTES * 60);\n    const [isTimerActive, setIsTimerActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [quizTransitionTriggered, setQuizTransitionTriggered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { toast } = (0,_app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const logInteraction = (0,_use_interaction_logger__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Format time as MM:SS\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLessonTimer.useCallback[formatTime]\": (seconds)=>{\n            const mins = Math.floor(seconds / 60);\n            const secs = seconds % 60;\n            return \"\".concat(mins.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n        }\n    }[\"useLessonTimer.useCallback[formatTime]\"], []);\n    // Update time remaining based on lesson start time\n    const updateTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLessonTimer.useCallback[updateTimeRemaining]\": ()=>{\n            if (!lessonStartTime) return;\n            const now = new Date();\n            const elapsedSeconds = Math.floor((now.getTime() - lessonStartTime.getTime()) / 1000);\n            const remaining = Math.max(0, LESSON_DURATION_MINUTES * 60 - elapsedSeconds);\n            setTimeRemaining(remaining);\n            // Check for forced quiz transition at 37.5 minutes (7.5 minutes remaining)\n            const quizTransitionTimeRemaining = QUIZ_TRANSITION_MINUTES * 60;\n            const timeElapsed = LESSON_DURATION_MINUTES * 60 - remaining;\n            if (timeElapsed >= quizTransitionTimeRemaining && !quizTransitionTriggered && onQuizTransition) {\n                setQuizTransitionTriggered(true);\n                // Log the forced quiz transition\n                if (backendSessionId && lessonRef) {\n                    logInteraction('forced_quiz_transition', {\n                        lessonRef,\n                        sessionId: backendSessionId,\n                        elapsedTime: timeElapsed,\n                        remainingTime: remaining,\n                        phase: currentLessonPhase || 'teaching',\n                        reason: 'time_limit_approaching'\n                    });\n                }\n                // Show transition notification\n                toast({\n                    title: \"Quiz Time!\",\n                    description: \"Moving to quiz phase to ensure you have enough time to complete it.\",\n                    variant: \"warning\",\n                    duration: 8000\n                });\n                // Trigger quiz transition callback\n                onQuizTransition();\n            }\n            // Auto-complete lesson when time is up\n            if (remaining <= 0 && currentLessonPhase !== LESSON_PHASE_COMPLETED && (sessionIdFromUrlProp || backendSessionId)) {\n                setIsTimerActive(false);\n                // Log time up event\n                if (backendSessionId && lessonRef) {\n                    logInteraction('lesson_time_up', {\n                        lessonRef,\n                        sessionId: backendSessionId,\n                        phase: currentLessonPhase || 'unknown',\n                        elapsedSeconds: elapsedSeconds,\n                        quizTransitionTriggered: quizTransitionTriggered\n                    });\n                }\n                // Call the onTimeUp callback\n                onTimeUp();\n            }\n        }\n    }[\"useLessonTimer.useCallback[updateTimeRemaining]\"], [\n        lessonStartTime,\n        currentLessonPhase,\n        sessionIdFromUrlProp,\n        backendSessionId,\n        lessonRef,\n        logInteraction,\n        onTimeUp,\n        onQuizTransition,\n        quizTransitionTriggered\n    ]);\n    // Timer effect\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLessonTimer.useEffect\": ()=>{\n            let interval = null;\n            if (isTimerActive && lessonStartTime) {\n                updateTimeRemaining(); // Initial update\n                interval = setInterval(updateTimeRemaining, 1000); // Update every second\n                // Show warning at 5 minutes remaining (and other notifications)\n                const checkTimeWarnings = {\n                    \"useLessonTimer.useEffect.checkTimeWarnings\": ()=>{\n                        if (timeRemaining <= 300 && timeRemaining > 295) {\n                            toast({\n                                title: \"Time Alert\",\n                                description: \"5 minutes remaining in the lesson!\",\n                                variant: \"warning\",\n                                duration: 5000\n                            });\n                        }\n                        // Show quiz transition warning at 10 minutes remaining (2.5 minutes before forced transition)\n                        const quizTransitionWarningTime = (LESSON_DURATION_MINUTES - QUIZ_TRANSITION_MINUTES + 2.5) * 60;\n                        if (timeRemaining <= quizTransitionWarningTime && timeRemaining > quizTransitionWarningTime - 5) {\n                            toast({\n                                title: \"Quiz Phase Approaching\",\n                                description: \"The lesson will transition to quiz phase in 2.5 minutes to ensure adequate completion time.\",\n                                variant: \"default\",\n                                duration: 6000\n                            });\n                        }\n                    }\n                }[\"useLessonTimer.useEffect.checkTimeWarnings\"];\n                checkTimeWarnings();\n            }\n            return ({\n                \"useLessonTimer.useEffect\": ()=>{\n                    if (interval) clearInterval(interval);\n                }\n            })[\"useLessonTimer.useEffect\"];\n        }\n    }[\"useLessonTimer.useEffect\"], [\n        isTimerActive,\n        lessonStartTime,\n        timeRemaining,\n        updateTimeRemaining,\n        toast\n    ]);\n    // Start timer when the lesson begins\n    const startTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLessonTimer.useCallback[startTimer]\": ()=>{\n            if (!lessonStartTime) {\n                const startTime = new Date();\n                // Log lesson start with time tracking\n                if (backendSessionId && lessonRef) {\n                    logInteraction('lesson_timer_started', {\n                        lessonRef,\n                        sessionId: backendSessionId,\n                        startTime: startTime.toISOString(),\n                        expectedDuration: LESSON_DURATION_MINUTES * 60,\n                        quizTransitionAt: QUIZ_TRANSITION_MINUTES * 60,\n                        phase: currentLessonPhase || 'initializing'\n                    });\n                }\n                // Show welcome message with time info\n                toast({\n                    title: \"Lesson Started\",\n                    description: \"You have \".concat(LESSON_DURATION_MINUTES, \" minutes total. Quiz phase will begin at \").concat(QUIZ_TRANSITION_MINUTES, \" minutes if needed.\"),\n                    duration: 6000\n                });\n                return startTime;\n            }\n            return null;\n        }\n    }[\"useLessonTimer.useCallback[startTimer]\"], [\n        backendSessionId,\n        currentLessonPhase,\n        lessonRef,\n        lessonStartTime,\n        logInteraction,\n        toast\n    ]);\n    // Helper function to get time-based progress indicators\n    const getTimerStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useLessonTimer.useCallback[getTimerStatus]\": ()=>{\n            const elapsedTime = LESSON_DURATION_MINUTES * 60 - timeRemaining;\n            const quizTransitionTime = QUIZ_TRANSITION_MINUTES * 60;\n            return {\n                isInQuizPhase: elapsedTime >= quizTransitionTime,\n                timeUntilQuizTransition: Math.max(0, quizTransitionTime - elapsedTime),\n                progressPercentage: elapsedTime / (LESSON_DURATION_MINUTES * 60) * 100,\n                quizTransitionTriggered\n            };\n        }\n    }[\"useLessonTimer.useCallback[getTimerStatus]\"], [\n        timeRemaining,\n        quizTransitionTriggered\n    ]);\n    return {\n        timeRemaining,\n        isTimerActive,\n        setIsTimerActive,\n        formatTime,\n        startTimer,\n        getTimerStatus\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useLessonTimer.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclassroom%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);