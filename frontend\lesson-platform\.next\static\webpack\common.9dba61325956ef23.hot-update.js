"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("common",{

/***/ "(app-pages-browser)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(app-pages-browser)/./node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// lib/firebase.ts\n/* __next_internal_client_entry_do_not_use__ app,auth,db,storage auto */ \n\n\n\n// Default Firebase configuration for development\nconst devConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n    authDomain: \"solynta-academy.firebaseapp.com\",\n    projectId: \"solynta-academy\",\n    storageBucket: \"solynta-academy.firebasestorage.app\",\n    messagingSenderId: \"914922463191\",\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\",\n    measurementId: \"G-ZVC7R06Y33\"\n};\n// Firebase configuration - try environment variables first, then fallback to dev config\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\" || 0,\n    authDomain: \"solynta-academy.firebaseapp.com\" || 0,\n    projectId: \"solynta-academy\" || 0,\n    storageBucket: \"solynta-academy.firebasestorage.app\" || 0,\n    messagingSenderId: \"914922463191\" || 0,\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\" || 0,\n    measurementId: \"G-ZVC7R06Y33\" || 0\n};\nconsole.log('Using Firebase config with project ID:', firebaseConfig.projectId);\n// Initialize Firebase app (Singleton pattern)\nconst app = !(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)();\n// Initialize services - these will be initialized client-side\nlet auth;\nlet db;\nlet storage;\n// Check if running in a browser environment\nif (true) {\n    // Initialize Auth\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.setPersistence)(auth, firebase_auth__WEBPACK_IMPORTED_MODULE_1__.browserLocalPersistence).catch((error)=>console.error(\"Auth persistence error:\", error));\n    // Initialize Firestore\n    // Note: getFirestore() could also be used if default settings are acceptable\n    db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.initializeFirestore)(app, {\n        cacheSizeBytes: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.CACHE_SIZE_UNLIMITED,\n        experimentalForceLongPolling: true,\n        ignoreUndefinedProperties: true\n    });\n    // Initialize Storage\n    storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n    // Connect to emulators in development if configured\n    if ( true && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {\n        console.log(\"Connecting to Firebase Emulators...\");\n        // Use dynamic imports for emulator functions to potentially reduce bundle size\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectAuthEmulator } = param;\n            try {\n                connectAuthEmulator(auth, 'http://localhost:9099', {\n                    disableWarnings: true\n                });\n                console.log(\"Auth Emulator connected to http://localhost:9099\");\n            } catch (error) {\n                console.error(\"Error connecting to Auth Emulator:\", error);\n            }\n        });\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectFirestoreEmulator } = param;\n            try {\n                connectFirestoreEmulator(db, 'localhost', 8080);\n                console.log(\"Firestore Emulator connected to localhost:8080\");\n            } catch (error) {\n                console.error(\"Error connecting to Firestore Emulator:\", error);\n            }\n        });\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectStorageEmulator } = param;\n            try {\n                connectStorageEmulator(storage, 'localhost', 9199);\n                console.log(\"Storage Emulator connected to localhost:9199\");\n            } catch (error) {\n                console.error(\"Error connecting to Storage Emulator:\", error);\n            }\n        });\n    }\n} else {}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvZmlyZWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsa0JBQWtCO3lFQUd5RDtBQU1wRDtBQUtLO0FBSUY7QUFFMUIsaURBQWlEO0FBQ2pELE1BQU1TLFlBQVk7SUFDaEJDLFFBQVE7SUFDUkMsWUFBWTtJQUNaQyxXQUFXO0lBQ1hDLGVBQWU7SUFDZkMsbUJBQW1CO0lBQ25CQyxPQUFPO0lBQ1BDLGVBQWU7QUFDakI7QUFFQSx3RkFBd0Y7QUFDeEYsTUFBTUMsaUJBQWlCO0lBQ3JCUCxRQUFRUSx5Q0FBd0MsSUFBSVQsQ0FBZ0I7SUFDcEVFLFlBQVlPLGlDQUE0QyxJQUFJVCxDQUFvQjtJQUNoRkcsV0FBV00saUJBQTJDLElBQUlULENBQW1CO0lBQzdFSSxlQUFlSyxxQ0FBK0MsSUFBSVQsQ0FBdUI7SUFDekZLLG1CQUFtQkksY0FBb0QsSUFBSVQsQ0FBMkI7SUFDdEdNLE9BQU9HLDJDQUF1QyxJQUFJVCxDQUFlO0lBQ2pFTyxlQUFlRSxjQUErQyxJQUFJVCxDQUF1QjtBQUMzRjtBQUVBa0IsUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ1gsZUFBZUwsU0FBUztBQUU5RSw4Q0FBOEM7QUFDOUMsTUFBTWlCLE1BQW1CLENBQUM1QixxREFBT0EsR0FBRzZCLE1BQU0sR0FBRzlCLDJEQUFhQSxDQUFDaUIsa0JBQWtCZixvREFBTUE7QUFFbkYsOERBQThEO0FBQzlELElBQUk2QjtBQUNKLElBQUlDO0FBQ0osSUFBSUM7QUFFSiw0Q0FBNEM7QUFDNUMsSUFBSSxJQUE2QixFQUFFO0lBQ2pDLGtCQUFrQjtJQUNsQkYsT0FBTzVCLHNEQUFPQSxDQUFDMEI7SUFDZnhCLDZEQUFjQSxDQUFDMEIsTUFBTTNCLGtFQUF1QkEsRUFDekM4QixLQUFLLENBQUMsQ0FBQ0MsUUFBVVIsUUFBUVEsS0FBSyxDQUFDLDJCQUEyQkE7SUFFN0QsdUJBQXVCO0lBQ3ZCLDZFQUE2RTtJQUM3RUgsS0FBSzFCLHVFQUFtQkEsQ0FBQ3VCLEtBQUs7UUFDNUJPLGdCQUFnQjdCLG9FQUFvQkE7UUFDcEM4Qiw4QkFBOEI7UUFDOUJDLDJCQUEyQjtJQUM3QjtJQUVBLHFCQUFxQjtJQUNyQkwsVUFBVXpCLDREQUFVQSxDQUFDcUI7SUFFckIsb0RBQW9EO0lBQ3BELElBQUlYLEtBQXNDLElBQ3RDQSxPQUFPQSxDQUFDQyxHQUFHLENBQUNvQixpQ0FBaUMsS0FBSyxRQUFRO1FBQzVEWixRQUFRQyxHQUFHLENBQUM7UUFDWiwrRUFBK0U7UUFDL0UsbUxBQXVCLENBQUNZLElBQUksQ0FBQztnQkFBQyxFQUFFQyxtQkFBbUIsRUFBRTtZQUNuRCxJQUFJO2dCQUNGQSxvQkFBb0JWLE1BQU0seUJBQXlCO29CQUFFVyxpQkFBaUI7Z0JBQUs7Z0JBQzNFZixRQUFRQyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9PLE9BQU87Z0JBQ2RSLFFBQVFRLEtBQUssQ0FBQyxzQ0FBc0NBO1lBQ3REO1FBQ0Y7UUFFQSw2TEFBNEIsQ0FBQ0ssSUFBSSxDQUFDO2dCQUFDLEVBQUVHLHdCQUF3QixFQUFFO1lBQzdELElBQUk7Z0JBQ0ZBLHlCQUF5QlgsSUFBSSxhQUFhO2dCQUMxQ0wsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPTyxPQUFPO2dCQUNkUixRQUFRUSxLQUFLLENBQUMsMkNBQTJDQTtZQUMzRDtRQUNGO1FBRUEseUxBQTBCLENBQUNLLElBQUksQ0FBQztnQkFBQyxFQUFFSSxzQkFBc0IsRUFBRTtZQUN6RCxJQUFJO2dCQUNGQSx1QkFBdUJYLFNBQVMsYUFBYTtnQkFDN0NOLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEVBQUUsT0FBT08sT0FBTztnQkFDZFIsUUFBUVEsS0FBSyxDQUFDLHlDQUF5Q0E7WUFDekQ7UUFDRjtJQUNGO0FBQ0YsT0FBTyxFQVNOO0FBRWlDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGxpYlxcZmlyZWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gbGliL2ZpcmViYXNlLnRzXHJcbid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IGluaXRpYWxpemVBcHAsIGdldEFwcHMsIGdldEFwcCwgRmlyZWJhc2VBcHAgfSBmcm9tICdmaXJlYmFzZS9hcHAnO1xyXG5pbXBvcnQge1xyXG4gIGdldEF1dGgsXHJcbiAgYnJvd3NlckxvY2FsUGVyc2lzdGVuY2UsXHJcbiAgc2V0UGVyc2lzdGVuY2UsXHJcbiAgQXV0aCAvLyBJbXBvcnQgQXV0aCB0eXBlXHJcbn0gZnJvbSAnZmlyZWJhc2UvYXV0aCc7XHJcbmltcG9ydCB7XHJcbiAgaW5pdGlhbGl6ZUZpcmVzdG9yZSxcclxuICBDQUNIRV9TSVpFX1VOTElNSVRFRCxcclxuICBGaXJlc3RvcmUgLy8gSW1wb3J0IEZpcmVzdG9yZSB0eXBlXHJcbn0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJztcclxuaW1wb3J0IHtcclxuICBnZXRTdG9yYWdlLFxyXG4gIEZpcmViYXNlU3RvcmFnZSAvLyBJbXBvcnQgRmlyZWJhc2VTdG9yYWdlIHR5cGVcclxufSBmcm9tICdmaXJlYmFzZS9zdG9yYWdlJztcclxuXHJcbi8vIERlZmF1bHQgRmlyZWJhc2UgY29uZmlndXJhdGlvbiBmb3IgZGV2ZWxvcG1lbnRcclxuY29uc3QgZGV2Q29uZmlnID0ge1xyXG4gIGFwaUtleTogXCJBSXphU3lEV1ZNOFB2Y1dENG5Ba3BzSTdGdURLQ3ZwcF9QRW5QbFVcIixcclxuICBhdXRoRG9tYWluOiBcInNvbHludGEtYWNhZGVteS5maXJlYmFzZWFwcC5jb21cIixcclxuICBwcm9qZWN0SWQ6IFwic29seW50YS1hY2FkZW15XCIsXHJcbiAgc3RvcmFnZUJ1Y2tldDogXCJzb2x5bnRhLWFjYWRlbXkuZmlyZWJhc2VzdG9yYWdlLmFwcFwiLFxyXG4gIG1lc3NhZ2luZ1NlbmRlcklkOiBcIjkxNDkyMjQ2MzE5MVwiLFxyXG4gIGFwcElkOiBcIjE6OTE0OTIyNDYzMTkxOndlYjpiNmU5YzczN2RiYTc3YTI2NjQzNTkyXCIsXHJcbiAgbWVhc3VyZW1lbnRJZDogXCJHLVpWQzdSMDZZMzNcIlxyXG59O1xyXG5cclxuLy8gRmlyZWJhc2UgY29uZmlndXJhdGlvbiAtIHRyeSBlbnZpcm9ubWVudCB2YXJpYWJsZXMgZmlyc3QsIHRoZW4gZmFsbGJhY2sgdG8gZGV2IGNvbmZpZ1xyXG5jb25zdCBmaXJlYmFzZUNvbmZpZyA9IHtcclxuICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FQSV9LRVkgfHwgZGV2Q29uZmlnLmFwaUtleSxcclxuICBhdXRoRG9tYWluOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9BVVRIX0RPTUFJTiB8fCBkZXZDb25maWcuYXV0aERvbWFpbixcclxuICBwcm9qZWN0SWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX1BST0pFQ1RfSUQgfHwgZGV2Q29uZmlnLnByb2plY3RJZCxcclxuICBzdG9yYWdlQnVja2V0OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GSVJFQkFTRV9TVE9SQUdFX0JVQ0tFVCB8fCBkZXZDb25maWcuc3RvcmFnZUJ1Y2tldCxcclxuICBtZXNzYWdpbmdTZW5kZXJJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVTU0FHSU5HX1NFTkRFUl9JRCB8fCBkZXZDb25maWcubWVzc2FnaW5nU2VuZGVySWQsXHJcbiAgYXBwSWQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FQUF9JRCB8fCBkZXZDb25maWcuYXBwSWQsXHJcbiAgbWVhc3VyZW1lbnRJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVBU1VSRU1FTlRfSUQgfHwgZGV2Q29uZmlnLm1lYXN1cmVtZW50SWRcclxufTtcclxuXHJcbmNvbnNvbGUubG9nKCdVc2luZyBGaXJlYmFzZSBjb25maWcgd2l0aCBwcm9qZWN0IElEOicsIGZpcmViYXNlQ29uZmlnLnByb2plY3RJZCk7XHJcblxyXG4vLyBJbml0aWFsaXplIEZpcmViYXNlIGFwcCAoU2luZ2xldG9uIHBhdHRlcm4pXHJcbmNvbnN0IGFwcDogRmlyZWJhc2VBcHAgPSAhZ2V0QXBwcygpLmxlbmd0aCA/IGluaXRpYWxpemVBcHAoZmlyZWJhc2VDb25maWcpIDogZ2V0QXBwKCk7XHJcblxyXG4vLyBJbml0aWFsaXplIHNlcnZpY2VzIC0gdGhlc2Ugd2lsbCBiZSBpbml0aWFsaXplZCBjbGllbnQtc2lkZVxyXG5sZXQgYXV0aDogQXV0aDtcclxubGV0IGRiOiBGaXJlc3RvcmU7XHJcbmxldCBzdG9yYWdlOiBGaXJlYmFzZVN0b3JhZ2U7XHJcblxyXG4vLyBDaGVjayBpZiBydW5uaW5nIGluIGEgYnJvd3NlciBlbnZpcm9ubWVudFxyXG5pZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAvLyBJbml0aWFsaXplIEF1dGhcclxuICBhdXRoID0gZ2V0QXV0aChhcHApO1xyXG4gIHNldFBlcnNpc3RlbmNlKGF1dGgsIGJyb3dzZXJMb2NhbFBlcnNpc3RlbmNlKVxyXG4gICAgLmNhdGNoKChlcnJvcikgPT4gY29uc29sZS5lcnJvcihcIkF1dGggcGVyc2lzdGVuY2UgZXJyb3I6XCIsIGVycm9yKSk7XHJcblxyXG4gIC8vIEluaXRpYWxpemUgRmlyZXN0b3JlXHJcbiAgLy8gTm90ZTogZ2V0RmlyZXN0b3JlKCkgY291bGQgYWxzbyBiZSB1c2VkIGlmIGRlZmF1bHQgc2V0dGluZ3MgYXJlIGFjY2VwdGFibGVcclxuICBkYiA9IGluaXRpYWxpemVGaXJlc3RvcmUoYXBwLCB7XHJcbiAgICBjYWNoZVNpemVCeXRlczogQ0FDSEVfU0laRV9VTkxJTUlURUQsXHJcbiAgICBleHBlcmltZW50YWxGb3JjZUxvbmdQb2xsaW5nOiB0cnVlLCAvLyBDb25zaWRlciBpZiBzdGlsbCBuZWVkZWQsIG1pZ2h0IGltcGFjdCBwZXJmb3JtYW5jZS9jb3N0XHJcbiAgICBpZ25vcmVVbmRlZmluZWRQcm9wZXJ0aWVzOiB0cnVlLCAvLyBVc2VmdWwgZm9yIGF2b2lkaW5nIGVycm9ycyB3aXRoIHVuZGVmaW5lZCB2YWx1ZXNcclxuICB9KTtcclxuXHJcbiAgLy8gSW5pdGlhbGl6ZSBTdG9yYWdlXHJcbiAgc3RvcmFnZSA9IGdldFN0b3JhZ2UoYXBwKTtcclxuXHJcbiAgLy8gQ29ubmVjdCB0byBlbXVsYXRvcnMgaW4gZGV2ZWxvcG1lbnQgaWYgY29uZmlndXJlZFxyXG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJlxyXG4gICAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19VU0VfRklSRUJBU0VfRU1VTEFUT1IgPT09ICd0cnVlJykge1xyXG4gICAgY29uc29sZS5sb2coXCJDb25uZWN0aW5nIHRvIEZpcmViYXNlIEVtdWxhdG9ycy4uLlwiKTtcclxuICAgIC8vIFVzZSBkeW5hbWljIGltcG9ydHMgZm9yIGVtdWxhdG9yIGZ1bmN0aW9ucyB0byBwb3RlbnRpYWxseSByZWR1Y2UgYnVuZGxlIHNpemVcclxuICAgIGltcG9ydCgnZmlyZWJhc2UvYXV0aCcpLnRoZW4oKHsgY29ubmVjdEF1dGhFbXVsYXRvciB9KSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29ubmVjdEF1dGhFbXVsYXRvcihhdXRoLCAnaHR0cDovL2xvY2FsaG9zdDo5MDk5JywgeyBkaXNhYmxlV2FybmluZ3M6IHRydWUgfSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJBdXRoIEVtdWxhdG9yIGNvbm5lY3RlZCB0byBodHRwOi8vbG9jYWxob3N0OjkwOTlcIik7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNvbm5lY3RpbmcgdG8gQXV0aCBFbXVsYXRvcjpcIiwgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICBpbXBvcnQoJ2ZpcmViYXNlL2ZpcmVzdG9yZScpLnRoZW4oKHsgY29ubmVjdEZpcmVzdG9yZUVtdWxhdG9yIH0pID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25uZWN0RmlyZXN0b3JlRW11bGF0b3IoZGIsICdsb2NhbGhvc3QnLCA4MDgwKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIkZpcmVzdG9yZSBFbXVsYXRvciBjb25uZWN0ZWQgdG8gbG9jYWxob3N0OjgwODBcIik7XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNvbm5lY3RpbmcgdG8gRmlyZXN0b3JlIEVtdWxhdG9yOlwiLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG5cclxuICAgIGltcG9ydCgnZmlyZWJhc2Uvc3RvcmFnZScpLnRoZW4oKHsgY29ubmVjdFN0b3JhZ2VFbXVsYXRvciB9KSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29ubmVjdFN0b3JhZ2VFbXVsYXRvcihzdG9yYWdlLCAnbG9jYWxob3N0JywgOTE5OSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJTdG9yYWdlIEVtdWxhdG9yIGNvbm5lY3RlZCB0byBsb2NhbGhvc3Q6OTE5OVwiKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgY29ubmVjdGluZyB0byBTdG9yYWdlIEVtdWxhdG9yOlwiLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH0pO1xyXG4gIH1cclxufSBlbHNlIHtcclxuICAvLyBQcm92aWRlIG5vbi1mdW5jdGlvbmFsIHBsYWNlaG9sZGVycyBmb3IgU1NSL3NlcnZlciBlbnZpcm9ubWVudHNcclxuICAvLyBUaGlzIHByZXZlbnRzIGVycm9ycyBkdXJpbmcgaW1wb3J0IGJ1dCB0aGVzZSBzZXJ2aWNlcyB3b24ndCB3b3JrIHNlcnZlci1zaWRlXHJcbiAgLy8gQXNzaWduaW5nIHt9IGFzIFR5cGUgbWlnaHQgY2F1c2UgaXNzdWVzIGlmIG1ldGhvZHMgYXJlIGNhbGxlZCBzZXJ2ZXItc2lkZS5cclxuICAvLyBBIG1vcmUgcm9idXN0IGFwcHJvYWNoIG1pZ2h0IGludm9sdmUgcHJvdmlkaW5nIG1vY2sgaW1wbGVtZW50YXRpb25zIG9yXHJcbiAgLy8gZW5zdXJpbmcgdGhlc2UgZXhwb3J0cyBhcmUgb25seSB1c2VkIGluIGNsaWVudCBjb21wb25lbnRzLlxyXG4gIGF1dGggPSB7fSBhcyBBdXRoO1xyXG4gIGRiID0ge30gYXMgRmlyZXN0b3JlO1xyXG4gIHN0b3JhZ2UgPSB7fSBhcyBGaXJlYmFzZVN0b3JhZ2U7XHJcbn1cclxuXHJcbmV4cG9ydCB7IGFwcCwgYXV0aCwgZGIsIHN0b3JhZ2UgfTsiXSwibmFtZXMiOlsiaW5pdGlhbGl6ZUFwcCIsImdldEFwcHMiLCJnZXRBcHAiLCJnZXRBdXRoIiwiYnJvd3NlckxvY2FsUGVyc2lzdGVuY2UiLCJzZXRQZXJzaXN0ZW5jZSIsImluaXRpYWxpemVGaXJlc3RvcmUiLCJDQUNIRV9TSVpFX1VOTElNSVRFRCIsImdldFN0b3JhZ2UiLCJkZXZDb25maWciLCJhcGlLZXkiLCJhdXRoRG9tYWluIiwicHJvamVjdElkIiwic3RvcmFnZUJ1Y2tldCIsIm1lc3NhZ2luZ1NlbmRlcklkIiwiYXBwSWQiLCJtZWFzdXJlbWVudElkIiwiZmlyZWJhc2VDb25maWciLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfQVBJX0tFWSIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FVVEhfRE9NQUlOIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfUFJPSkVDVF9JRCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX1NUT1JBR0VfQlVDS0VUIiwiTkVYVF9QVUJMSUNfRklSRUJBU0VfTUVTU0FHSU5HX1NFTkRFUl9JRCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX0FQUF9JRCIsIk5FWFRfUFVCTElDX0ZJUkVCQVNFX01FQVNVUkVNRU5UX0lEIiwiY29uc29sZSIsImxvZyIsImFwcCIsImxlbmd0aCIsImF1dGgiLCJkYiIsInN0b3JhZ2UiLCJjYXRjaCIsImVycm9yIiwiY2FjaGVTaXplQnl0ZXMiLCJleHBlcmltZW50YWxGb3JjZUxvbmdQb2xsaW5nIiwiaWdub3JlVW5kZWZpbmVkUHJvcGVydGllcyIsIk5FWFRfUFVCTElDX1VTRV9GSVJFQkFTRV9FTVVMQVRPUiIsInRoZW4iLCJjb25uZWN0QXV0aEVtdWxhdG9yIiwiZGlzYWJsZVdhcm5pbmdzIiwiY29ubmVjdEZpcmVzdG9yZUVtdWxhdG9yIiwiY29ubmVjdFN0b3JhZ2VFbXVsYXRvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase.ts\n"));

/***/ })

});