#!/usr/bin/env python3
"""
Comprehensive test to validate strict forward-only progression through the mandatory 8-phase diagnostic sequence.

MANDATORY DIAGNOSTIC SEQUENCE:
1. diagnostic_start_probe → 
2. diagnostic_probing_L{level}_ask_q1 → 
3. diagnostic_probing_L{level}_eval_q1_ask_q2 → 
4. diagnostic_probing_L{level}_eval_q2_ask_q3 → 
5. diagnostic_probing_L{level}_eval_q3_ask_q4 → 
6. diagnostic_probing_L{level}_eval_q4_ask_q5 → 
7. diagnostic_probing_L{level}_eval_q5_decide_level → 
8. teaching_start_level_{assigned_level}
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mandatory_8_phase_sequence():
    """Test the mandatory 8-phase diagnostic sequence with strict forward-only progression"""
    print("Testing Mandatory 8-Phase Diagnostic Sequence")
    print("=" * 60)
    
    # Import the helper functions
    try:
        from main import calculate_next_mandatory_phase, validate_diagnostic_phase_sequence
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Define the mandatory 8-phase sequence
    mandatory_sequence = [
        'diagnostic_start_probe',
        'diagnostic_probing_L5_ask_q1',
        'diagnostic_probing_L5_eval_q1_ask_q2',
        'diagnostic_probing_L5_eval_q2_ask_q3',
        'diagnostic_probing_L5_eval_q3_ask_q4',
        'diagnostic_probing_L5_eval_q4_ask_q5',
        'diagnostic_probing_L5_eval_q5_decide_level',
        'teaching_start_level_5'
    ]
    
    print(f"Testing sequence: {' → '.join(mandatory_sequence)}")
    print()
    
    all_tests_passed = True
    context = {'diagnostic_phase_history': []}
    
    # Test 1: Forward progression through all phases
    print("Test 1: Forward Progression Through All 8 Phases")
    print("-" * 50)
    
    for i in range(len(mandatory_sequence) - 1):
        current_phase = mandatory_sequence[i]
        expected_next_phase = mandatory_sequence[i + 1]
        
        # Calculate next phase using our function
        calculated_next_phase = calculate_next_mandatory_phase(current_phase, 'test_request')
        
        print(f"Phase {i+1}: {current_phase}")
        print(f"  Expected next: {expected_next_phase}")
        print(f"  Calculated next: {calculated_next_phase}")
        
        if calculated_next_phase == expected_next_phase:
            print("  ✅ PASS")
        else:
            print("  ❌ FAIL")
            all_tests_passed = False
        print()
    
    # Test 2: Backward transition prevention
    print("Test 2: Backward Transition Prevention")
    print("-" * 50)
    
    # Simulate being in phase 4 and trying to go back to phase 2
    current_phase = 'diagnostic_probing_L5_eval_q2_ask_q3'
    attempted_backward_phase = 'diagnostic_probing_L5_eval_q1_ask_q2'
    
    # Add phases to history to simulate progression
    test_context = {
        'diagnostic_phase_history': [
            'diagnostic_start_probe',
            'diagnostic_probing_L5_ask_q1',
            'diagnostic_probing_L5_eval_q1_ask_q2',
            'diagnostic_probing_L5_eval_q2_ask_q3'
        ]
    }
    
    is_valid, corrected_phase, message = validate_diagnostic_phase_sequence(
        current_phase, attempted_backward_phase, 'test_request', test_context
    )
    
    print(f"Current phase: {current_phase}")
    print(f"Attempted backward transition: {attempted_backward_phase}")
    print(f"Validation result: {'BLOCKED' if not is_valid else 'ALLOWED'}")
    print(f"Corrected phase: {corrected_phase}")
    print(f"Message: {message}")
    
    if not is_valid and 'backward' in message.lower():
        print("✅ PASS - Backward transition correctly blocked")
    else:
        print("❌ FAIL - Backward transition not blocked")
        all_tests_passed = False
    print()
    
    # Test 3: Phase skipping prevention
    print("Test 3: Phase Skipping Prevention")
    print("-" * 50)
    
    current_phase = 'diagnostic_probing_L5_ask_q1'
    attempted_skip_phase = 'diagnostic_probing_L5_eval_q2_ask_q3'  # Skipping eval_q1_ask_q2
    
    test_context = {'diagnostic_phase_history': ['diagnostic_start_probe', 'diagnostic_probing_L5_ask_q1']}
    
    is_valid, corrected_phase, message = validate_diagnostic_phase_sequence(
        current_phase, attempted_skip_phase, 'test_request', test_context
    )
    
    print(f"Current phase: {current_phase}")
    print(f"Attempted skip: {attempted_skip_phase}")
    print(f"Validation result: {'BLOCKED' if not is_valid else 'ALLOWED'}")
    print(f"Corrected phase: {corrected_phase}")
    
    expected_next = 'diagnostic_probing_L5_eval_q1_ask_q2'
    if corrected_phase == expected_next:
        print("✅ PASS - Phase skipping prevented, correct next phase enforced")
    else:
        print("❌ FAIL - Phase skipping not properly prevented")
        all_tests_passed = False
    print()
    
    # Test 4: Question progression integrity (Q1→Q2→Q3→Q4→Q5)
    print("Test 4: Question Progression Integrity")
    print("-" * 50)
    
    question_phases = [
        'diagnostic_probing_L5_ask_q1',
        'diagnostic_probing_L5_eval_q1_ask_q2',
        'diagnostic_probing_L5_eval_q2_ask_q3',
        'diagnostic_probing_L5_eval_q3_ask_q4',
        'diagnostic_probing_L5_eval_q4_ask_q5',
        'diagnostic_probing_L5_eval_q5_decide_level'
    ]
    
    question_progression_correct = True
    for i in range(len(question_phases) - 1):
        current = question_phases[i]
        expected_next = question_phases[i + 1]
        calculated_next = calculate_next_mandatory_phase(current, 'test_request')
        
        print(f"Q{i+1} phase: {current}")
        print(f"  Next: {calculated_next} {'✅' if calculated_next == expected_next else '❌'}")
        
        if calculated_next != expected_next:
            question_progression_correct = False
    
    if question_progression_correct:
        print("✅ PASS - All 5 questions progress correctly")
    else:
        print("❌ FAIL - Question progression has errors")
        all_tests_passed = False
    print()
    
    # Test 5: Teaching transition validation
    print("Test 5: Teaching Transition Validation")
    print("-" * 50)
    
    # Valid transition from eval_q5_decide_level
    valid_current = 'diagnostic_probing_L5_eval_q5_decide_level'
    valid_next = 'teaching_start_level_5'
    
    calculated_teaching = calculate_next_mandatory_phase(valid_current, 'test_request')
    print(f"From final diagnostic phase: {valid_current}")
    print(f"Expected teaching transition: {valid_next}")
    print(f"Calculated transition: {calculated_teaching}")
    
    if calculated_teaching == valid_next:
        print("✅ PASS - Teaching transition correct")
    else:
        print("❌ FAIL - Teaching transition incorrect")
        all_tests_passed = False
    print()
    
    # Summary
    print("=" * 60)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - Strict 8-Phase Sequence Enforcement Working!")
        print("✅ Forward-only progression enforced")
        print("✅ Backward transitions blocked")
        print("✅ Phase skipping prevented")
        print("✅ Question progression integrity maintained")
        print("✅ Teaching transition validation working")
        return True
    else:
        print("💥 SOME TESTS FAILED - Sequence enforcement needs fixes")
        return False

def main():
    """Main test function"""
    print("Strict 8-Phase Diagnostic Sequence Enforcement Test")
    print("=" * 60)
    
    success = test_mandatory_8_phase_sequence()
    
    if success:
        print("\n✅ Strict 8-phase sequence enforcement validation PASSED")
        return True
    else:
        print("\n❌ Strict 8-phase sequence enforcement validation FAILED")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
